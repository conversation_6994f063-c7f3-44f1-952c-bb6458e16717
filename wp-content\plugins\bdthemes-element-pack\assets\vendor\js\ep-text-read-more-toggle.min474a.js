!function(e,t){"use strict";e(window).on("elementor/frontend/init",(function(){const t=function(t){jQuery(t).find(".bdt-ep-read-more-text").length&&jQuery(t).find(".bdt-ep-read-more-text").each((function(){var t=e(this).data("read-more").words_length||20,i=e(this).html().replace(/<\/?[^>]+(>|$)/g,"").split(/\s+/);if(i.length>t){var d=i.slice(0,t).join(" "),n=i.slice(t).join(" ");e(this).html(`\n                          ${d}\n                          <a href="#" class="bdt_read_more">...${ElementPackConfig.words_limit.read_more}</a>\n                          <span class="bdt_more_text" style="display:none;">${n}</span>\n                          <a href="#" class="bdt_read_less" style="display:none;">${ElementPackConfig.words_limit.read_less}</a>\n                      `),e(this).find("a.bdt_read_more").on("click",(function(t){t.preventDefault(),e(this).hide(),e(this).siblings(".bdt_more_text").show(),e(this).siblings("a.bdt_read_less").show()})),e(this).find("a.bdt_read_less").click((function(t){t.preventDefault(),e(this).hide(),e(this).siblings(".bdt_more_text").hide(),e(this).siblings("a.bdt_read_more").show()}))}}))},i={"bdt-review-card.default":t,"bdt-review-card-carousel.default":t,"bdt-review-card-grid.default":t,"bdt-testimonial-carousel.default":t,"bdt-testimonial-carousel.bdt-twyla":t,"bdt-testimonial-carousel.bdt-vyxo":t,"bdt-testimonial-grid.default":t,"bdt-testimonial-slider.default":t,"bdt-testimonial-slider.bdt-single":t,"bdt-testimonial-slider.bdt-thumb":t};e.each(i,(function(e,t){elementorFrontend.hooks.addAction("frontend/element_ready/"+e,t)}))}))}(jQuery,window.elementorFrontend);