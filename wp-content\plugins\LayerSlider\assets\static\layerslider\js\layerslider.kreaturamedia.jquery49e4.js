
/*
	* LayerSlider
	*
	* (c) 2011-2025 <PERSON>, <PERSON> & Kreatura Media
	*
	* LayerSlider home:		https://layerslider.com/
	* Licensing:			https://layerslider.com/licensing/
*/

!function(ie){"use strict";window._layerSliders={},window._lsData={$overflowWrapper:ie("body").length?ie("body"):ie("html"),isMobile:!!navigator.userAgent.match(/(iPhone|iPod|iPad|Android|BlackBerry|BB10|webOS|Windows Phone|IEMobile|mobi|opera mini|nexus 7)/i)||navigator.maxTouchPoints&&2<navigator.maxTouchPoints&&/Macintosh/.test(navigator.userAgent),supportOrientation:!!window.DeviceOrientationEvent,screen:{},document:{},viewport:{},scroll:{direction:"down"},resize:{},getScreenSize:function(){window._lsData.screen={width:screen.width,height:screen.height,ratio:screen.width/screen.height}},slide:{keys:{slidedelay:["data","duration"],duration:["data","duration"],timeshift:["data","timeShift"],transition2d:["data","transition2d"],transition3d:["data","transition3d"],transitionorigami:["data","transitionorigami"],customtransition2d:["data","customtransition2d"],customtransition3d:["data","customtransition3d"],transitionduration:["data","transitionDuration"],backgroundsize:["data","backgroundSize"],bgsize:["data","backgroundSize"],backgroundposition:["data","backgroundPosition"],bgposition:["data","backgroundPosition"],backgroundcolor:["data","backgroundColor"],bgcolor:["data","backgroundColor"],thumbnail:["data","thumbnail"],deeplink:["data","deeplink"],overflow:["data","overflow"],kenburnspan:["kenBurns","pan"],kenburnszoom:["kenBurns","zoom"],kenburnsrotation:["kenBurns","rotation"],kenburnsrotate:["kenBurns","rotation"],kenburnsscale:["kenBurns","scale"],filterfrom:["filter","from"],filterto:["filter","to"],parallaxtype:["parallax","type"],parallaxevent:["parallax","event"],parallaxdirection:["parallax","direction"],parallaxdelay:["parallax","startAt"],parallaxaxis:["parallax","axis"],parallaxtransformorigin:["parallax","transformOrigin"],parallaxdurationmove:["parallax","durationMove"],parallaxdurationleave:["parallax","durationLeave"],parallaxrotate:["parallax","rotation"],parallaxrotation:["parallax","rotation"],parallaxdistance:["parallax","distance"],parallaxtransformperspective:["parallax","transformPerspective"],parallaxpathduration:["parallax","pathDuration"],parallaxpath:["parallax","path"],parallaxcount:["parallax","count"],parallaxstartat:["parallax","startAt"],parallaxrandomduration:["parallax","randomDuration"],parallaxoffsetx:["parallax","randomX"],parallaxoffsety:["parallax","randomY"],parallaxeasing:["parallax","ease"],parallaxrandomstartat:["parallax","randomStartAt"],parallaxrandomcount:["parallax","randomCount"],parallaxrandomwait:["parallax","randomWait"],globalhover:["data","globalhover"]}},layer:{keys:{keyframe:["is"],responsive:["is"],pinned:["is"],position:["settings"],static:["settings"],minresponsiveratio:["settings"],maxresponsiveratio:["settings"],trmask:["settings"],minfontsize:["styleSettings"],minmobilefontsize:["styleSettings"],overlay:["styleSettings"],pointerevents:["styleSettings"],smartbg:["styleSettings"],autoplay:["mediaSettings"],controls:["mediaSettings"],showinfo:["mediaSettings"],thumbnail:["poster","mediaSettings"],poster:["mediaSettings"],volume:["mediaSettings"],muted:["mediaSettings"],loopmedia:["loop","mediaSettings"],backgroundvideo:["backgroundVideo","mediaSettings"],fadein:["opacity","inLayerFromCSS"],opacityin:["opacity","inLayerFromCSS"],rotatein:["rotation","inLayerFromCSS"],rotatexin:["rotationX","inLayerFromCSS"],rotateyin:["rotationY","inLayerFromCSS"],rotationin:["rotation","inLayerFromCSS"],rotationxin:["rotationX","inLayerFromCSS"],rotationyin:["rotationY","inLayerFromCSS"],scalein:["scale","inLayerFromCSS"],scalexin:["scaleX","inLayerFromCSS"],scaleyin:["scaleY","inLayerFromCSS"],skewxin:["skewX","inLayerFromCSS"],skewyin:["skewY","inLayerFromCSS"],bgcolorin:["backgroundColor","inLayerStyleFromCSS"],colorin:["color","inLayerStyleFromCSS"],radiusin:["borderRadius","inLayerStyleShouldBeConvertedFrom"],widthin:["width","inLayerStyleShouldBeConvertedFrom"],heightin:["height","inLayerStyleShouldBeConvertedFrom"],filterin:["filter","inLayerStyleShouldBeConvertedFrom"],clipin:["clipPath","inClipFromCSS"],zindexin:["zIndex","in"],rotate:["rotation","inLayerToCSS"],rotatex:["rotationX","inLayerToCSS"],rotatey:["rotationY","inLayerToCSS"],rotation:["rotation","inLayerToCSS"],rotationx:["rotationX","inLayerToCSS"],rotationy:["rotationY","inLayerToCSS"],scale:["scale","inLayerToCSS"],scalex:["scaleX","inLayerToCSS"],scaley:["scaleY","inLayerToCSS"],skewx:["skewX","inLayerToCSS"],skewy:["skewY","inLayerToCSS"],transformoriginin:["transformOrigin","inLayerShouldBeConverted"],offsetxin:["x","inLayerShouldBeConverted"],offsetyin:["y","inLayerShouldBeConverted"],delayin:["startAt","in"],startatin:["startAt","in"],startatfirst:["startAtFirst","in"],instartat:["startAt","in"],durationin:["duration","in"],easein:["ease","in"],easingin:["ease","in"],transitionin:["enabled","in"],transformmirrorin:["mirror","in"],skipviewport:["skipViewport","settings"],textfadein:["opacity","textInNodesFrom"],textopacityin:["opacity","textInNodesFrom"],textrotatein:["rotation","textInNodesFrom"],textrotatexin:["rotationX","textInNodesFrom"],textrotateyin:["rotationY","textInNodesFrom"],textrotationin:["rotation","textInNodesFrom"],textrotationxin:["rotationX","textInNodesFrom"],textrotationyin:["rotationY","textInNodesFrom"],textscalein:["scale","textInNodesFrom"],textscalexin:["scaleX","textInNodesFrom"],textscaleyin:["scaleY","textInNodesFrom"],textskewxin:["skewX","textInNodesFrom"],textskewyin:["skewY","textInNodesFrom"],textcolorin:["color","textInNodesFrom"],textoverflowin:["overflow","textInLayerStyle"],texteasein:["ease","textInNodesTo"],texteasingin:["ease","textInNodesTo"],texttransformoriginin:["transformOrigin","textInShouldBeConverted"],textoffsetxin:["x","textInShouldBeConverted"],textoffsetyin:["y","textInShouldBeConverted"],texttypein:["type","textIn"],textshiftin:["shiftNodes","textIn"],textdelayin:["startAt","textIn"],textstartatin:["startAt","textIn"],textinstartat:["startAt","textIn"],textdurationin:["duration","textIn"],texttransitionin:["enabled","textIn"],texttransformmirrorin:["mirror","textIn"],fadeout:["opacity","outLayerToCSS"],opacityout:["opacity","outLayerToCSS"],rotateout:["rotation","outLayerToCSS"],rotatexout:["rotationX","outLayerToCSS"],rotateyout:["rotationY","outLayerToCSS"],rotationout:["rotation","outLayerToCSS"],rotationxout:["rotationX","outLayerToCSS"],rotationyout:["rotationY","outLayerToCSS"],scaleout:["scale","outLayerToCSS"],scalexout:["scaleX","outLayerToCSS"],scaleyout:["scaleY","outLayerToCSS"],skewxout:["skewX","outLayerToCSS"],skewyout:["skewY","outLayerToCSS"],bgcolorout:["backgroundColor","outLayerStyleToCSS"],colorout:["color","outLayerStyleToCSS"],radiusout:["borderRadius","outLayerStyleShouldBeConvertedTo"],widthout:["width","outLayerStyleShouldBeConvertedTo"],heightout:["height","outLayerStyleShouldBeConvertedTo"],filterout:["filter","outLayerStyleShouldBeConvertedTo"],clipout:["clipPath","outClipToCSS"],zindexout:["zIndex","out"],transformoriginout:["transformOrigin","outLayerShouldBeConverted"],offsetxout:["x","outLayerShouldBeConverted"],offsetyout:["y","outLayerShouldBeConverted"],showuntil:["showUntil","out"],startatout:["startAt","out"],outstartat:["startAt","out"],durationout:["duration","out"],easeout:["ease","out"],easingout:["ease","out"],transitionout:["enabled","out"],transformmirrorout:["mirror","out"],textfadeout:["opacity","textOutNodesTo"],textopacityout:["opacity","textOutNodesTo"],textrotateout:["rotation","textOutNodesTo"],textrotatexout:["rotationX","textOutNodesTo"],textrotateyout:["rotationY","textOutNodesTo"],textrotationout:["rotation","textOutNodesTo"],textrotationxout:["rotationX","textOutNodesTo"],textrotationyout:["rotationY","textOutNodesTo"],textscaleout:["scale","textOutNodesTo"],textscalexout:["scaleX","textOutNodesTo"],textscaleyout:["scaleY","textOutNodesTo"],textskewxout:["skewX","textOutNodesTo"],textskewyout:["skewY","textOutNodesTo"],textcolorout:["color","textOutNodesTo"],texteaseout:["ease","textOutNodesTo"],texteasingout:["ease","textOutNodesTo"],texttransformoriginout:["transformOrigin","textOutShouldBeConverted"],textoffsetxout:["x","textOutShouldBeConverted"],textoffsetyout:["y","textOutShouldBeConverted"],textoverflowout:["overflow","textOutLayerStyle"],texttypeout:["type","textOut"],textshiftout:["shiftNodes","textOut"],textdelayout:["startAt","textOut"],textstartatout:["startAt","textOut"],textoutstartat:["startAt","textOut"],textdurationout:["duration","textOut"],texttransitionout:["enabled","textOut"],texttransformmirrorout:["mirror","textOut"],loopopacity:["opacity","loopToCSS"],looprotate:["rotation","loopToCSS"],looprotatex:["rotationX","loopToCSS"],looprotatey:["rotationY","loopToCSS"],looprotation:["rotation","loopToCSS"],looprotationx:["rotationX","loopToCSS"],looprotationy:["rotationY","loopToCSS"],loopscale:["scale","loopToCSS"],loopscalex:["scaleX","loopToCSS"],loopscaley:["scaleY","loopToCSS"],loopskewx:["skewX","loopToCSS"],loopskewy:["skewY","loopToCSS"],looptransformorigin:["transformOrigin","loopLayerShouldBeConverted"],loopoffsetx:["x","loopLayerShouldBeConverted"],loopoffsety:["y","loopLayerShouldBeConverted"],loopfilter:["filter","loopLayerShouldBeConverted"],loopclip:["clipPath","loopClipToCSS"],loopdelay:["startAt","loop"],loopstartat:["startAt","loop"],loopduration:["duration","loop"],loopcount:["count","loop"],looprepeatdelay:["repeatDelay","loop"],loopyoyo:["yoyo","loop"],loopease:["ease","loop"],loopeasing:["ease","loop"],loop:["enabled","loop"],hoveropacity:["opacity","hoverToCSS"],hoverrotate:["rotation","hoverToCSS"],hoverrotatex:["rotationX","hoverToCSS"],hoverrotatey:["rotationY","hoverToCSS"],hoverrotation:["rotation","hoverToCSS"],hoverrotationx:["rotationX","hoverToCSS"],hoverrotationy:["rotationY","hoverToCSS"],hoverscale:["scale","hoverToCSS"],hoverscalex:["scaleX","hoverToCSS"],hoverscaley:["scaleY","hoverToCSS"],hoverskewx:["skewX","hoverToCSS"],hoverskewy:["skewY","hoverToCSS"],hoverbgcolor:["backgroundColor","hoverToCSS"],hovercolor:["color","hoverToCSS"],hoverease:["easeIn","hover"],hovereasing:["easeIn","hover"],hovereasein:["easeIn","hover"],hovereasingin:["easeIn","hover"],hovereaseout:["easeOut","hover"],hovereasingout:["easeOut","hover"],hoverduration:["durationIn","hover"],hoverdurationin:["durationIn","hover"],hoverdurationout:["durationOut","hover"],hoveralwaysontop:["alwaysOnTop","hover"],hoveroffsetx:["x","hoverShouldBeConverted"],hoveroffsety:["y","hoverShouldBeConverted"],hoverfilter:["filter","hoverShouldBeConverted"],hoverborderradius:["borderRadius","hoverShouldBeConverted"],hoverradius:["borderRadius","hoverShouldBeConverted"],hovertransformorigin:["transformOrigin","hoverShouldBeConverted"],hoverskipglobal:["skipGlobal","hover"],hover:["enabled","hover"],kenburnspan:["pan","kenBurns"],kenburnszoom:["zoom","kenBurns"],kenburnsrotation:["rotation","kenBurns"],kenburnsrotate:["rotation","kenBurns"],kenburnsscale:["scale","kenBurns"],parallaxlevel:["level","parallax"],parallaxtype:["type","parallax"],parallaxevent:["event","parallax"],parallaxdirection:["direction","parallax"],parallaxduration:["duration","parallax"],parallaxdelay:["startAt","parallax"],parallaxaxis:["axis","parallax"],parallaxtransformorigin:["transformOrigin","parallax"],parallaxdurationmove:["durationMove","parallax"],parallaxdurationleave:["durationLeave","parallax"],parallaxrotate:["rotation","parallax"],parallaxrotation:["rotation","parallax"],parallaxreset:["reset","parallax"],parallaxdistance:["distance","parallax"],parallaxpath:["path","parallax"],parallaxcount:["count","parallax"],parallaxstartat:["startAt","parallax"],parallaxpathduration:["pathDuration","parallax"],parallaxrandomduration:["randomDuration","parallax"],parallaxoffsetx:["randomX","parallax"],parallaxoffsety:["randomY","parallax"],parallaxeasing:["ease","parallax"],parallaxrandomcount:["randomCount","parallax"],parallaxrandomstartat:["randomStartAt","parallax"],parallaxrandomwait:["randomWait","parallax"],parallax:["enabled","parallax"],scroll:["enabled","scroll"],scrollduration:["duration","scroll"],scrolldurationrev:["durationRev","scroll"],scrollease:["ease","scroll"],scrolleaserev:["easeRev","scroll"],scrollcenter:["center","scroll"],scrollopacity:["opacity","scroll"],scrollopacitymin:["opacitymin","scroll"],scrollopacitymax:["opacitymax","scroll"],scrollopacityyoyo:["opacityyoyo","scroll"],scrollopacityinvert:["opacityinvert","scroll"],scrollrotate:["rotation","scroll"],scrollrotatemin:["rotationmin","scroll"],scrollrotatemax:["rotationmax","scroll"],scrollrotateyoyo:["rotationyoyo","scroll"],scrollrotatex:["rotationX","scroll"],scrollrotatexmin:["rotationXmin","scroll"],scrollrotatexmax:["rotationXmax","scroll"],scrollrotatexyoyo:["rotationXyoyo","scroll"],scrollrotatey:["rotationY","scroll"],scrollrotateymin:["rotationYmin","scroll"],scrollrotateymax:["rotationYmax","scroll"],scrollrotateyyoyo:["rotationYyoyo","scroll"],scrollrotation:["rotation","scroll"],scrollrotationmin:["rotationmin","scroll"],scrollrotationmax:["rotationmax","scroll"],scrollrotationyoyo:["rotationyoyo","scroll"],scrollrotationx:["rotationX","scroll"],scrollrotationxmin:["rotationXmin","scroll"],scrollrotationxmax:["rotationXmax","scroll"],scrollrotationxyoyo:["rotationXyoyo","scroll"],scrollrotationy:["rotationY","scroll"],scrollrotationymin:["rotationYmin","scroll"],scrollrotationymax:["rotationYmax","scroll"],scrollrotationyyoyo:["rotationYyoyo","scroll"],scrollscalex:["scaleX","scroll"],scrollscalexmin:["scaleXmin","scroll"],scrollscalexmax:["scaleXmax","scroll"],scrollscalexyoyo:["scaleXyoyo","scroll"],scrollscaley:["scaleY","scroll"],scrollscaleymin:["scaleYmin","scroll"],scrollscaleymax:["scaleYmax","scroll"],scrollscaleyyoyo:["scaleYyoyo","scroll"],scrollskewx:["skewX","scroll"],scrollskewxmin:["skewXmin","scroll"],scrollskewxmax:["skewXmax","scroll"],scrollskewxyoyo:["skewXyoyo","scroll"],scrollskewy:["skewY","scroll"],scrollskewymin:["skewYmin","scroll"],scrollskewymax:["skewYmax","scroll"],scrollskewyyoyo:["skewYyoyo","scroll"],scrolloffsetx:["x","scroll"],scrolloffsetxmin:["xmin","scroll"],scrolloffsetxmax:["xmax","scroll"],scrolloffsetxyoyo:["xyoyo","scroll"],scrolloffsetxresponsive:["xresponsive","scroll"],scrolloffsety:["y","scroll"],scrolloffsetymin:["ymin","scroll"],scrolloffsetymax:["ymax","scroll"],scrolloffsetyyoyo:["yyoyo","scroll"],scrolloffsetyresponsive:["yresponsive","scroll"],scrolltransformorigin:["transformOrigin","scrollShouldBeConverted"],scrolltransformoriginalt:["transformOriginAlt","scrollShouldBeConverted"],scrollgetposition:["getPosition","scroll"],transformperspective:["layer","transformPerspective"],transformperspectivein:["layer","transformPerspective"],transformperspectiveout:["layer","transformPerspective"],texttransformperspective:["text","transformPerspective"],texttransformperspectivein:["text","transformPerspective"],texttransformperspectiveout:["text","transformPerspective"],looptransformperspective:["loop","transformPerspective"],hovertransformperspective:["hover","transformPerspective"],parallaxtransformperspective:["parallax","transformPerspective"],scrolltransformperspective:["scroll","transformPerspective"]}}},ie(window).on("resize.lsGlobal",function(){window._lsData.documentIsAccessible&&(window._lsData.document={width:document.body.scrollWidth,height:document.body.scrollHeight},window._lsData.viewport={lastWidth:window._lsData.viewport.width||0,lastHeight:window._lsData.viewport.height||0,width:ie(window).width(),height:ie(window).height()},ie(window).trigger("scroll.lsGlobal"),window._lsData.resize.timeout&&clearTimeout(window._lsData.resize.timeout),window._lsData.resize.once?window._lsData.resize.once=!1:window._lsData.resize.timeout=setTimeout(function(){window._lsData.resize.once=!0,ie(window).trigger("resize.lsGlobal")},100))}),ie(window).on("scroll.lsGlobal",function(){window._lsData.documentIsAccessible&&(window._lsData.scroll.left=window.pageXOffset,window._lsData.scroll.top=window.pageYOffset,window._lsData.scroll.leftMax=window._lsData.document.width-window._lsData.viewport.width,window._lsData.scroll.topMax=window._lsData.document.height-window._lsData.viewport.height)}),ie(document).on("wheel.lsGlobal ",function(e){0!==Math.abs(e.originalEvent.deltaY)&&(window._lsData.scroll.direction=e.originalEvent.deltaY<0?"up":"down")}),ie(document).on("touchstart.lsGlobal ",function(e){window._lsData.ts=e.originalEvent.touches[0].clientY}),ie(document).on("touchend.lsGlobal ",function(e){e=e.originalEvent.changedTouches[0].clientY;window._lsData.ts>e+5?window._lsData.scroll.direction="down":window._lsData.ts<e-5&&(window._lsData.scroll.direction="up")}),ie.fn.layerSlider=function(i,a,s,r){window._lsData.documentIsAccessible=!0,i=i||{};var t,e="1.8.0",o=ie.fn.jquery;if(window._layerSlider.checkVersions(e,o,e))return(typeof i).match("object|undefined")?this.each(function(e){t="LS"+Math.random().toString(36).substr(2,9),ie(this).data("lsSliderUID")||(window._layerSliders[t]=new n(this,ie(this),i,t))}):"data"===i?window._layerSliders[this.data("lsSliderUID")]:"eventData"===i?window._layerSliders[this.data("lsSliderUID")].api.eventData():"defaultInitOptions"===i?window._layerSliders[this.data("lsSliderUID")].defaults.init.options||!1:"userInitOptions"===i?window._layerSliders[this.data("lsSliderUID")].userInitOptions||!1:"sliderInitOptions"===i?window._layerSliders[this.data("lsSliderUID")].o||!1:"originalMarkup"===i?window._layerSliders[this.data("lsSliderUID")].originalMarkup||!1:this.each(function(e){var t=window._layerSliders[ie(this).data("lsSliderUID")];t&&t.api.methods(i,a,s,r),t=null});window._layerSlider.showNotice(this,"oldjquery",o,e)};var n=function(L,$,i,W){$.data("lsSliderUID",W).attr("data-layerslider-uid",W);var J=this,ee=J.gsap=window._layerSlider.GSAP||window,M=ie(window),te=window._lsData;J.defaults={init:{staggerSplitChar:"|",rangeSplitChar:"..",randomSplitChar:"|",openingBracket:"[",closingBracket:"]",dataKey:"_LS",controls:["#playmedia","#pausemedia","#unmute","#unmutemedia","#start","#stop","#prev","#next","#replay","#reverse","#reverse-replay","#reversereplay"],options:{silentMode:!1,getData:!1,destroyAfter:!0,type:"responsive",fullSizeMode:"normal",fitScreenWidth:!0,calculateOffsetFrom:!1,preventSliderClip:!0,allowFullscreen:!1,performanceMode:!0,performanceModeThreshold:"20sh",responsiveUnder:-1,layersContainerWidth:-1,layersContainerHeight:-1,maxRatio:-1,insertMethod:"prependTo",insertSelector:null,clipSlideTransition:!1,slideBGSize:"cover",slideBGPosition:"50% 50%",preferBlendMode:!1,scene:!1,sceneHeight:2,sceneSpeed:100,sceneDuration:null,smoothScrollDuration:1e3,stickTo:"center",autoStart:!0,startInViewport:!0,playByScroll:!1,playByScrollSpeed:1,playByScrollStart:!1,playByScrollSkipSlideBreaks:!1,pauseOnHover:!1,pauseLayers:!1,firstSlide:1,sliderFadeInDuration:0,animateFirstSlide:!1,firstSlideDuration:!1,firstSlideTimeShift:!1,cycles:-1,forceCycles:!0,twoWaySlideshow:!1,shuffleSlideshow:!1,forceLayersOutDuration:750,slideDuration:!1,slideDurationWithoutLayers:3e3,slideTransitionDuration:!1,slideTimeShift:0,skin:"v6",skinsPath:"/layerslider/skins/",globalBGColor:"transparent",globalBGImage:!1,globalBGRepeat:"no-repeat",globalBGAttachment:"scroll",globalBGSize:"auto",globalBGPosition:"50% 50%",marginTop:!1,marginBottom:!1,navPrevNext:!0,navStartStop:!0,navButtons:!0,keybNav:!0,touchNav:!0,hoverPrevNext:!0,hoverBottomNav:!1,showBarTimer:!1,showCircleTimer:!0,showSlideBarTimer:!1,thumbnailNavigation:"hover",tnFillMode:"contain",tnContainerWidth:"60%",tnWidth:100,tnHeight:60,tnActiveOpacity:35,tnInactiveOpacity:100,scrollModifier:0,autoPlayVideos:!0,autoPauseSlideshow:"auto",youtubePreview:"maxresdefault.jpg",rememberUnmuteState:!0,parallaxCenterDegree:40,parallaxSensitivity:10,parallaxCenterLayers:"center",parallaxScrollReverse:!1,scrollCenterLayers:"center",yourLogo:!1,yourLogoStyle:"left: -10px; top: -10px;",yourLogoLink:!1,yourLogoTarget:"_self",optimizeForMobile:!0,hideOnMobile:!1,hideUnder:-1,hideOver:-1,slideOnSwipe:!0,allowRestartOnResize:!1,fixFloatedContainers:!1,useSrcset:!0,hashChange:!1,refreshWaypoint:!0,staticImage:""}},slider:{errorText:"LayerSlider (UID: "+W+") error:"},slide:{keys:window._lsData.slide.keys,options:{$link:!1,index:-1,data:{duration:-1,timeShift:0,calculatedTimeShift:0},parallax:{count:-1,path:"circle",direction:"forward",startAt:"slidestart",pathDuration:3,type:"2d",randomCount:-1,randomX:"[-35sw..35sw]",randomY:"[-35sh..35sh]",randomStartAt:"slidestart",randomDuration:3,randomWait:0,distance:10,rotation:10,ease:"easeInOutSine"},kenBurns:{scale:1.2},filter:{}},registerPluginDefaults:function(e,t,i){J.defaults.slide.options.plugins||(J.defaults.slide.options.plugins={}),J.defaults.slide.options.plugins[e]=t}},layer:{keys:window._lsData.layer.keys,splitTypeKeys:["chars_asc","chars_desc","chars_rand","chars_center","chars_edge","words_asc","words_desc","words_rand","words_center","words_edge","lines_asc","lines_desc","lines_rand","lines_center","lines_edge"],timelineHierarchy:{slidestart:[0],transitioninstart:[1],transitioninend:[2],textinstart:[3,[1,2,6,7,8]],textinend:[4],allinend:[5],loopstart:[6,[1,2,3,4,5]],loopend:[7],autoparallaxstart:[6.5,[0,1,2,3,4,5,6,7]],autoparallaxend:[7.5],transitioninandloopend:[8],textinandloopend:[9],allinandloopend:[10],textoutstart:[11,[2,3,4,5,6,7,8,9,10]],textoutend:[12],textoutandloopend:[13],transitionoutstart:[14,[2,3,4,5,6,7,8,9,10,11,12,13]],transitionoutend:[15],alloutend:[16],alloutandloopend:[17]},properties:{filter:function(){return{blur:0,brightness:100,contrast:100,grayscale:0,"hue-rotate":0,invert:0,saturate:100,sepia:0}}},options:function(e,t){var i=e.data("countdown")||{},a=e.data("counter")||{},t={is:{slideBackground:!!e.is("img.ls-bg"),backgroundVideo:!!e.is(".ls-bg-video"),imageLayer:!!e.is("img.ls-layer, picture.ls-layer"),countdown:!(!i.date||!i.component),counter:!!a.type,layerGroup:!!e.is(".ls-layer-group"),insideLayerGroup:!!e.data("$layerGroup"),mediaLayer:!1,textLayer:!1,responsive:!0,onSlide:t},should:{},elements:{},settings:{position:"relative",slideIn:t,slideOut:t},styleSettings:{minfontsize:0,minmobilefontsize:0},mediaSettings:{backgroundVideo:!1},timeline:{slidestart:0,transitioninstart:0,transitioninend:0,textinstart:0,textinend:0,allinend:function(e){return Math.max(this.transitioninend,this.textinend)},loopstart:0,loopend:0,autoparallaxstart:0,autoparallaxend:0,transitioninandloopend:function(e){return 0===this.loopend&&e.loop.enabled&&("number"==typeof e.loop.startAt||-1!==e.loop.startAt.indexOf("textinstart")&&-1!==e.loop.startAt.indexOf("textinend")&&-1!==e.loop.startAt.indexOf("allinend"))?(this.loopstart=J.transitions.layers.timeline.getTiming(e,e.loop.startAt,"loopstart"),this.loopend=-1!==e.loop.count&&e.timeline.loopstart+(e.loop.repeat+1)*e.loop.duration+e.loop.repeat*e.loop.repeatDelay):J.debugMode&&J.debug.add("warn","layerTransition.infinite",e.self[0].tagName+"."+e.self.attr("class")+" [ "+e.self.html().substr(0,30)+"... ]"),Math.max(this.transitioninend,this.loopend,this.autoparallaxend)},textinandloopend:function(e){return Math.max(this.textinend,this.loopend,this.autoparallaxend)},allinandloopend:function(e){return Math.max(this.allinend(),this.loopend,this.autoparallaxend)},textoutstart:0,textoutend:0,textoutandloopend:function(e){return Math.max(this.textoutend,this.loopend,this.autoparallaxend)},transitionoutstart:function(e){return Math.max(this.allinandloopend(),this.textoutend,this.autoparallaxend)},transitionoutend:0,alloutend:function(e){return Math.max(this.transitionoutend,this.textoutend,this.allinend())},alloutandloopend:function(e){return Math.max(this.transitionoutend,this.textoutandloopend(),this.allinend())},staticfrom:!1,staticto:!1},transitionProperties:{in:{enabled:!0,layerFrom:{autoCSS:!1,immediateRender:!1,css:{opacity:0}},layerTo:{autoCSS:!1,onStart:function(){J.transitions.layers.in.onStart(e)},onComplete:function(){J.transitions.layers.in.onComplete(e)},css:{display:"block",opacity:1,rotation:0,rotationX:0,rotationY:0,scaleX:1,scaleY:1,skewX:0,skewY:0,x:0,y:0}},layerStyleFrom:{autoCSS:!1,immediateRender:!1,css:{}},layerStyleTo:{autoCSS:!1,css:{}},clipFrom:{autoCSS:!1,immediateRender:!1,css:{}},layerShouldBeConverted:{transformOrigin:"50% 50% 0",x:0,y:0},layerStyleShouldBeConvertedFrom:{},layerStyleShouldBeConvertedTo:{},startAt:0,duration:1,ease:"easeInOutQuint"},textIn:{enabled:null,nodesFrom:{cycle:{},random:{},opacity:0},nodesTo:{ease:"easeInOutQuint",css:{opacity:1,rotation:0,rotationX:0,rotationY:0,scaleX:1,scaleY:1,skewX:0,skewY:0,x:0,y:0}},shouldBeConverted:{cycle:{},random:{},transformOrigin:"50% 50% 0",x:0,y:0},layerStyle:{},split:"",shiftNodes:.05,startAt:"transitioninend",duration:1},out:{enabled:!0,layerFrom:{autoCSS:!1,immediateRender:!1,css:{}},layerTo:{autoCSS:!1,onStart:function(){J.transitions.layers.out.onStart(e)},onComplete:function(){J.transitions.layers.out.onComplete(e)},css:{opacity:0,rotation:0,rotationX:0,rotationY:0,scaleX:1,scaleY:1,skewX:0,skewY:0}},layerStyleFrom:{autoCSS:!1,immediateRender:!1,css:{}},layerStyleTo:{autoCSS:!1,css:{}},clipTo:{autoCSS:!1,css:{}},layerShouldBeConverted:{x:0,y:0},layerStyleShouldBeConvertedFrom:{},layerStyleShouldBeConvertedTo:{},startAt:"slidechangeonly",duration:1,ease:"easeInOutQuint"},textOut:{enabled:null,nodesFrom:{immediateRender:!1,cycle:{},opacity:1},nodesTo:{ease:"easeInOutQuint",immediateRender:!1,cycle:{},random:{},opacity:0},layerStyle:{},shouldBeConverted:{cycle:{},random:{},x:0,y:0},split:"",startAt:"allinandloopend",shiftNodes:.05,duration:1},loop:{enabled:null,from:{autoCSS:!1,immediateRender:!1,css:{}},to:{autoCSS:!1,css:{}},clipTo:{autoCSS:!1,immediateRender:!1,css:{}},layerShouldBeConverted:{transformOrigin:"50% 50% 0",x:0,y:0},ease:"linear",startAt:"allinend",repeatDelay:0,duration:1,count:0,yoyo:!1},hover:{enabled:null,from:{autoCSS:!1,immediateRender:!1,css:{}},to:{autoCSS:!1,css:{}},shouldBeConverted:{transformOrigin:"50% 50% 0"},alwaysOnTop:!0,easeIn:"easeOutQuart",durationIn:.5},parallax:{enabled:null},scroll:{enabled:null,shouldBeConverted:{transformOrigin:"50% 50% 0"},xresponsive:!0,yresponsive:!0},kenBurns:{scale:1.2},clip:{enabled:!1,default:{autoCSS:!1,immediateRender:!1,css:{clipPath:"polygon(0% 0%,100% 0%,100% 100%,0% 100%)"}},style:{autoCSS:!1,immediateRender:!1,css:{clipPath:"polygon(0% 0%,100% 0%,100% 100%,0% 100%)"}},none:{autoCSS:!1,immediateRender:!1,css:{clipPath:"polygon(-9999% -9999%,9999% -9999%,9999% 9999%,-9999% 9999%)"}}},filter:{values:{style:{},in:{},out:{},loop:{},hover:{},afterIn:{},afterLoop:{},bgFrom:{},bgTo:{}},transitions:{bg:null,in:null,out:null,loop:null,hover:null}},init:{wrapper:{autoCSS:!1,immediateRender:!1,css:{display:"block"}}},transformPerspective:{layer:500,text:500,loop:500,hover:500},reset:{wrapperOnTimelineEnd:{autoCSS:!1,css:{opacity:1,display:"none"}},wrapperOnSlideChange:{autoCSS:!1,css:{x:0,y:0,rotation:0,rotationX:0,rotationY:0,scaleX:1,scaleY:1,skewX:0,skewY:0,opacity:1,display:"none"}},altWrapperOnSlideChange:{autoCSS:!1,css:{x:0,y:0,rotation:0,rotationX:0,rotationY:0,scaleX:1,scaleY:1,skewX:0,skewY:0,opacity:1}}}}};return{is:t.is,should:t.should,elements:t.elements,settings:t.settings,styleSettings:t.styleSettings,mediaSettings:t.mediaSettings,mediaProperties:t.mediaProperties,timeline:t.timeline,in:t.transitionProperties.in,inLayerFrom:t.transitionProperties.in.layerFrom,inLayerFromCSS:t.transitionProperties.in.layerFrom.css,inLayerStyleFrom:t.transitionProperties.in.layerStyleFrom,inLayerStyleFromCSS:t.transitionProperties.in.layerStyleFrom.css,inClipFrom:t.transitionProperties.in.clipFrom,inClipFromCSS:t.transitionProperties.in.clipFrom.css,inLayerTo:t.transitionProperties.in.layerTo,inLayerToCSS:t.transitionProperties.in.layerTo.css,inLayerStyleTo:t.transitionProperties.in.layerStyleTo,inLayerStyleToCSS:t.transitionProperties.in.layerStyleTo.css,inClipTo:t.transitionProperties.clip.style,inClipToCSS:t.transitionProperties.clip.style.css,inLayerShouldBeConverted:t.transitionProperties.in.layerShouldBeConverted,inLayerStyleShouldBeConvertedFrom:t.transitionProperties.in.layerStyleShouldBeConvertedFrom,inLayerStyleShouldBeConvertedTo:t.transitionProperties.in.layerStyleShouldBeConvertedTo,textIn:t.transitionProperties.textIn,textInLayerStyle:t.transitionProperties.textIn.layerStyle,textInNodesFrom:t.transitionProperties.textIn.nodesFrom,textInNodesTo:t.transitionProperties.textIn.nodesTo,textInNodesToCSS:t.transitionProperties.textIn.nodesTo.css,textInShouldBeConverted:t.transitionProperties.textIn.shouldBeConverted,out:t.transitionProperties.out,outLayerFrom:t.transitionProperties.out.layerFrom,outLayerFromCSS:t.transitionProperties.out.layerFrom.css,outLayerStyleFrom:t.transitionProperties.out.layerStyleFrom,outLayerStyleFromCSS:t.transitionProperties.out.layerStyleFrom.css,outClipFrom:t.transitionProperties.clip.style,outClipFromCSS:t.transitionProperties.clip.style.css,outLayerTo:t.transitionProperties.out.layerTo,outLayerToCSS:t.transitionProperties.out.layerTo.css,outLayerStyleTo:t.transitionProperties.out.layerStyleTo,outLayerStyleToCSS:t.transitionProperties.out.layerStyleTo.css,outClipTo:t.transitionProperties.out.clipTo,outClipToCSS:t.transitionProperties.out.clipTo.css,outLayerShouldBeConverted:t.transitionProperties.out.layerShouldBeConverted,outLayerStyleShouldBeConvertedFrom:t.transitionProperties.out.layerStyleShouldBeConvertedFrom,outLayerStyleShouldBeConvertedTo:t.transitionProperties.out.layerStyleShouldBeConvertedTo,textOut:t.transitionProperties.textOut,textOutLayerStyle:t.transitionProperties.textOut.layerStyle,textOutNodesFrom:t.transitionProperties.textOut.nodesFrom,textOutNodesTo:t.transitionProperties.textOut.nodesTo,textOutShouldBeConverted:t.transitionProperties.textOut.shouldBeConverted,loop:t.transitionProperties.loop,loopFrom:t.transitionProperties.loop.from,loopFromCSS:t.transitionProperties.loop.from.css,loopClipFrom:t.transitionProperties.clip.style,loopClipFromCSS:t.transitionProperties.clip.style.css,loopTo:t.transitionProperties.loop.to,loopToCSS:t.transitionProperties.loop.to.css,loopClipTo:t.transitionProperties.loop.clipTo,loopClipToCSS:t.transitionProperties.loop.clipTo.css,loopLayerShouldBeConverted:t.transitionProperties.loop.layerShouldBeConverted,hover:t.transitionProperties.hover,hoverFrom:t.transitionProperties.hover.from,hoverFromCSS:t.transitionProperties.hover.from.css,hoverTo:t.transitionProperties.hover.to,hoverToCSS:t.transitionProperties.hover.to.css,hoverShouldBeConverted:t.transitionProperties.hover.shouldBeConverted,parallax:t.transitionProperties.parallax,scroll:t.transitionProperties.scroll,scrollShouldBeConverted:t.transitionProperties.scroll.shouldBeConverted,kenBurns:t.transitionProperties.kenBurns,clip:t.transitionProperties.clip,filter:t.transitionProperties.filter,transformPerspective:t.transitionProperties.transformPerspective,init:t.transitionProperties.init,reset:t.transitionProperties.reset,countdownSettings:i,counterSettings:a}}}},J.slides={count:0,first:{},last:{},prev:{},current:{},next:{},init:function(){if(!document.body.contains(L))return!1;for(var e=$.find("> .ls-layer, > .ls-slide"),t=0,i=J.defaults.slide.keys,a=0,s=e.length;a<s;a++){var r=ie(e[a]),o=r[0].style,n=ie.extend(!0,{},J.defaults.slide.options),l=!1;if(J.slides.count++,r.removeClass("ls-layer").addClass("ls-slide").css({width:J.slider.initial.originalWidth,height:J.slider.initial.originalHeight}).appendTo(J.slider.$hiddenWrapper),r.data("ls"))for(var d=r.data("ls").split(";"),p=0;p<d.length;p++){var c,u,h=d[p].split(":");h[0]=ie.trim(h[0].toLowerCase()),h[1]=ie.trim(h[1]),-1==["thumbnail"].indexOf(h[0])&&(h[1]=h[1].toLowerCase()),""!==h[0]&&(void 0!==i[h[0]]?(c=void 0===i[h[0]][1]?h[0]:i[h[0]][1],u=J.functions.convert.properties(h[1]),c.match(/(duration|delay|wait|timeshift)/i)&&ie.isNumeric(u)&&(u/=1e3),n[i[h[0]][0]]||(n[i[h[0]][0]]={}),n[i[h[0]][0]][c]=u):n.data[h[0]]=h[1])}if(n.plugins&&!ie.isEmptyObject(n.plugins))for(var m in n.plugins)if(r.data("ls-plugin-"+m)){var f,g=r.data("ls-plugin-"+m).toLowerCase().split(";"),y={};for(f in n.plugins[m])y[f.toLowerCase()]=f;for(var v=0;v<g.length;v++){var x,w=g[v].split(":");w[0]=ie.trim(w[0]),""!==w[0]&&(x=J.functions.convert.properties(ie.trim(w[1])),-1===w[0].indexOf("duration")&&-1===w[0].indexOf("delay")||(x/=1e3),y[w[0]]?n.plugins[m][y[w[0]]]=x:n.plugins[m][w[0]]=x)}}else delete n.plugins[m];r.children("a.ls-link").length&&(n.data.$link=r.children("a.ls-link").first().css({zIndex:5}).attr("data-ls-slide-link",t+1).appendTo(J.slider.$layersWrapper),J.layers.set.smartLinks(n.data.$link)),n.data.$backgroundVideo=r.children('[data-ls*="backgroundvideo"]').first(),n.data.$backgroundVideo.find("iframe, video, audio").length||(n.data.$backgroundVideo=ie()),n.data.$backgroundVideo.length&&(null!==n.data.$backgroundVideo.attr("data-ls").split("backgroundvideo")[1].split(";")[0].match(/(true|enabled|on|1)/i)?(n.data.$backgroundVideo.addClass("ls-bg-video").css({width:"auto",height:"auto"}).children("video, audio, iframe").css({width:"100%",height:"100%"}),n.data.$backgroundVideo.append(ie('<div class="ls-bg-video-overlay"></div>'))):n.data.$backgroundVideo=!1),r.find("> .ls-bg").length&&(n.data.$background=r.find("> .ls-bg").first()),n.data.thumbnail||(r.find("> .ls-tn").length?l=r.find("> .ls-tn").first():r.find("> .ls-bg").length&&(l=r.find("> .ls-bg").first()),l?(n.data.thumbnail=J.functions.getURL(l),n.data.tnAlt=J.functions.getALT(l)):n.data.thumbnail=J.o.skinsPath+J.o.skin+"/nothumb.png"),(n.data.customtransition2d||n.data.customtransition3d)&&"undefined"==typeof layerSliderCustomTransitions&&(delete n.data.customtransition2d,delete n.data.customtransition3d,J.debugMode&&J.debug.add("warn","sliderInit.customTransitions",t+1)),"visible"===o.overflow&&(n.data.overflow="visible"),n.data.backgroundColor?n.data.$background||n.data.$backgroundVideo&&n.data.$backgroundVideo.length||(n.data.$background=ie("<img>").addClass("ls-bg").attr("src","data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==").appendTo(r)):n.data.backgroundColor=""===r[0].style.backgroundColor?"transparent":r[0].style.backgroundColor,J.slides[++t]={},J.slides[t].data=ie.extend(!0,{},J.defaults.slide.options.data,n.data),J.slides[t].parallax=n.parallax,J.slides[t].scroll=n.scroll,J.slides[t].kenBurns=n.kenBurns,J.slides[t].filter=n.filter,J.slides[t].index=t,J.slides[t].$layers=ie(),J.slides[t].plugins=n.plugins,J.slider.thumbnails.push(n.data.thumbnail),J.layers.init(r,t)}J.debugMode&&J.debug.groupEnd("sliderInit.style")},set:{slideIndexes:function(){var e=J.slides;e.prev.index=e.current.index,e.current.index=e.next.index,e.next.index=J.slideshow.get.slideInSequence(J.slideshow.direction),e.set.slidesData(),J.slider.set.attributes(),J.slides.indexesCreated=!0},nextSlideIndex:function(e){var t=J.slides;t.next.index=e,t.set.slidesData()},slidesData:function(){var e=J.slides;e.prev=-1!==e.prev.index?ie.extend(!0,{},e[e.prev.index]):{},e.current=-1!==e.current.index?ie.extend(!0,{},e[e.current.index]):{},e.next=-1!==e.next.index?ie.extend(!0,{},e[e.next.index]):{}},firstSlide:function(){var e,t=J.slides;t.first.index="random"===J.o.firstSlide?J.o.firstSlide:Math.max(J.functions.convert.properties(J.o.firstSlide,!0),1),J.o.shuffleSlideshow&&2<J.slides.count?J.o.twoWaySlideshow=!1:J.o.shuffleSlideshow=!1,t.first.index="random"==t.first.index?Math.floor(Math.random()*J.slides.count+1):t.first.index,!document.location.hash||(e=J.slides.deeplink(document.location.hash,!0))&&(J.slides.first.index=e),t.first.index=t.first.index<1||t.first.index>J.slides.count?1:t.first.index,J.o.shuffleSlideshow&&"random"!=J.o.firstSlide&&(t.first.index=J.o.firstSlide),t[t.first.index]&&t[t.first.index].data&&(t.first.data=ie.extend(!0,{},t[t.first.index].data)),J.o.playByScroll&&J.slideshow.set.normalizedSequence(),J.debugMode&&J.debug.options.firstSlide&&(t.first.index=J.debug.options.firstSlide)},actions:function(e,t){}},get:{deeplink:function(e){return e&&J.slides[e]&&J.slides[e].data&&J.slides[e].data.deeplink?J.slides[e].data.deeplink:null}},deeplink:function(e,t){for(var i=!1,a=1;a<J.slides.count+1;a++)if(J.slides[a].data.deeplink==e.substring(1)){if(i=a,t)return i;J.slideshow.changeTo(i,!0,!0)}if(!i&&t)return!1},slide:[]},J.countdown={calculateDate:function(e){var t,i=J.countdown.calculateDistance(e.date,e.repeat);switch(e.component){case"days":t=Math.floor(i/864e5);break;case"hours":t=Math.floor(i%864e5/36e5);break;case"minutes":t=Math.floor(i%36e5/6e4);break;case"seconds":t=Math.floor(i%6e4/1e3)}return t=Math.max(0,t||0),e.leadingZeros&&(t=t.toString().padStart(2,"0")),t},calculateDistance:function(e,t){var i=new Date(e),a=new Date;if(a<=i)return i.getTime()-a.getTime();switch(t){case"daily":var s=Math.ceil((a-i)/864e5);i.setDate(i.getDate()+s);break;case"weekly":s=Math.ceil((a-i)/6048e5);i.setDate(i.getDate()+7*s);break;case"monthly":var r=12*(a.getFullYear()-i.getFullYear())+a.getMonth()-i.getMonth();i.getDate()>a.getDate()&&r--,i.setMonth(i.getMonth()+r+1);break;case"yearly":r=a.getFullYear()-i.getFullYear();(i.getMonth()>a.getMonth()||i.getMonth()===a.getMonth()&&i.getDate()>a.getDate())&&r--,i.setFullYear(i.getFullYear()+r+1)}return i.getTime()-a.getTime()},startInterval:function(e,t){J.countdown.setLayerTimer(e,t),J.countdown.stopInterval(t),t.interval=setInterval(function(){J.countdown.setLayerTimer(e,t)},1e3)},setLayerTimer:function(e,t){e.text(this.calculateDate(t))},stopInterval:function(e){e.interval&&clearInterval(e.interval)}},J.counter={start:function(t,i){this.stop(i);var e=parseFloat(i.start),a=parseFloat(i.end),s=a<e?e:a,r=e<a?e:a,o=parseFloat(i.duration)||2e3,n=i.ease||"easeOutSine",l=parseFloat(i.step)||1,d=parseFloat(i.stepDelay)||50,p=Math.max(parseInt(e).toString().length,parseInt(a).toString().length);i.maxLength=p,"step"===i.type&&(n="linear",o=Math.abs(e-a)/l*d),isNaN(e)||isNaN(a)||(i.animationTween=ee.TweenMax.to({value:e},o/1e3,{value:a,ease:n,onUpdate:function(){var e=this.target.value;"step"===i.type&&(e=Math.round(e/l)*l,e=Math.max(e,r),e=Math.min(e,s)),t.text(J.counter.applyNumberFormat(i,e))},onComplete:function(){t.text(J.counter.applyNumberFormat(i,a))}}))},applyNumberFormat:function(e,t){var i=e.dp,a=e.ds,s=e.ts;if(!ie.isNumeric(t))return t;var i=parseFloat(t).toFixed(i).split("."),r=i[0],i=i[1];if(e.lz)for(;r.length<e.maxLength;0)r="0"+r;return r=r.replace(/\B(?=(\d{3})+(?!\d))/g,s),i?r+a+i:r},stop:function(e){e.animationTween&&(e.animationTween.kill(),e.animationTween=null)},setFormat:function(e,t){}},J.layers={$all:ie(),getStyle:function(e,t){return-1!=e.indexOf("em")?e:(-1!=e.indexOf("%")?parseFloat(e)*t:parseFloat(e)||0).toString()},toNum:function(e,t){return t=parseFloat(t),-1!=e.indexOf("em")?parseFloat(e)*t:parseFloat(e)},init:function(e,t){if(!document.body.contains(L))return!1;e.find("noscript").remove();for(var i,a=e.find(".ls-bg, .ls-l, .ls-layer, .ls-lg, .ls-layer-group"),s=0,r=a.length;s<r;s++){var o=ie(a[s]),n=o[0],l=o.children();if(o.data("slideIndex",t),o.hasClass("ls-l"))o.removeClass("ls-l").addClass("ls-layer");else if(o.hasClass("ls-lg"))o.removeClass("ls-lg").addClass("ls-layer-group");else if(!o.is(".ls-bg, .ls-layer, .ls-layer-group")){o.remove();continue}o.is("a")&&1===l.length&&((n=(o=o.children().first())[0]).setAttribute("data-ls",n.parentNode.getAttribute("data-ls")),n.parentNode.removeAttribute("data-ls"),o.parent().removeClass("ls-layer"),o.addClass("ls-layer")),o.is(".ls-layer-group")&&o.children().data("$layerGroup",o);n=new J.defaults.layer.options(o,t);o.data(J.defaults.init.dataKey,n),-1!==o.attr("class").indexOf("ls-linkto-")&&this.set.linkTo(o),n.is.counter&&(n.counterSettings.ease=J.functions.convert.easing(n.counterSettings.ease)),o.parent().is("a")?(o.parent().data("slideIndex")&&o.data("slideIndex",o.parent().data("slideIndex")),i=o.parent(),this.set.smartLinks(i)):i=o,i.attr("data-ls-actions")&&this.set.actions(i,JSON.parse(i.attr("data-ls-actions"))||{}),J.slides[t].$layers=J.slides[t].$layers.add(i)}},set:{mirrorTransitions:function(e){return e=e.split(" ")},actions:function(a,e){e.length;ie.each(e,function(e,t){var i=ie.extend(!0,{},t),t=i.trigger;a.on(t,function(e){setTimeout(function(e){J.actions.do(e)},i.delay||0,i)})})},smartLinks:function(e){var a=e.attr("href"),t=e.attr("target"),i="";if(t&&-1!==t.indexOf("ls-scroll")){switch(a){case"pagetop":i="Scroll to page top";break;case"pagebottom":i="Scroll to page bottom";break;case"slidertop":i="Scroll to the top of the slider";break;case"":case"sliderbottom":i="Scroll to the bottom of the slider";break;default:i="Scroll to a specified location on the page"}J.layers.set.ariaLabel(e,i),e.on("click."+W,function(e){e.preventDefault();var t,i=document.body.scrollHeight-te.viewport.height;if(a)switch(a){case"pagetop":t=0;break;case"pagebottom":t=i;break;case"slidertop":t=J.slider.offset.top;break;case"":case"sliderbottom":t=J.slider.offset.top+J.slider.height;break;default:t=ie(a).filter(":visible").last().length?ie(a).filter(":visible").last().offset().top:J.slider.offset.top+J.slider.height}t+=J.o.scrollModifier,t=Math.min(t,i),t=Math.max(0,t),ee.TweenMax.to("html, body",1,{scrollTop:t,ease:ee.Quint.easeInOut})})}if(-1!==J.defaults.init.controls.indexOf(a.toLowerCase())||a.match(/^\#[0-9]/)){var s=ie.trim(a.toLowerCase().split("#")[1]),r=parseInt(s);switch(s){case"playmedia":i="play active media elements on current slide";break;case"pausemedia":i="pause active media elements on current slide";break;case"prev":i="jump to the previous slide";break;case"next":i="jump to the next slide";break;case"start":i="start slideshow";break;case"stop":i="stop slideshow";break;case"replay":i="replay slide";break;case"reverse":i="reverse slide";break;case"reverse-replay":case"reversereplay":i="reverse, than replay slide";break;default:"number"==typeof r&&r==r&&(i="jump to slide "+r)}J.layers.set.ariaLabel(e,i),e.on("click."+W,function(e){if(e.preventDefault(),-1!==["prev","next","last","first","start","stop"].indexOf(s))J.navigation[s]("clicked");else if("number"==typeof r&&r==r)J.slideshow.changeTo(r,!0,!0);else if(!J.slider.state.changingSlides)switch(s){case"replay":J.api.methods("replay");break;case"reverse":J.api.methods("reverse");break;case"reverse-replay":case"reversereplay":J.api.methods("reverse",!0);break;case"playmedia":J.media.functions.playActiveMedia();break;case"pausemedia":J.media.functions.pauseActiveMedia();break;case"unmute":case"unmutemedia":J.media.unmute.multipleMediaElements()}})}},ariaLabel:function(e,t){e.attr("aria-label")||e.attr("aria-label",t)},linkTo:function(e){for(var t=e.attr("class").split(" "),i=1,a=0;a<t.length;a++)-1!=t[a].indexOf("ls-linkto-")&&(i=parseInt(t[a].split("ls-linkto-")[1]));e.data(J.defaults.init.dataKey).settings.linkedToSlide=i,e.css({cursor:"pointer"}).on("click."+W,function(e){e.preventDefault(),$.layerSlider(ie(this).data(J.defaults.init.dataKey).settings.linkedToSlide)})},wrappers:function(e,t,i){t.original;t.is.slideBackground||t.is.backgroundVideo?(t.elements.$bgWrapper=e.closest(".ls-bg-wrap"),t.elements.$bgOuterWrapper=e.closest(".ls-bg-outer")):(t.elements.$wrapper=e.closest(".ls-in-out"),t.elements.$wrapper.data(J.defaults.init.dataKey,{}),t.settings.wrapperData=t.elements.$wrapper.data(J.defaults.init.dataKey),t.elements.$clipWrapper=e.closest(".ls-clip"),t.elements.$clipWrapper.data(J.defaults.init.dataKey,{}),t.settings.clipWrapperData=t.elements.$clipWrapper.data(J.defaults.init.dataKey),t.elements.$loopWrapper=e.closest(".ls-loop"),t.elements.$loopWrapper.data(J.defaults.init.dataKey,{}),t.settings.loopWrapperData=t.elements.$loopWrapper.data(J.defaults.init.dataKey)),t.parallax.enabled&&(t.elements.$parallaxWrapper=e.closest(".ls-parallax"),t.elements.$parallaxWrapper.data(J.defaults.init.dataKey,{parallax:{}}),t.settings.parallaxWrapperData=t.elements.$parallaxWrapper.data(J.defaults.init.dataKey),J.transitions.layers.parallax.addLayer(t.elements.$parallaxWrapper,t.settings.parallaxWrapperData.parallax,t,i)),t.scroll.enabled&&(t.elements.$scrollWrapper=e.closest(".ls-scroll"),t.elements.$scrollTransformWrapper=e.closest(".ls-scroll-transform"),t.elements.$scrollWrapper.data(J.defaults.init.dataKey,{scroll:{}}),t.settings.scrollWrapperData=t.elements.$scrollWrapper.data(J.defaults.init.dataKey),J.transitions.layers.scroll.addLayer(t.elements.$scrollWrapper,t.settings.scrollWrapperData.scroll,t,i)),t.hover.enabled&&(!J.slides[i].data.globalhover||t.hover.skipGlobal&&J.slides[i].data.globalhover)&&J.transitions.layers.hover.set(e,t),t.elements.$outerWrapper=e.closest(".ls-z"),t.elements.$outerStyleWrapper=t.elements.$outerWrapper.find("> .ls-wrapper"),t.settings.trmask&&t.elements.$outerStyleWrapper.css("overflow","hidden"),t.elements.$outerWrapper.attr("data-slide-index",i),t.elements.$innerWrapper=e.closest(".ls-wrapper").addClass("ls-inner-wrapper"),t.elements.$_innerWrappers=t.elements.$outerWrapper.find(".ls-wrapper"),t.elements.$_allWrappers=t.elements.$outerWrapper.add(t.elements.$_innerWrappers),t.elements.$_outerWrappers=t.elements.$outerWrapper.add(t.elements.$outerWrapper.find(".ls-wrapper:not(.ls-inner-wrapper)"))},singleLayer:function(e,t){J.layers.set.style(e),J.layers.set.properties(e,t),J.layers.set.dataAttribute("add",e,"hidden"),e.data("hasBeenSet",!0)},style:function(e){var t,i,a,s,r,o,n,l,d,p,c,u,h=e[0],m=e.data(J.defaults.init.dataKey),f=h.style,g=J.layers,y=null,v=null,x=null,w=null,b=0,S=0,T=!1,k=parseFloat(h.style.fontSize)||36,C=h.getBoundingClientRect(),P=!e.is("img, picture")&&window.LS_previewZoom||1,O=""!==f.paddingLeft?g.getStyle(f.paddingLeft,J.slider.initial.percW):g.getStyle(e.css("padding-left"),J.slider.initial.percW),I=""!==f.paddingRight?g.getStyle(f.paddingRight,J.slider.initial.percW):g.getStyle(e.css("padding-right"),J.slider.initial.percW),L=""!==f.paddingTop?g.getStyle(f.paddingTop,J.slider.initial.percH):g.getStyle(e.css("padding-top"),J.slider.initial.percH),$=""!==f.paddingBottom?g.getStyle(f.paddingBottom,J.slider.initial.percH):g.getStyle(e.css("padding-bottom"),J.slider.initial.percH),W=""!==f.marginLeft?g.getStyle(f.marginLeft,J.slider.initial.percW):g.getStyle(e.css("margin-left"),J.slider.initial.percW),M=""!==f.marginRight?g.getStyle(f.marginRight,J.slider.initial.percW):g.getStyle(e.css("margin-right"),J.slider.initial.percW),B=""!==f.marginTop?g.getStyle(f.marginTop,J.slider.initial.percH):g.getStyle(e.css("margin-top"),J.slider.initial.percH),_=""!==f.marginBottom?g.getStyle(f.marginBottom,J.slider.initial.percH):g.getStyle(e.css("margin-bottom"),J.slider.initial.percH);h.style.margin="0",n=""!==f.borderLeftWidth?g.getStyle(f.borderLeftWidth,J.slider.initial.percW):g.getStyle(e.css("border-left-width"),J.slider.initial.percW),d=""!==f.borderRightWidth?g.getStyle(f.borderRightWidth,J.slider.initial.percW):g.getStyle(e.css("border-right-width"),J.slider.initial.percW),l=""!==f.borderTopWidth?g.getStyle(f.borderTopWidth,J.slider.initial.percH):g.getStyle(e.css("border-top-width"),J.slider.initial.percH),p=""!==f.borderBottomWidth?g.getStyle(f.borderBottomWidth,J.slider.initial.percH):g.getStyle(e.css("border-bottom-width"),J.slider.initial.percH),1!==J.media.properties.$allMediaLayers.filter(e).length&&!e.children("iframe").length||(c=(V=e.children()).attr("width")?V.attr("width"):V.width(),u=V.attr("height")?V.attr("height"):V.height(),300===parseInt(c)&&150===parseInt(u)&&(c=640,u=360),""!==h.style.width&&"auto"!==h.style.width||e.css("width",c),""!==h.style.height&&"auto"!==h.style.height||e.css("height",u),"100%"===f.width&&"100%"===f.height&&(f.left="50%",f.top="50%"),T=c/u,V.css({width:"100%",height:"100%"}));var D,F,A,z,V=m.attributes;e.is("img, picture")&&(z=(F=(D=e.is("img")?e:e.find("img")).data("preloadedWidth"))/(A=D.data("preloadedHeight")),(!f.width&&!f.height||"auto"===f.width&&"auto"===f.height)&&V&&(V.width&&V.height?(t=-1===V.width.indexOf("%")?parseInt(V.width):(b=parseInt(V.width),g.getStyle(V.width,J.slider.initial.percW)),i=-1===V.height.indexOf("%")?parseInt(V.height):(S=parseInt(V.height),g.getStyle(V.height,J.slider.initial.percH))):V.maxWidth&&(e[0].style.width=V.maxWidth+"px",t=V.maxWidth,i=e.height()))),D=C.width?C.width/P:(C.right-C.left)/P,P=C.height?C.height/P:(C.bottom-C.top)/P,t||(t=f.width,-1!==f.width.indexOf("%")&&(b=parseInt(f.width)),t=(t=""!==t&&"auto"!==t?g.getStyle(t,J.slider.initial.percW):D-J.layers.toNum(O,k)-J.layers.toNum(I,k)-J.layers.toNum(n,k)-J.layers.toNum(d,k))||"auto"),i||(i=f.height,-1!==f.height.indexOf("%")&&(S=parseInt(f.height)),i=(i=""!==i&&"auto"!==i?g.getStyle(i,J.slider.initial.percH):P-J.layers.toNum(L,k)-J.layers.toNum($,k)-J.layers.toNum(l,k)-J.layers.toNum(p,k))||"auto"),0===parseInt(i)&&(e.addClass("ls-force-height-auto"),parseInt(h.getBoundingClientRect().height),e.removeClass("ls-force-height-auto")),T=T||("auto"!==t&&"auto"!==i?t/i:"auto"),!e.is("img, picture")||f.width||f.height||V&&(!V||V.width||V.height)||F===t&&A===i||(F!==t?i=(t=5<F?F:t)/(T=5<F?z:T):A!==i&&(t=(i=5<A?A:i)*(T=5<A?z:T))),parseFloat(e.css("opacity")),(A=f.clipPath||f.clip||!1)&&(m.clip.style.css.clipPath=A,m.clip.userDefined=!0,f.clip="",f.clipPath=""),z=f.webkitFilter||f.filter;var N,R,E,H;f.minWidth&&""!==f.minWidth&&(-1!==f.minWidth.indexOf("%")&&(a=parseInt(f.minWidth)),y=-1!==f.minWidth.indexOf("em")?f.minWidth:parseFloat(f.minWidth)),f.minHeight&&""!==f.minHeight&&(-1!==f.minHeight.indexOf("%")&&(s=parseInt(f.minHeight)),v=-1!==f.minHeight.indexOf("em")?f.minHeight:parseFloat(f.minHeight)),f.maxWidth&&""!==f.maxWidth&&(-1!==f.maxWidth.indexOf("%")&&(r=parseInt(f.maxWidth)),x=-1!==f.maxWidth.indexOf("em")?f.maxWidth:parseFloat(f.maxWidth)),f.maxHeight&&""!==f.maxHeight&&(-1!==f.maxHeight.indexOf("%")&&(o=parseInt(f.maxHeight)),w=-1!==f.maxHeight.indexOf("em")?f.maxHeight:parseFloat(f.maxHeight)),m.original={clip:A,left:f.left||"0",top:f.top||"0",width:"auto"!==t?parseFloat(t):"auto",height:"auto"!==i?parseFloat(i):"auto",minWidth:y,minHeight:v,maxWidth:x,maxHeight:w,sWidth:f.width,sHeight:f.height,percentWidth:b,percentHeight:S,percentMinWidth:a||null,percentMinHeight:s||null,percentMaxWidth:r||null,percentMaxHeight:o||null,backgroundImage:f.backgroundImage,backgroundSize:f.backgroundSize||"cover",backgroundRepeat:f.backgroundRepeat,backgroundPosition:f.backgroundPosition,ratio:T,paddingLeft:O,paddingTop:L,paddingRight:I,paddingBottom:$,marginLeft:W,marginRight:M,marginTop:B,marginBottom:_,borderLeftWidth:n,borderTopWidth:l,borderRightWidth:d,borderBottomWidth:p,borderRadius:(N=e,E=["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"],H="",(R=f).borderRadius&&(-1===R.borderRadius.indexOf("/")?ie.each(["border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius"],function(e,t){e=R[E[e]],t=N.css(t),t=ie.trim(void 0!==e&&e.length?e:t);-1==t.indexOf(" ")&&-1==t.indexOf("em")&&-1==t.indexOf("%")&&(t=parseInt(t)),H+=t+" "}):H=R.borderRadius),ie.trim(H)),fontSize:k,lineHeight:h.style.lineHeight,letterSpacing:h.style.letterSpacing,textStrokeWidth:h.style.textStrokeWidth||h.style.webkitTextStrokeWidth,color:e.css("color"),boxShadow:h.style.boxShadow,textShadow:h.style.textShadow,zIndex:parseInt(e.css("z-index"))||"auto",filter:z,backgroundColor:e.css("background-color"),dataLS:e.attr("data-ls")||"",styles:e.attr("style")||""},f.width&&"auto"!==f.width||(m.original.autoWidth=!0),f.height&&"auto"!==f.height||(m.original.autoHeight=!0),f.zIndex="auto",m.responsive={left:f.left?parseInt(f.left):0,top:f.top?parseInt(f.top):0,width:t,height:i},e.is("picture")&&e.css({width:m.original.width,height:m.original.height})},parseScaleProperties:function(e){for(var t=[{x:"scalexin",y:"scaleyin",s:"scalein"},{x:"textscalexin",y:"textscaleyin",s:"textscalein"},{x:"loopscalex",y:"loopscaley",s:"loopscale"},{x:"textscalexout",y:"textscaleyout",s:"textscaleout"},{x:"scalexout",y:"scaleyout",s:"scaleout"},{x:"hoverscalex",y:"hoverscaley",s:"hoverscale"}],i=0;i<t.length;i++){var a=t[i];e[a.x]&&e[a.y]&&e[a.x]===e[a.y]&&(e[a.s]=e[a.x],delete e[a.x],delete e[a.y])}return e},properties:function(e,t,i){t=t||e.data("slideIndex");var a=e.data(J.defaults.init.dataKey);e.data("ls");if(a.is.textLayer=!(e.is("img, picture")||a.is.mediaLayer||a.is.backgroundVideo),(a.self=e).data("ls")){for(var s,r,o,n,l=J.defaults.layer.keys,d=e.data("ls").split(";"),p={},c=0;c<d.length;c++)ie.trim(d[c])&&(s=d[c].indexOf(":"),r=d[c].substring(0,s).trim(),o=d[c].substring(s+1).trim(),r&&(p[r]=o));for(n in d=null,p=this.parseScaleProperties(p)){var u=null,h=null,m=null,f=null,g=!1,y=!1;if(r=n.toLowerCase(),o=p[n],-1==["thumbnail","poster"].indexOf(r)&&(o=o.toLowerCase()),(r=r.replace("split","text")).match(/(text)/)&&(y=!0),void 0!==l[r]){if(u=l[r][0],f=o,"overlay"!==r&&(f=J.functions.convert.properties(o)),-1!==r.indexOf("mirror")&&(f=J.layers.set.mirrorTransitions(o)),!o||!/random/.test(o)&&o.charAt(0)!==J.defaults.init.openingBracket||/parallax/.test(r)||(g=!0),g&&(y||/offset/.test(r)||(f=J.functions.convert.randomProperties(f,u,e,a)),a.should.update||(a.should.update=!0)),"number"==typeof f&&u.match(/(duration|startat|shift|delay|wait)/i)&&(f/=1e3),r.match(/(fade)(.+)/))switch(f){case!0:f=0;break;case!1:f=1}void 0!==(m=l[r][1])?""!==f?"object"==typeof f&&-1==r.indexOf("mirror")?r.match(/(text)/)?m.match(/(converted)/i)?a[m][u]=f:a[m].cycle[u]=f:(h=J.functions.convert.properties(ie.trim(f[0])),J.debugMode&&J.debug.add("warn","layerInit.prop1",[r,f,h]),"number"==typeof h&&u.match(/(duration|startat|shift|delay)/i)&&(h/=1e3),a[m][u]=h):y&&g?a[m].random[u]=f:a[m][u]=f:J.debugMode&&J.debug.add("warn","layerInit.prop2",r):a[u][r]=f}else"clip"===r?(a.original.clip=o,a.clip.style.css.clipPath=o):J.debugMode&&J.debug.add("warn","layerInit.prop4",r)}}if(J.browser.isOld&&(a.in.enabled=!0,a.textIn.enabled=!1,a.textOut.enabled=!1,a.textIn.type=null,a.textOut.type=null),a.in.enabled&&(a.inLayerTo.ease=a.inLayerStyleTo.ease=a.inClipTo.ease=J.functions.convert.easing(a.in.ease)),void 0!==a.inLayerStyleShouldBeConvertedFrom.borderRadius&&(a.inLayerStyleShouldBeConvertedTo.borderRadius=a.original.borderRadius),void 0!==a.outLayerStyleShouldBeConvertedTo.borderRadius&&(a.outLayerStyleShouldBeConvertedFrom.borderRadius=a.original.borderRadius),a.inLayerStyleFromCSS.backgroundColor&&(a.inLayerStyleToCSS.backgroundColor=a.original.backgroundColor),a.outLayerStyleToCSS.backgroundColor&&(a.outLayerStyleFromCSS.backgroundColor=a.original.backgroundColor),a.inLayerStyleFromCSS.color&&(a.inLayerStyleToCSS.color=a.original.color),a.outLayerStyleToCSS.color&&(a.outLayerStyleFromCSS.color=a.original.color),void 0!==a.inLayerStyleShouldBeConvertedFrom.width&&(a.inLayerStyleShouldBeConvertedTo.width=a.original.width),void 0!==a.outLayerStyleShouldBeConvertedTo.width&&(a.outLayerStyleShouldBeConvertedFrom.width=a.original.width),void 0!==a.inLayerStyleShouldBeConvertedFrom.height&&(a.inLayerStyleShouldBeConvertedTo.height=a.original.height),void 0!==a.outLayerStyleShouldBeConvertedTo.height&&(a.outLayerStyleShouldBeConvertedFrom.height=a.original.height),void 0!==a.out.showUntil&&0!==a.out.showUntil&&(a.out.startAt="transitioninend + "+a.out.showUntil),-1!==a.out.startAt.indexOf("slidechangeonly")&&"slidechangeonly"!==a.out.startAt&&(a.out.startAt="slidechangeonly"),a.out.enabled&&(a.outLayerTo.ease=a.outLayerStyleTo.ease=a.outClipTo.ease=J.functions.convert.easing(a.out.ease)),ie.isNumeric(a.loop.count)&&(0<a.loop.count||-1===a.loop.count)&&!1!==a.loop.enabled?(a.loop.enabled=!0,a.loopTo.ease=a.loopClipTo.ease=J.functions.convert.easing(a.loop.ease),-1!==a.loop.count?a.loop.yoyo?a.loop.repeat=2*a.loop.count-1:a.loop.repeat=a.loop.count-1:a.loop.repeat=-1):a.loop.enabled=!1,(!ie.isEmptyObject(a.hoverToCSS)||a.hoverShouldBeConverted.x||a.hoverShouldBeConverted.y||a.hoverShouldBeConverted.borderRadius||a.hoverShouldBeConverted.filter)&&!1!==a.hover.enabled?(a.hover.enabled=!0,a.hover.easeOut||(a.hover.easeOut=a.hover.easeIn),a.hover.easeIn=J.functions.convert.easing(a.hover.easeIn),a.hover.easeOut=J.functions.convert.easing(a.hover.easeOut,!0),a.hover.durationOut||(a.hover.durationOut=a.hover.durationIn),ee.TweenMax.set(e[0],{autoCSS:!1,css:{transformPerspective:a.hoverShouldBeConverted.transformPerspective}})):a.hover.enabled=!1,a.hover.skipGlobal&&e.attr("data-ls-skipglobalhover",""),a.parallax.level&&ie.isNumeric(a.parallax.level)&&0!==a.parallax.level&&!1!==a.parallax.enabled?("cursor"!==a.parallax.event&&a.parallax.event||!a.parallax.reset||e.attr("data-ls-parallax-reset",""),a.parallax.enabled=!0):a.parallax.enabled=!1,a.is.slideBackground){var v={scale:1,rotation:0};if(J.slides[t].kenBurns.zoom&&(a.kenBurns=J.slides[t].kenBurns),a.kenBurns.zoom){switch(a.kenBurns.from={},a.kenBurns.to={},a.kenBurns.zoom){case"out":a.kenBurns.from.scale=a.kenBurns.scale||1,a.kenBurns.from.rotation=a.kenBurns.rotation||0,a.kenBurns.to=v;break;case"in":a.kenBurns.from=v,a.kenBurns.to.scale=a.kenBurns.scale||1,a.kenBurns.to.rotation=a.kenBurns.rotation||0}delete a.kenBurns.scale,delete a.kenBurns.rotation}else a.kenBurns.from=v,a.kenBurns.to=v;ie.isEmptyObject(J.slides[t].filter)||(J.slides[t].filter.from&&(a.filter.values.bgFrom=J.transitions.layers.filters.convert(J.slides[t].filter.from)),J.slides[t].filter.to&&(a.filter.values.bgTo=J.transitions.layers.filters.convert(J.slides[t].filter.to)))}if(a.textIn.type&&-1===J.defaults.layer.splitTypeKeys.indexOf(a.textIn.type)&&(J.debugMode&&J.debug.add("warn","layerInit.splitType3a",[e[0].tagName,a.textIn.type]),delete a.textIn.type,delete a.textIn.ns,a.textIn.enabled=!1),a.textOut.type&&-1===J.defaults.layer.splitTypeKeys.indexOf(a.textOut.type)&&(J.debugMode&&J.debug.add("warn","layerInit.splitType3b",[e[0].tagName,a.textOut.type]),delete a.textOut.type,delete a.textOut.ns,a.textOut.enabled=!1),a.textIn.type||a.textOut.type){var x=0;if(a.is.textLayer?(a.textIn.type&&(a.textIn.enabled=!0,a.textInNodesTo.ease=J.functions.convert.easing(a.textInNodesTo.ease),a.textIn.split=a.textIn.type.split("_")[0],e.children().length&&J.debugMode&&(x=1)),a.textOut.type&&!J.o.inLayerPreview&&(a.textOut.enabled=!0),a.textOut.enabled&&(a.textOutNodesTo.ease=J.functions.convert.easing(a.textOutNodesTo.ease)),a.textOut.enabled&&a.textOut.type.split("_")[0]!==a.textIn.split&&(a.textIn.split+=", "+a.textOut.type.split("_")[0],e.children().length&&J.debugMode&&(x=1)),-1!==a.textIn.split.indexOf("chars")&&-1===a.textIn.split.indexOf("words")&&(a.textIn.split+=", words"),-1!==a.textIn.split.indexOf("words")&&-1===a.textIn.split.indexOf("lines")&&(a.textIn.split+=", lines")):(delete a.textIn.type,delete a.textOut.type,delete a.textIn.ns,delete a.textOut.ns,J.debugMode&&(x=2)),J.debugMode&&0!==x&&t&&!i)switch(x){case 1:J.debug.add("warn","layerInit.splitType1",[e.prop("nodeName"),t]);break;case 2:J.debug.add("warn","layerInit.splitType2",[t,e.prop("nodeName")])}}(a.inClipFromCSS.clipPath||a.outClipToCSS.clipPath||a.loopClipToCSS.clipPath||a.clip.style.css.clipPath!==a.clip.default.css.clipPath||a.clip.userDefined)&&(a.inClipFromCSS.clipPath?"="==a.inClipFromCSS.clipPath&&(a.inClipFromCSS.clipPath=a.clip.style.css.clipPath):a.inClipFromCSS.clipPath=a.clip.default.css.clipPath,a.outClipToCSS.clipPath?"="==a.outClipToCSS.clipPath&&(a.outClipToCSS.clipPath=a.clip.style.css.clipPath):a.outClipToCSS.clipPath=a.clip.default.css.clipPath,a.clip.enabled=!0),a.textIn.enabled&&"textmask"===a.textInLayerStyle.overflow&&e.attr("data-ls-mask-text-in",""),a.textOut.enabled&&"textmask"===a.textOutLayerStyle.overflow&&e.attr("data-ls-mask-text-out",""),a.in.enabled&&a.inLayerToCSS.hasOwnProperty("scale")&&(delete a.inLayerToCSS.scaleX,delete a.inLayerToCSS.scaleY),a.out.enabled&&a.outLayerToCSS.hasOwnProperty("scale")&&(delete a.outLayerToCSS.scaleX,delete a.outLayerToCSS.scaleY),a.inLayerStyleShouldBeConvertedFrom.filter&&(a.filter.values.in=J.transitions.layers.filters.convert(a.inLayerStyleShouldBeConvertedFrom.filter)),a.filter.values.style=J.transitions.layers.filters.convert(a.original.filter),a.outLayerStyleShouldBeConvertedTo.filter&&(a.filter.values.out=J.transitions.layers.filters.convert(a.outLayerStyleShouldBeConvertedTo.filter)),a.loopLayerShouldBeConverted.filter&&(a.filter.values.loop=J.transitions.layers.filters.convert(a.loopLayerShouldBeConverted.filter)),a.hoverShouldBeConverted.filter&&(a.filter.values.hover=J.transitions.layers.filters.convert(a.hoverShouldBeConverted.filter)),a.in.enabled||(a.in.duration=0),a.textIn.enabled||(a.textIn.duration=0),a.loop.enabled||(a.loop.duration=0),a.textOut.enabled||(a.textOut.duration=0),a.out.enabled||(a.out.duration=0),e.attr("data-ls-slidein",t),void 0!==a.settings.static&&"none"!==a.settings.static?(0!==(x=parseInt(a.settings.static))&&"forever"!==a.settings.static?(e.attr("data-ls-slideout",x),a.settings.slideOut=x):a.settings.slideOut=0,a.is.static=!0,e.attr("data-ls-static","1")):e.attr("data-ls-slideout",t),a.is.mediaLayer&&e.children("video, audio").length&&J.media.html5.singleInit(e.children("video, audio").eq(0)),a.is.backgroundVideo&&a.styleSettings.overlay&&e.find(".ls-bg-video-overlay").css({backgroundImage:"url("+a.styleSettings.overlay+")"}),a.styleSettings.minfontsize&&(a.styleSettings.minfontsize=parseFloat(a.styleSettings.minfontsize)),a.styleSettings.minmobilefontsize&&(a.styleSettings.minmobilefontsize=parseFloat(a.styleSettings.minmobilefontsize)),J.slider.isPopup&&(a.is.pinned=!1),a.is.pinned&&(J.slider.hasPinnedLayers=!0,J.slider.$layersWrapper.is(".ls-layers-clip")||J.slider.$layersWrapper.addClass("ls-layers-clip"),!te.isMobile&&!J.browser.isSafari||J.slider.$layersWrapper.is(".ls-m-layers-clip")||J.slider.$layersWrapper.addClass("ls-m-layers-clip")),a.styleSettings.smartbg&&(e.attr("data-ls-smart-bg","").css("--bgs",a.original.backgroundSize),a.styleSettings.smartbg=a.styleSettings.smartbg.toString(),a.is.smartBG=!0)},dataAttribute:function(e,t,i){var a=t.add(t.closest(".ls-wrapper.ls-z"));switch(e){case"remove":a.removeAttr("data-ls-"+i);break;case"add":"active"===i&&a.removeAttr("data-ls-hidden"),"hidden"===i&&a.removeAttr("data-ls-active data-ls-animating-in data-ls-text-animating-in data-ls-animating-out data-ls-text-animating-out"),-1===i.indexOf("in")&&-1===i.indexOf("out")||(a.removeAttr("data-ls-active"),a.removeAttr("data-ls-hidden")),"active"===i&&t.is("[data-ls-animating-in], [data-ls-text-animating-in]")||"hidden"===i&&t.is("[data-ls-animating-out], [data-ls-text-animating-out]")||a.attr("data-ls-"+i,"")}}},get:function(e){var t,i,a,s,r,o=this.$all;return e&&(t="in",a=i="",s=':not(".ls-bg")',r=':not(".ls-bg-video")',-1==(e=e.toLowerCase()).indexOf("bgvideo")&&-1==e.indexOf("backgroundvideo")||(r="",e=e.replace("bgvideo","").replace("backgroundvideo","")),-1!=e.indexOf("video")&&(a+=", > video",e=e.replace("video","")),-1!=e.indexOf("audio")&&(a+=", > audio",e=e.replace("audio","")),-1!=e.indexOf("html5")&&(a+=", > video, > audio",e=e.replace("html5","")),-1!=e.indexOf("youtube")&&(a+=', > iframe[src*="youtube-nocookie.com"], > iframe[src*="youtube.com"], > iframe[src*="youtu.be"], > iframe[data-src*="youtube-nocookie.com"], > iframe[data-src*="youtube.com"], > iframe[data-src*="youtu.be"]',e=e.replace("youtube","")),-1!=e.indexOf("vimeo")&&(a+=', > iframe[src*="player.vimeo"], > iframe[data-src*="player.vimeo"]',e=e.replace("vimeo","")),","==a.charAt(0)&&(a=a.substring(2,a.length)),-1!=e.indexOf("out")&&(t="out"),-1==e.indexOf("img")&&-1==e.indexOf("image")||(i="img"),-1==e.indexOf("bg")&&-1==e.indexOf("background")&&-1==e.indexOf("bgonly")||(s=""),o=-1!=e.indexOf("current")?o.filter(i+"[data-ls-slide"+t+'="'+J.slides.current.index+'"]'+s+r):-1!=e.indexOf("next")?o.filter(i+"[data-ls-slide"+t+'="'+J.slides.next.index+'"]'+s+r):o.filter(i+s+r),-1!=e.indexOf("notactive")&&(o=o.filter(".ls-bg, .ls-bg-video, :hidden"),e=e.replace("notactive","")),-1!=e.indexOf("active")&&(o=o.filter(':visible:not(.ls-bg, .ls-bg-video), [data-ls-static="1"]:not([data-ls-hidden])'),e=e.replace("active","")),-1!=e.indexOf("notstatic")&&(o=o.filter(':not([data-ls-static="1"])'),e=e.replace("notstatic","")),-1!=e.indexOf("static")&&(o=o.filter('[data-ls-static="1"]'),e=e.replace("static","")),-1!=e.indexOf("bgonly")&&(o=o.filter(".ls-bg"),e=e.replace("bgonly","")),-1!=e.indexOf("countdown")&&(o=o.filter("[data-countdown]"),e=e.replace("countdown","")),""!==a&&(o=o.find(a))),o},update:{data:function(e,t,i){var a,s,r;switch(e instanceof jQuery||(e=ie(e)),i&&e.attr("data-ls",i).data("ls",i),a=(s=e.data(J.defaults.init.dataKey)).is.onSlide,r=s.original,t){default:case"transitions":s.settings.timelineIsCalculated=!1,J.layers.set.properties(e,a,!0);break;case"all":e.data(J.defaults.init.dataKey,new J.defaults.layer.options(e,a)),(s=e.data(J.defaults.init.dataKey)).original=r,J.layers.set.properties(e,a,!0),J.layers.set.wrappers(e,s,a)}}},wrap:function(u,e){var t,h;J.slides[u].wrapped||"wrapping"===J.slides[u].wrapped||(J.slides[u].wrapped="wrapping",t=e?25:0,e=J.slides[u].$layers,0===(h=e.length)?J.slides[u].wrapped=!0:e.each(function(p,c){J.timeouts["slide-"+u+"-layer-"+p]=setTimeout(function(){delete J.timeouts["slide-"+u+"-layer-"+p];var e=ie(c),t=e,i="",a=!1,s="";e.hasClass("ls-hide-phone")&&(s+=" ls-hide-on-phone"),e.hasClass("ls-hide-tablet")&&(s+=" ls-hide-on-tablet"),e.hasClass("ls-hide-desktop")&&(s+=" ls-hide-on-desktop"),e.removeClass("ls-hide-phone ls-hide-tablet ls-hide-desktop"),t.is("a")&&1===t.children().length&&(a=!0,e=t.find(".ls-layer"));var r,o,n,l=e.data(J.defaults.init.dataKey);if(!l)return!0;d=e.data("$layerGroup")||J.slider.$layersWrapper,l.is.backgroundVideo?d=J.slider.$bgVideosWrapper:l.is.slideBackground&&(d=J.slider.$slideBGWrapper),e.data("hasBeenSet")||J.layers.set.singleLayer(e,u),l.textIn.split&&(n=new ee.SplitType(e[0],{split:l.textIn.split}),l.textIn.type&&(l.textIn.ns=n[l.textIn.type.split("_")[0]]),l.textOut.type&&(l.textOut.ns=n[l.textOut.type.split("_")[0]]),-1!==e.css("background-clip").indexOf("text")&&(r=e.find(".ls-ch"),o=e.find(".ls-wd"),n=e.find(".ls-ln"),l.textIn.$nodesForBackgroundClip=r.length?r:o.length?o:n,r.length?e.addClass("ls-has-chars"):o.length?e.addClass("ls-has-words"):e.addClass("ls-has-lines"),l.textIn.$nodesForBackgroundClip.each(function(){var e=ie(this),t=ie.trim(e.text()),i=document.createTextNode(t),t=ie(i),i=ie('<div class="ls-textnode-bgclip-wrap"><div class="ls-textnode"></div></div>').css({backgroundColor:l.original.backgroundColor,backgroundImage:l.original.backgroundImage,backgroundSize:l.original.backgroundSize,backgroundRepeat:l.original.backgroundRepeat,backgroundPosition:l.original.backgroundPosition});e.css({verticalAlign:"top"}).text(""),t.clone().appendTo(e).wrap('<div class="ls-textnode-dummy"></div>'),t.appendTo(e).wrap(i)}))),i=l.is.slideBackground||l.is.backgroundVideo?'<div class="ls-wrapper ls-bg-outer"><div class="ls-wrapper ls-bg-wrap"></div></div>':(l.clip.enabled&&(i='<div class="ls-wrapper ls-clip"></div>'),l.loop.enabled&&(i='<div class="ls-wrapper ls-loop">'+i+"</div>"),l.scroll.enabled&&(i='<div class="ls-wrapper ls-scroll"><div class="ls-wrapper ls-scroll-transform">'+i+"</div></div>"),'<div class="ls-wrapper ls-in-out" style="">'+i+"</div>"),l.parallax.enabled&&(i='<div class="ls-wrapper ls-parallax">'+i+"</div>"),""!==(i='<div class="ls-wrapper ls-z'+(l.is.pinned?" ls-pinned":"")+'">'+i+"</div>")?e.appendTo(d).wrap(i):e.appendTo(d),!0===a&&t.addClass("ls-layer-link").appendTo(e.parent());var d={},a=e.css("mix-blend-mode");a&&"normal"!==a&&(d["mix-blend-mode"]=a,e.css("mix-blend-mode","normal")),l.original.customZIndex=1;a=parseInt(l.original.zIndex);l.is.backgroundVideo||l.is.slideBackground?d={zIndex:l.original.customZIndex}:(a=a||p+101,d.zIndex=a,l.original.customZIndex=a),J.browser.isSafari&&(d.transform="translateZ(0)"),J.layers.set.wrappers(e,l,u),l.elements.$outerWrapper.css(d).addClass(s),l.styleSettings.pointerevents&&l.elements.$innerWrapper.css("pointer-events","none"),l.is.slideBackground&&l.elements.$bgWrapper.css({backgroundColor:J.slides[u].data.backgroundColor}),J.layers.$all=J.layers.$all.add(e),J.slides[u].$layers=J.slides[u].$layers.not(t),p===h-1&&($.children(".ls-slide").eq(u-1).empty(),J.slides[u].wrapped=!0)},t*(p+1))}))}},J.slideshow={direction:"next",nextLoop:0,firstStart:!0,forceFastChange:!1,sequence:{normal:[],randomized:[]},state:{changed:-1,running:!0,paused:!1,pausedByVideo:!1,pausedByHover:!1,pausedByLastCycle:!1},should:{change:!1,start:!1,stop:!1},isPaused:function(){return this.state.paused||this.state.pausedByVideo||this.state.pausedByHover||this.state.pausedByPerformance},init:function(){1==J.slides.count&&ie.extend(J.o,{autoStart:!1,navPrevNext:!1,navStartStop:!1,navButtons:!1,cycles:-1,forceLoopNum:!1,autoPauseSlideshow:!0,firstSlide:1,thumbnailNavigation:"disabled"}),J.o.autoStart&&1!=J.slides.count||J.functions.setStates(this,{running:!1,paused:!0}),this.set.pauseOnHover(),this.set.sequences()},set:{pauseOnHover:function(){J.o.pauseOnHover=!0===J.o.pauseOnHover?"slideshowOnly":J.o.pauseOnHover,!1!==J.o.pauseOnHover&&$.on("mouseenter."+W,function(){J.slider.state.inFullscreen||(J.functions.setStates(J.slideshow,{pausedByHover:!0}),"slideshowOnly"!==J.o.pauseOnHover&&J.transitions.layers.timeline.pause())}).on("mouseleave."+W,function(){var e=1;J.transitions._slideTimeline&&J.transitions._slideTimeline.duration()>J.transitions.layers.timeline.totalDuration&&(e=J.transitions.layers.timeline.totalDuration/J.transitions._slideTimeline.duration()),J.functions.setStates(J.slideshow,{pausedByHover:!1}),ie("body").hasClass("ls-unselectable")||"slideshowOnly"===J.o.pauseOnHover||J.o.pauseLayers&&J.slideshow.isPaused()||J.transitions.layers.timeline.resume(),J.transitions._slideTimeline&&J.transitions.layers.timeline.state.finished&&J.transitions._slideTimeline.progress()<e&&J.functions.setStates(J.transitions.layers.timeline,{finished:!1}),J.slideshow.start()})},sequences:function(){for(var e=0;e<J.slides.count;e++)J.slideshow.sequence.normal[e]=e+1;J.slideshow.sequence.randomized=J.functions.shuffleArray(ie.merge([],J.slideshow.sequence.normal))},normalizedSequence:function(){var e=J.o.shuffleSlideshow?"randomized":"normal",t=J.slideshow.sequence[e],i=J.slideshow.sequence[e].length,a=t.indexOf(J.slides.first.index);J.slideshow.sequence.normalized=[];for(var s=a;s<i;s++)J.slideshow.sequence.normalized.push(t[s]);for(var r=0;r<a;r++)J.slideshow.sequence.normalized.push(t[r])},prevNext:function(e){switch("prev"!==e&&"first"!==e||!J.o.twoWaySlideshow?J.slideshow.direction="next":J.slideshow.direction="prev",e){case"prev":J.slideshow.changeTo(J.slideshow.get.slideInSequence("prev"),!0);break;case"next":J.slideshow.changeTo(J.slideshow.get.slideInSequence("next"),!0);break;case"first":J.slideshow.changeTo(1,!0);break;case"last":J.slideshow.changeTo(J.slides.count,!0)}}},get:{sequence:function(){var e="normal";return J.o.playByScroll?e="normalized":J.o.shuffleSlideshow&&(e="randomized"),e},slideInSequence:function(e){var t=J.slideshow.sequence[this.sequence()],i=t.indexOf(J.slides.current.index);switch(e){case"prev":return 0===i?t[t.length-1]:t[i-1];case"next":return i===t.length-1?t[0]:t[i+1];default:return t[e]}},indexOfSlideInSequence:function(e){return J.slideshow.sequence[this.sequence()].indexOf(e)}},cycles:{set:function(){0<J.o.cycles&&(J.slideshow.curCycle=1,J.slideshow.cycleSlideIndex=J.slideshow.get.indexOfSlideInSequence(J.slides.first.index))},check:function(e){if(J.slideshow.get.indexOfSlideInSequence(e)===J.slideshow.cycleSlideIndex)return++J.slideshow.curCycle===J.o.cycles+1}},start:function(e){!this.isPaused()&&J.transitions._slideTimeline&&J.transitions.layers.timeline.state.finished&&this.changeTo(J.slides.next.index)},stop:function(){J.functions.setStates(this,{running:!1,paused:!0})},changeTo:function(e,t,i){if(!document.body.contains(L))return!1;if(J.slides.current.index===e&&!J.slideshow.forceSlideChangeTo)return!1;if(J.slideshow.forceSlideChangeTo=null,!this.firstStart&&J.api.hasEvent("slideChangeWillStart")){var a=J.api.triggerEvent("slideChangeWillStart",J.api.eventData());if(!1===a)return;ie.isNumeric(a)&&(e=parseInt(a))}e>J.slides.count||e<1?J.debugMode&&(J.debug.add("group","slideshow"),J.debug.add("warn","slideshow.invalidSlideIndex",[e,J.slides.count]),J.debug.groupEnd()):J.slider.isBusy()||J.slideshow.state.pausedByVideo&&!t?!J.slider.state.preloadingImages&&J.slider.state.animatingSlides&&J.transitions._slideTransition&&(J.slideshow.forceSlideChangeTo=e,J.slideshow.should.change=!0,J.transitions._slideTransition.progress(1),J.transitions.timelines.set("currentforce",function(e,t){e.progress(1)})):(J.functions.setStates(J.transitions.layers.timeline,{finished:!1}),J.slideshow.should.change=!1,J.debugMode&&J.debug.add("group","slideshow"),t?(i&&(J.navigation.direction=J.slides.current.index>e?"prev":"next"),J.o.twoWaySlideshow&&("prev"===J.navigation.direction?J.slideshow.direction="prev":J.slideshow.direction="next"),J.debugMode&&(J.debug.add("log","slideshow.changedByUser",!1),J.o.twoWaySlideshow&&J.debug.add("log","slideshow.setdir",J.slideshow.direction))):J.navigation.direction=J.slideshow.direction,J.transitions.timers.reverse(),J.gui.media.hideUnmute(),J.slides.set.nextSlideIndex(e),J.debugMode&&(J.debug.add("log","slideshow.change",[J.slides.current.index,J.slides.next.index,J.slideshow.direction,J.navigation.direction]),J.debug.groupEnd()),J.functions.setStates(this,{pausedByVideo:!1,changed:++this.state.changed}),J.functions.setStates(J.slider,{changingSlides:!0}),J.slider.state.animatingSlides&&J.transitions._slideTransition&&J.transitions.timelines.set("currentforce",function(e,t){e.progress(1)}),J.preload.imagesOfSlide(J.slides.next.index,function(){J.transitions.start()}))},forceStop:function(){J.navigation.stop(),ie.each(J.timeouts,function(e,t){clearTimeout(J.timeouts[e])}),J.transitions.timers.stop(),J.transitions._slideTimeline.stop(),J.transitions._slideTimelineAlternate.stop(),J.functions.setStates(J.transitions.layers.timeline,{stopped:!0,running:!1}),$.find("*").stop(!0,!1).dequeue()},restart:function(){$.find("*").stop(),J.navigation.change(J.slides.current.index,J.slideshow.direction)}},J.media={properties:{$allMediaLayers:ie(),playingInCurSlide:0,endedInCurSlide:0,userDidUnmute:!1},init:function(){J.functions.setStates(J.slider,{waitingForYouTube:!1,waitingForVimeo:!1}),J.media.properties.allowToUnmute=!0,J.media.youtube.init(),J.media.vimeo.init(),J.media.html5.init()},isPlayable:function(e,t){return!(e.is("[data-ls-animating-out], [data-ls-hidden]")&&!t.is.backgroundVideo)},get:function(e){var t,i=J.media.properties.$allMediaLayers;return e&&(-1!==(e=e.toLowerCase()).indexOf("notbg")&&(i=i.not(".ls-bg-video")),-1!==e.indexOf("active")&&(i=i.filter("[data-ls-active], [data-ls-animating-in], [data-ls-text-animating-in]")),-1!==e.indexOf("notstatic")&&(i=i.not("[data-ls-static]")),t=i.find("video, audio, iframe"),-1!==e.indexOf("notplaying")?t=t.filter("[data-ls-not-playing]"):-1!==e.indexOf("playing")&&(t=t.filter("[data-ls-playing]")),-1!==e.indexOf("allowtounmute")&&(t=t.filter("[data-ls-allow-to-unmute]")),-1!==e.indexOf("mutedbybrowser")&&(t=t.filter("[data-ls-muted-by-browser]"))),t},set:{backgroundVideo:function(e,t){0===e.children(".ls-vpcontainer").length&&(e=ie("<div>").addClass("ls-vpcontainer").appendTo(e),t.mediaSettings.poster&&ie("<div>").appendTo(e).addClass("ls-videopreview").attr({style:"background-image: url("+t.mediaSettings.poster+")"}))},customPoster:function(e,t){return t.mediaSettings.poster||!1},dataAttribute:function(e,t){e.removeAttr("data-ls-playing data-ls-not-playing"),e.attr("data-ls-"+t,""),J.media.functions.checkSlideshowWaiting()},mediaElements:function(e,t,i){var a=ie("<div>").addClass("ls-vpcontainer").appendTo(e);!("autoplay"in i.mediaSettings)&&J.o.autoPlayVideos||i.mediaSettings.autoplay?"instant"===i.mediaSettings.autoplay?e.addClass("ls-instant-autoplay"):e.addClass("ls-autoplay"):ie("<div>").appendTo(a).addClass("ls-playvideo"),t.is("iframe")&&i.mediaProperties.poster&&ie("<div>").appendTo(a).addClass("ls-videopreview").attr({style:"background-image: url("+i.mediaProperties.poster+")"}),t.is("video, audio")&&i.mediaProperties.poster&&ie("<div>").appendTo(a).addClass("ls-videopreview").attr({style:"background-image: url("+i.mediaProperties.poster+")"})},properties:function(e,t,i){switch(i.is.mediaLayer=!0,i.mediaProperties.type){case"youtube":i.is.backgroundVideo?(i.mediaProperties.options=ie.extend(!0,{loop:1,playlist:i.mediaProperties.embedID,controls:0,autoplay:0,showinfo:!1,modestbranding:1,thumbnail:!1},J.media[i.mediaProperties.type].defaults,i.mediaProperties.embedOptions),J.media.set.backgroundVideo(e,i)):(i.mediaProperties.options=ie.extend(!0,{},J.media[i.mediaProperties.type].defaults,i.mediaProperties.embedOptions,{autoplay:0}),J.media.set.mediaElements(e,t,i));break;case"vimeo":i.is.backgroundVideo?(i.mediaProperties.options=ie.extend(!0,{loop:1,byline:0,portrait:0,title:0,fun:0},J.media[i.mediaProperties.type].defaults,i.mediaProperties.embedOptions),J.media.set.backgroundVideo(e,i)):(i.mediaProperties.options=ie.extend(!0,{},J.media[i.mediaProperties.type].defaults,i.mediaProperties.embedOptions,{autoplay:0}),J.media.set.mediaElements(e,t,i));break;case"html5":t.attr("autoplay")&&(t.removeAttr("autoplay"),e.attr("data-ls",e.attr("data-ls")+" autoplay: true;")),i.is.backgroundVideo?(t.removeAttr("controls"),t[0].muted=!0,t.attr("loop","")):"controls"in i.mediaSettings&&"auto"!==i.mediaSettings.controls&&(i.mediaSettings.controls?(t.attr("controls",""),t.removeAttr("nocontrols")):t.removeAttr("controls")),"volume"in i.mediaSettings&&(i.mediaSettings.volume<0?i.mediaSettings.volume=0:100<i.mediaSettings.volume&&(i.mediaSettings.volume=100),t[0].volume=i.mediaSettings.volume/100),"muted"in i.mediaSettings&&(i.mediaSettings.muted?t[0].muted=!0:t[0].muted=!1),"loop"in i.mediaSettings&&(i.mediaSettings.loop?t.attr("loop",""):t.removeAttr("loop")),i.is.backgroundVideo?J.media.set.backgroundVideo(e,i):J.media.set.mediaElements(e,t,i)}},thumbnail:function(e,t){switch(t){case"show":e.stop(!0,!0).fadeIn(J.transitions.media.defaults.fadeIn);break;case"hide":e.stop(!0,!0).delay(J.transitions.media.defaults.delay).fadeOut(J.transitions.media.defaults.fadeOut)}}},events:{start:function(e,t,i){var a;J.api.hasEvent("mediaDidStart")&&!i.mediaProperties.alreadyStarted&&J.media.functions.allowedToPlay(e,i)&&((a=J.api.eventData()).event.target=e[0],J.api.triggerEvent("mediaDidStart",a),i.mediaProperties.alreadyStarted=!0)},stop:function(e,t,i){var a;J.api.hasEvent("mediaDidStop")&&i.mediaProperties.alreadyStarted&&((a=J.api.eventData()).event.target=e[0],J.api.triggerEvent("mediaDidStop",a),i.mediaProperties.alreadyStarted=!1)}},unmute:{set:function(e,t,i){("youtube"!==t&&"vimeo"!==t||J.media.properties.allowToUnmute)&&J.gui.media.showUnmute(),e.attr("data-ls-allow-to-unmute",""),i&&e.attr("data-ls-muted-by-browser","")},multipleMediaElements:function(){var e=$.find("[data-ls-allow-to-unmute]");(J.o.rememberUnmuteState?e:e.filter("[data-ls-playing], [data-ls-muted-by-browser]")).each(function(){J.media.unmute.singleMediaElement(ie(this))}),J.gui.media.hideUnmute(),J.media.properties.userDidUnmute=!0},singleMediaElement:function(e){var t,i=e.closest(".ls-layer").data(J.defaults.init.dataKey),a=!1;switch(i.mediaProperties.type){case"youtube":J.media.properties.allowToUnmute&&(a=!0,i.mediaProperties.player.unMute());break;case"vimeo":J.media.properties.allowToUnmute&&(a=!0,t=i.mediaSettings.volume?i.mediaSettings.volume/100:1,i.mediaProperties.player.setVolume(t));break;case"html5":a=!0,e[0].muted=!1}a&&e.removeAttr("data-ls-muted-by-browser data-ls-allow-to-unmute")}},functions:{allowedToPlay:function(e,t){e=e.closest(".ls-layer");return!!(J.slides.current.index===parseInt(e.attr("data-ls-slidein"))||t.is.backgroundVideo&&J.slides.next.index===parseInt(e.attr("data-ls-slidein"))||e.attr("data-ls-static")&&void 0!==e.attr("data-ls-active"))},playActiveMedia:function(e){var t,i;J.media.get("notbg,active").each(function(){if(t=ie(this).closest(".ls-layer"),i=t.data(J.defaults.init.dataKey),e){if(!i.mediaProperties.pausedByPerformance)return!0;i.mediaProperties.pausedByPerformance=!1}if(J.media.isPlayable(t,i))if(i.mediaProperties.alreadyStarted)switch(i.mediaProperties.type){case"youtube":i.mediaProperties.player.playVideo();break;case"vimeo":i.mediaProperties.player.play();break;case"html5":i.mediaProperties.$media[0].play()}else ie(this).parent().find(".ls-vpcontainer").trigger("playMedia")})},stopSingleMedia:function(e,t){var i=t.mediaProperties.$media;J.media[t.mediaProperties.type].stop(e,i,t,!0),J.media.functions.mediaEnded(i,e,t)},pauseActiveMedia:function(e){var t;J.media.get("notbg,active,playing").each(function(){switch(t=ie(this).closest(".ls-layer").data(J.defaults.init.dataKey),e&&(t.mediaProperties.pausedByPerformance=!0),t.mediaProperties.type){case"youtube":t.mediaProperties.player.pauseVideo();break;case"vimeo":t.mediaProperties.player.pause();break;case"html5":this.pause()}})},urlToObject:function(e){var a={},e=e.split("?")[1];return e&&e.split("#")[0].replace(/([^=&]+)=([^&]*)/g,function(e,t,i){a[decodeURIComponent(t)]=ie.isNumeric(decodeURIComponent(i))?parseInt(decodeURIComponent(i)):decodeURIComponent(i)}),a},checkSlideshowState:function(e,t){!t.is.static&&J.o.autoPauseSlideshow&&(J.functions.setStates(J.slideshow,{pausedByVideo:!0}),"auto"==J.o.autoPauseSlideshow&&J.media.properties.playingInCurSlide++)},checkSlideshowWaiting:function(){var e=J.media.properties.playingInCurSlide,t=J.media.get("notbg,active,notstatic,notplaying"),i=t.length;i===e&&0<i&&J.slideshow.state.pausedByVideo&&J.o.autoPauseSlideshow&&!J.timeouts.pausedVideos&&J.slideshow.state.running?J.timeouts.pausedVideos=setTimeout(function(){J.slideshow.state.running?t.each(function(){J.media.functions.mediaEnded(ie(this),ie(this).closest(".ls-layer"),ie(this).closest(".ls-layer").data(J.defaults.init.dataKey))}):J.slideshow.state.pausedByVideo=!1},5e3):J.timeouts.pausedVideos&&(clearTimeout(J.timeouts.pausedVideos),delete J.timeouts.pausedVideos)},playIfAllowed:function(e){var t=e.data(J.defaults.init.dataKey);t.is.mediaLayer&&(te.isMobile&&($.hasClass("ls-device-is-phone")&&t.elements.$outerWrapper.hasClass("ls-hide-on-phone")||$.hasClass("ls-device-is-tablet")&&t.elements.$outerWrapper.hasClass("ls-hide-on-tablet"))||("autoplay"in t.mediaSettings||!J.o.autoPlayVideos)&&!t.mediaSettings.autoplay||e.find(".ls-vpcontainer").trigger("playMedia"))},stop:function(a,e){var a=void 0===a||a,t=!!e&&e.children("iframe, video, audio").first(),e=!!e&&e.data(J.defaults.init.dataKey).mediaProperties.type;"youtube"!==e&&t||(t||J.layers.get("current,out,youtube")).each(function(){var e=ie(this),t=e.closest(".ls-layer"),i=t.data(J.defaults.init.dataKey);J.media.youtube.stop(t,e,i,a)}),"vimeo"!==e&&t||(t||J.layers.get("current,out,vimeo")).each(function(){var e=ie(this),t=e.closest(".ls-layer"),i=t.data(J.defaults.init.dataKey);J.media.vimeo.stop(t,e,i,a)}),"html5"!==e&&t||(t||J.layers.get("current,out,html5")).each(function(){var e=ie(this),t=e.closest(".ls-layer"),i=t.data(J.defaults.init.dataKey);J.media.html5.stop(t,e,i,a)}),J.media.properties.playingInCurSlide=Math.max(--J.media.properties.playingInCurSlide,0),J.media.properties.endedInCurSlide=Math.max(--J.media.properties.endedInCurSlide,0)},mediaEnded:function(e,t,i){"auto"!=J.o.autoPauseSlideshow||i.is.backgroundVideo||(i.is.static||J.media.properties.endedInCurSlide++,J.media.properties.endedInCurSlide==J.media.properties.playingInCurSlide&&0!==J.media.properties.playingInCurSlide&&J.functions.setStates(J.slideshow,{pausedByVideo:!1})),J.media.set.dataAttribute(e,"not-playing"),J.media.events.stop(e,t,i)},removeFromTimeline:function(e){J.transitions._slideTimeline.kill(null,e.closest(".ls-in-out")[0]),J.transitions._slideTimelineAlternate.kill(null,e.closest(".ls-in-out")[0])}},youtube:{defaults:{autoplay:0,playsinline:1,wmode:"opaque",html5:1,enablejsapi:1,version:3,rel:0},init:function(){var f=0;this.$medias=J.slider.$hiddenWrapper.find('iframe[src*="youtube-nocookie.com"], iframe[src*="youtube.com"], iframe[src*="youtu.be"], iframe[data-src*="youtube-nocookie.com"], iframe[data-src*="youtube.com"], iframe[data-src*="youtu.be"]').each(function(){var e=ie(this).attr({id:"ls-youtube-"+ ++f,allow:"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; fullscreen",allowfullscreen:""}),t=e.closest(".ls-layer");t.data("hasBeenSet")||J.layers.set.singleLayer(t);var i,a,s,r,o,n,l,d,p=t.data(J.defaults.init.dataKey),c=e.attr("src")||e.attr("data-src"),u=J.media.functions.urlToObject(c),h=(c=c.split("?")[0].split("//")[1]).split("/"),m=h[h.length-1],h=J.media.set.customPoster(t,p);p.mediaProperties={type:"youtube",$media:e,embedURL:c,embedID:m,embedOptions:u},h?(p.mediaProperties.poster=h,J.media.set.properties(t,e,p)):(i=["default.jpg","mqdefault.jpg","hqdefault.jpg","sddefault.jpg","maxresdefault.jpg"],a="https://img.youtube.com/vi/"+c.split("embed/")[1].split("?")[0]+"/",s=a+J.o.youtubePreview,o=r=0,n=function(){this.width>r&&(r=this.width,s=this.src),d()},l=function(){d()},d=function(){(o+=1)===i.length&&(p.mediaProperties.poster=s,J.media.set.properties(t,e,p))},i.forEach(function(e){var t=new Image;t.addEventListener("error",l,!1),t.addEventListener("load",n,!1),t.src=a+e}))}),this.$medias.length&&(J.media.properties.$allMediaLayers=J.media.properties.$allMediaLayers.add(this.$medias.closest(".ls-layer")),J.timeouts.loadYouTube=Math.floor(Date.now()/1e3),window.YT||ie("<script>").attr({src:"https://www.youtube.com/iframe_api",type:"text/javascript"}).appendTo("head"),window.onYouTubeIframeAPIReady=function(){window._layerSlider.globals.youTubeIsReady=!0},J.intervals.isYouTubeReady=setInterval(function(){window.YT&&1===window.YT.loaded||window._layerSlider.globals.youTubeIsReady||3<Math.floor(Date.now()/1e3)-J.timeouts.loadYouTube?(clearInterval(J.intervals.isYouTubeReady),delete J.intervals.isYouTubeReady,delete J.timeouts.loadYouTube,J.media.youtube.$medias.closest(".ls-layer").each(function(){var e=ie(this),t=e.find("iframe"),i=e.data(J.defaults.init.dataKey);e.on("playMedia."+W+" click."+W,".ls-vpcontainer",function(){J.media.set.thumbnail(ie(this),"hide"),J.media.functions.checkSlideshowState(e,i),t.hide(),J.media.youtube.play(e,i.mediaProperties.$media,i,i.mediaProperties.embedURL),setTimeout(function(){t.show()},10)}).on("playBackgroundVideo."+W,function(){J.media.youtube.play(e,i.mediaProperties.$media,i,i.mediaProperties.embedURL),J.layers.set.dataAttribute("add",e,"active")}).on("stopBackgroundVideo."+W,function(){J.media.youtube.stop(e,i.mediaProperties.$media,i,!0),J.layers.set.dataAttribute("add",e,"hidden")}).on("preloadBackgroundVideo."+W,function(){J.media.youtube.createPlayer(e,i.mediaProperties.$media,i,i.mediaProperties.embedURL,!0)})}),J.functions.setStates(J.slider,{waitingForYouTube:!1})):J.functions.setStates(J.slider,{waitingForYouTube:!0})},25))},createPlayer:function(t,i,a,s,r){a.mediaProperties.playerState="initializing","controls"in a.mediaSettings&&(a.mediaProperties.options.controls=a.mediaSettings.controls?1:0),"loop"in a.mediaSettings&&(a.mediaProperties.options.loop=a.mediaSettings.loop?1:0),"showinfo"in a.mediaSettings&&(a.mediaProperties.options.showinfo=a.mediaSettings.showinfo?1:0),a.mediaProperties.options.loop?a.mediaProperties.options.playlist=a.mediaProperties.embedID:delete a.mediaProperties.options.playlist,0===a.mediaProperties.options.showinfo&&(a.mediaProperties.options.modestbranding=1),i.attr("src","https://"+s+"?"+jQuery.param(a.mediaProperties.options)).on("load",function(){a.mediaProperties.player=new YT.Player(i[0],{events:{onReady:function(e){a.mediaProperties.playerState="ready",a.mediaSettings.volume&&a.mediaProperties.player.setVolume(a.mediaSettings.volume),r&&!a.mediaProperties.shouldPlay||(J.media.youtube.play(t,i,a,s),a.mediaProperties.shouldPlay=!1)},onStateChange:function(e){switch(e.data){case 0:a.mediaProperties.options.loop&&1===a.mediaProperties.options.loop||J.media.functions.mediaEnded(i,t,a);break;case 1:J.media.events.start(i,t,a),a.mediaProperties.lastStarted=Date.now();break;case 2:case-1:a.mediaProperties.firstState&&0!==a.mediaProperties.lastState&&1!==a.mediaProperties.lastState||a.mediaProperties.lastStarted&&(Date.now(),a.mediaProperties.lastStarted)}1===e.data?J.media.set.dataAttribute(i,"playing"):J.media.set.dataAttribute(i,"not-playing"),J.media.youtube.savePlayerState(a,e.data)}}})})},savePlayerState:function(e,t){e.mediaProperties.firstState||(e.mediaProperties.firstState=t),e.mediaProperties.lastState=t},play:function(e,t,i,a,s){void 0!==t.attr("data-ls-muted-by-browser")&&J.media.unmute.singleMediaElement(t),i.mediaProperties.player?i.mediaProperties.player.playVideo?(!i.is.backgroundVideo||"muted"in i.mediaSettings||i.mediaProperties.player.mute(),i.mediaSettings.muted?J.o.rememberUnmuteState&&J.media.properties.userDidUnmute||(i.mediaProperties.player.mute(),"offertounmute"==i.mediaSettings.muted&&J.media.unmute.set(t,i.mediaProperties.type)):s&&(i.mediaProperties.player.mute(),J.media.unmute.set(t,i.mediaProperties.type,!0)),J.media.functions.allowedToPlay(t,i)?J.media.isPlayable(e,i)&&i.mediaProperties.player.playVideo():J.media.youtube.stop(e,t,i,!0)):i.mediaProperties.shouldPlay=!0:i.mediaProperties.playerState?i.mediaProperties.shouldPlay=!0:this.createPlayer(e,t,i,a)},stop:function(e,t,i,a){i.mediaProperties.player&&(i.mediaProperties.player.pauseVideo&&i.mediaProperties.player.pauseVideo(),a&&i.mediaProperties.player.seekTo&&(i.mediaProperties.player.seekTo(0),i.mediaProperties.player.pauseVideo()),i.is.backgroundVideo||J.media.set.thumbnail(e.find(".ls-vpcontainer"),"show"),J.media.events.stop(t,e,i))}},vimeo:{defaults:{autoplay:0,autopause:0,wmode:"opaque",playsinline:1},init:function(){var d,e,t=this.$medias=J.slider.$hiddenWrapper.find('iframe[src*="player.vimeo"], iframe[data-src*="player.vimeo"]'),p=J.slider.$hiddenWrapper.find('.ls-slide:eq(0) iframe[src*="player.vimeo"], .ls-slide:eq(0) iframe[data-src*="player.vimeo"]').length;t.length&&(J.timeouts.loadVimeo=Math.floor(Date.now()/1e3),J.media.properties.$allMediaLayers=J.media.properties.$allMediaLayers.add(t.closest(".ls-layer")),d=0,ie("<script>").attr({src:"https://player.vimeo.com/api/player.js",type:"text/javascript"}).appendTo("head"),J.intervals.isVimeoReady=setInterval(function(){J.functions.setStates(J.slider,{waitingForVimeo:!0}),(window.Vimeo||3<Math.floor(Date.now()/1e3)-J.timeouts.loadVimeo)&&(clearInterval(J.intervals.isVimeoReady),delete J.intervals.isVimeoReady,delete J.timeouts.loadVimeo,e())},25),e=function(){var l=0;J.media.vimeo.$medias.each(function(){var t=ie(this).attr({id:"ls-vimeo-"+ ++d,allow:"accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture; fullscreen",allowfullscreen:""}),i=t.closest(".ls-layer");i.data("hasBeenSet")||J.layers.set.singleLayer(i);var a=i.data(J.defaults.init.dataKey),e=t.attr("src")||t.attr("data-src"),s=J.media.functions.urlToObject(e),r=e.split("video/")[1].split("?")[0],o="https://vimeo.com/api/oembed.json?url="+encodeURIComponent("https://vimeo.com/video/"+r+(s.h?"?h="+s.h:"")),e=e.split("?")[0].split("//")[1];J.media.vimeo.defaults.player_id="ls-vimeo-"+d;r=J.media.set.customPoster(i,a);a.mediaProperties={type:"vimeo",$media:t,embedURL:e,embedOptions:s};function n(e){a.mediaProperties.poster=e,(0===p||0<p&&++l==p)&&(window._layerSlider.globals.vimeoIsReady=!0),J.media.set.properties(i,t,a)}r?n(r):ie.getJSON(o,function(e){n(e.thumbnail_url)}),i.on("playMedia."+W+" click."+W,".ls-vpcontainer",function(){J.media.set.thumbnail(ie(this),"hide"),J.media.functions.checkSlideshowState(i,a),J.media.vimeo.play(i,t,a,e)}).on("playBackgroundVideo."+W,function(){J.media.vimeo.play(i,t,a,e),J.layers.set.dataAttribute("add",i,"active")}).on("stopBackgroundVideo."+W,function(){J.media.vimeo.stop(i,t,a,!0),J.layers.set.dataAttribute("add",i,"hidden")}).on("preloadBackgroundVideo."+W,function(){J.media.vimeo.createPlayer(i,t,a,e,!0)})}),J.functions.setStates(J.slider,{waitingForVimeo:!1})})},createPlayer:function(e,t,i,a,s){i.mediaProperties.playerState="initializing";"controls"in i.mediaSettings&&(i.mediaProperties.options.controls=i.mediaSettings.controls?1:0,delete i.mediaSettings.controls),"loop"in i.mediaSettings&&(i.mediaProperties.options.loop=i.mediaSettings.loop?1:0),"showinfo"in i.mediaSettings&&(i.mediaSettings.showinfo?(i.mediaProperties.options.byline=1,i.mediaProperties.options.portrait=1,i.mediaProperties.options.title=1):(i.mediaProperties.options.byline=0,i.mediaProperties.options.portrait=0,i.mediaProperties.options.title=0),delete i.mediaProperties.options.showinfo),"volume"in i.mediaSettings&&(0===i.mediaSettings.volume?(i.mediaSettings.volume=100,i.mediaSettings.muted=!0):(i.mediaSettings.volume<0||100<i.mediaSettings.volume)&&(i.mediaSettings.volume=100)),i.mediaSettings.muted&&(i.mediaProperties.options.muted=1),t.attr("src","https://"+a+"?"+jQuery.param(i.mediaProperties.options)),i.mediaProperties.player=new Vimeo.Player(t[0]),i.mediaProperties.player.on("play",function(){J.media.set.dataAttribute(t,"playing"),J.media.events.start(t,e,i)}),i.mediaProperties.player.on("pause",function(){J.media.set.dataAttribute(t,"not-playing")}),i.mediaProperties.player.on("ended",function(){J.media.functions.mediaEnded(t,e,i)}),i.mediaProperties.player.ready().then(function(){i.mediaProperties.playerState="ready",i.mediaSettings.volume&&!i.mediaSettings.muted&&ie.isNumeric(i.mediaSettings.volume)&&0<=i.mediaSettings.volume&&i.mediaSettings.volume<=100&&i.mediaProperties.player.setVolume(i.mediaSettings.volume/100),s||J.media.vimeo.play(e,t,i,a)})},play:function(t,i,a,s,e){void 0!==i.attr("data-ls-muted-by-browser")&&J.media.unmute.singleMediaElement(i),a.mediaProperties.player?(!a.is.backgroundVideo||"muted"in a.mediaSettings||a.mediaProperties.player.setVolume(0),a.mediaSettings.muted&&(J.o.rememberUnmuteState&&J.media.properties.userDidUnmute?J.o.rememberUnmuteState&&J.media.properties.userDidUnmute&&(a.mediaProperties.player.setVolume(a.mediaSettings.volume/100||1),delete a.mediaSettings.muted):(a.mediaProperties.player.setVolume(0),"offertounmute"==a.mediaSettings.muted&&J.media.unmute.set(i,a.mediaProperties.type))),e?(a.mediaProperties.player.setVolume(0),J.media.unmute.set(i,a.mediaProperties.type,!0)):a.mediaProperties.player.getVolume().then(function(e){0==e&&"offertounmute"==a.mediaSettings.muted&&J.media.unmute.set(i,a.mediaProperties.type)}),J.media.functions.allowedToPlay(i,a)?a.mediaProperties.player.play().then(function(){}).catch(function(e){switch(e.name){case"PasswordError":window.console&&(console.error(J.defaults.slider.errorText),console.error("Vimeo video is password protected and may cause playback issues."));break;case"PrivacyError":window.console&&(console.error(J.defaults.slider.errorText),console.error("Vimeo video is private and may cause playback issues."));break;default:J.media.vimeo.play(t,i,a,s,!0)}}):J.media.vimeo.stop(t,i,a,!0)):this.createPlayer(t,i,a,s)},stop:function(e,t,i,a){i.mediaProperties.player&&(i.mediaProperties.player.pause(),a&&i.mediaProperties.player.setCurrentTime(0),i.is.backgroundVideo||J.media.set.thumbnail(e.find(".ls-vpcontainer"),"show"),J.media.events.stop(t,e,i))}},html5:{singleInit:function(t){var e,i,a,s,r=t.closest(".ls-layer"),o=r.data(J.defaults.init.dataKey),n=t.find("source"),l=J.media.set.customPoster(r,o);o.mediaProperties={type:"html5",$media:t,poster:l||t.attr("poster")},t.removeAttr("poster"),0<n.length?n.each(function(){void 0!==(e=ie(this).attr("type"))&&!1!==e||(i=ie(this).attr("src"),a=i.split("."),s=a[a.length-1].toLowerCase(),ie(this).attr("type",ie(this).parent()[0].tagName.toLowerCase()+"/"+s)),""!==t[0].canPlayType(ie(this).attr("type"))&&(o.mediaProperties.canBePlayed=!0)}):void 0!==t.attr("src")&&!1!==t.attr("src")&&(i=t.attr("src"),a=i.split("."),s=a[a.length-1].toLowerCase(),""!==t[0].canPlayType(t[0].tagName.toLowerCase()+"/"+s)&&(o.mediaProperties.canBePlayed=!0)),o.mediaProperties.canBePlayed&&(J.media.set.properties(r,t,o),t.on("ended."+W,function(){J.media.functions.mediaEnded(t,r,o)}).on("play."+W,function(){}).on("playing."+W,function(){J.media.events.start(t,r,o),J.media.set.dataAttribute(t,"playing")}).on("pause."+W,function(){J.media.set.dataAttribute(t,"not-playing")}),r.on("playMedia."+W+" click."+W,".ls-vpcontainer",function(e){J.media.set.thumbnail(ie(this),"hide"),J.media.functions.checkSlideshowState(r,o),J.media.html5.play(r,t,o)}).on("playBackgroundVideo."+W,function(){J.media.html5.play(r,t,o),J.layers.set.dataAttribute("add",r,"active")}).on("stopBackgroundVideo."+W,function(){J.media.html5.stop(r,t,o,!0),J.layers.set.dataAttribute("add",r,"hidden")}))},init:function(){var e,t=J.slider.$hiddenWrapper.find("video, audio");J.media.properties.$allMediaLayers=J.media.properties.$allMediaLayers.add(t.closest(".ls-layer")),t.length&&(e=0,t.each(function(){ie(this).closest(".ls-layer").data(J.defaults.init.dataKey).is.mediaLayer=!0,ie(this).attr("id","ls-html5-"+ ++e).attr("playsinline",""),ie(this)[0].pause()}))},play:function(e,t,i){var a;i.mediaProperties.canBePlayed&&(void 0!==t.attr("data-ls-muted-by-browser")&&J.media.unmute.singleMediaElement(t),i.mediaSettings.muted&&(J.o.rememberUnmuteState&&J.media.properties.userDidUnmute?J.o.rememberUnmuteState&&J.media.properties.userDidUnmute&&(t[0].muted=!1):(t[0].muted=!0,"offertounmute"==i.mediaSettings.muted&&J.media.unmute.set(t,i.mediaProperties.type))),void 0!==(a=t[0].play())&&a.then(function(e){}).catch(function(e){"NotAllowedError"===e.name&&(t[0].muted=!0,t[0].play(),t[0].paused&&J.functions.setStates(J.slideshow,{pausedByVideo:!1}),J.media.unmute.set(t,i.mediaProperties.type,!0))}))},stop:function(e,t,i,a){i.mediaProperties.canBePlayed&&(t[0].pause(),a&&(t[0].currentTime=0),i.is.backgroundVideo||J.media.set.thumbnail(ie(this),"show"),J.media.events.stop(t,e,i))}}},J.yourLogo={init:function(){J.o.yourLogo&&(this.$element=ie("<img>").addClass("ls-yourlogo").appendTo($).attr("style",J.o.yourLogoStyle).css({visibility:"hidden",display:"bock"}).on("load."+W,function(){var e=J.yourLogo.$element?500:0;J.timeouts.yourLogo=setTimeout(function(){delete J.timeouts.yourLogo,J.yourLogo.$element.data("originalWidth",J.yourLogo.$element.width()),J.yourLogo.$element.data("originalHeight",J.yourLogo.$element.height()),"auto"!=J.yourLogo.$element.css("left")&&J.yourLogo.$element.data("originalLeft",J.yourLogo.$element[0].style.left),"auto"!=J.yourLogo.$element.css("right")&&J.yourLogo.$element.data("originalRight",J.yourLogo.$element[0].style.right),"auto"!=J.yourLogo.$element.css("top")&&J.yourLogo.$element.data("originalTop",J.yourLogo.$element[0].style.top),"auto"!=J.yourLogo.$element.css("bottom")&&J.yourLogo.$element.data("originalBottom",J.yourLogo.$element[0].style.bottom),!1!==J.o.yourLogoLink&&ie("<a>").appendTo($).attr("href",J.o.yourLogoLink).attr("target",J.o.yourLogoTarget).css({textDecoration:"none",outline:"none"}).append(J.yourLogo.$element),J.yourLogo.$element.css({display:"none",visibility:"visible"}),J.yourLogo.resize()},e)}).attr("src",J.o.yourLogo))},resize:function(){this.$element.css({width:this.$element.data("originalWidth")*J.resize.ratio,height:this.$element.data("originalHeight")*J.resize.ratio}),this.$element.fadeIn(300);var e="auto",t="auto",i="auto",a="auto",e=this.$element.data("originalLeft")&&-1!=this.$element.data("originalLeft").indexOf("%")?$.width()/100*parseFloat(this.$element.data("originalLeft"))-this.$element.width()/2+parseInt($.css("padding-left")):parseInt(this.$element.data("originalLeft"))*J.resize.ratio,t=this.$element.data("originalRight")&&-1!=this.$element.data("originalRight").indexOf("%")?$.width()/100*parseFloat(this.$element.data("originalRight"))-this.$element.width()/2+parseInt($.css("padding-right")):parseInt(this.$element.data("originalRight"))*J.resize.ratio,i=this.$element.data("originalTop")&&-1!=this.$element.data("originalTop").indexOf("%")?$.height()/100*parseFloat(this.$element.data("originalTop"))-this.$element.height()/2+parseInt($.css("padding-top")):parseInt(this.$element.data("originalTop"))*J.resize.ratio,a=this.$element.data("originalBottom")&&-1!=this.$element.data("originalBottom").indexOf("%")?$.height()/100*parseFloat(this.$element.data("originalBottom"))-this.$element.height()/2+parseInt($.css("padding-bottom")):parseInt(this.$element.data("originalBottom"))*J.resize.ratio;this.$element.css({left:e,right:t,top:i,bottom:a})}},J.gui={navigation:{init:function(){J.o.navPrevNext&&this.prevNext.init(),(J.o.navStartStop||J.o.navButtons)&&this.bottom.init()},prevNext:{init:function(){ie('<a class="ls-gui-element ls-nav-prev" aria-label="jump to the previous slide" href="#" />').on("click."+W,function(e){e.preventDefault(),$.layerSlider("prev")}).appendTo($),ie('<a class="ls-gui-element ls-nav-next" aria-label="jump to the next slide" href="#" />').on("click."+W,function(e){e.preventDefault(),$.layerSlider("next")}).appendTo($),J.o.hoverPrevNext&&this.setHover()},setHover:function(){$.find(".ls-nav-prev, .ls-nav-next").css({display:"none"}),$.on("mouseenter."+W,function(){J.gui.navigation.forceHide||$.find(".ls-nav-prev, .ls-nav-next").stop(!0,!0).fadeIn(300)}).on("mouseleave."+W,function(){$.find(".ls-nav-prev, .ls-nav-next").stop(!0,!0).fadeOut(300)})}},bottom:{init:function(){this.wrapper=ie('<div class="ls-gui-element ls-bottom-nav-wrapper" />').appendTo($),J.o.navButtons&&"always"!=J.o.thumbnailNavigation&&this.bullets.init(),J.o.navStartStop?this.createStartStop():"always"!=J.o.thumbnailNavigation&&this.createSides(),J.o.hoverBottomNav&&"always"!=J.o.thumbnailNavigation&&this.setHover(),"always"==J.o.thumbnailNavigation&&(this.wrapper.addClass("ls-above-thumbnails"),this.thumbnails.init())},bullets:{init:function(){var t=this;ie('<span class="ls-bottom-slidebuttons" />').appendTo($.find(".ls-bottom-nav-wrapper"));for(var e=0;e<J.slides.count;e++){var i=ie('<a href="#" aria-label="jump to slide '+(e+1)+'" />').appendTo($.find(".ls-bottom-slidebuttons")).data("index",e+1).on("click."+W,function(e){e.preventDefault(),$.layerSlider(ie(this).data("index"))});"hover"==J.o.thumbnailNavigation&&i.on("mouseenter."+W,function(){var e=ie(this);$.find(".ls-thumbnail-hover-img").css({left:parseInt(t.hoverWrapper.css("padding-left")),top:parseInt(t.hoverWrapper.css("padding-top"))}),t.hoverImage.on("load."+W,function(){0===ie(this).width()?t.hoverImage.css({position:"relative",margin:"0 auto",left:"auto"}):t.hoverImage.css({position:"absolute",marginLeft:-ie(this).width()/2,left:"50%"}),t.hoverImage.css("display","none").stop(!0,!0).fadeIn(250)}).attr("src",J.slides[e.data("index")].data.thumbnail),t.hoverWrapper.css({display:"block"}).stop().animate({left:ie(this).position().left+(ie(this).width()-t.hoverWrapper.outerWidth())/2},250),t.hoverWrapperInner.css({display:"none",visibility:"visible"}).stop().fadeIn(250)}).on("mouseleave."+W,function(){t.hoverWrapperInner.stop().fadeOut(250,function(){t.hoverWrapper.css({visibility:"hidden",display:"block"})})})}t.set.active(J.slides.first.index),"hover"==J.o.thumbnailNavigation&&t.set.hover()},set:{active:function(e){void 0===e&&(e=J.slides.current.index),e--,$.find(".ls-bottom-slidebuttons a").removeClass("ls-nav-active"),$.find(".ls-bottom-slidebuttons a:eq( "+e+" )").addClass("ls-nav-active")},hover:function(){var e=J.gui.navigation.bottom.bullets,t=ie('<div class="ls-thumbnail-hover"><div class="ls-thumbnail-hover-inner"><div class="ls-thumbnail-hover-bg"></div><div class="ls-thumbnail-hover-img"><img></div><span></span></div></div>').appendTo($.find(".ls-bottom-slidebuttons"));$.find(".ls-thumbnail-hover, .ls-thumbnail-hover-img").css({width:J.o.tnWidth,height:J.o.tnHeight}),e.hoverWrapper=$.find(".ls-thumbnail-hover"),e.hoverImage=e.hoverWrapper.find("img").css({height:J.o.tnHeight}),e.hoverWrapperInner=$.find(".ls-thumbnail-hover-inner").css({visibility:"hidden",display:"block"}),t.appendTo($.find(".ls-bottom-slidebuttons"))}}},createStartStop:function(){this.buttonStart=ie('<a class="ls-nav-start" aria-label="start slideshow" href="#" />').on("click."+W,function(e){e.preventDefault(),$.layerSlider("start")}).prependTo($.find(".ls-bottom-nav-wrapper")),this.buttonStop=ie('<a class="ls-nav-stop" aria-label="stop slideshow" href="#" />').on("click."+W,function(e){e.preventDefault(),$.layerSlider("stop")}).appendTo($.find(".ls-bottom-nav-wrapper")),J.o.autoStart?this.setStartStop("start"):this.setStartStop("stop")},setStartStop:function(e){if(J.o.navStartStop)switch(e){case"start":this.buttonStart.addClass("ls-nav-start-active"),this.buttonStop.removeClass("ls-nav-stop-active");break;case"stop":this.buttonStart.removeClass("ls-nav-start-active"),this.buttonStop.addClass("ls-nav-stop-active")}},createSides:function(){ie('<span class="ls-nav-sides ls-nav-sideleft" />').prependTo($.find(".ls-bottom-nav-wrapper")),ie('<span class="ls-nav-sides ls-nav-sideright" />').appendTo($.find(".ls-bottom-nav-wrapper"))},setHover:function(){var e=this;e.wrapper.css({display:"none"}),$.on("mouseenter."+W,function(){J.gui.navigation.forceHide||e.wrapper.stop(!0,!0).fadeIn(300)}).on("mouseleave."+W,function(){e.wrapper.stop(!0,!0).fadeOut(300)})},switchHelper:function(e){if(J.o.hoverBottomNav&&!$.hasClass("ls-hover"))switch(e){case"on":J.gui.navigation.bottom.thumbnails.wrapper.css({visibility:"hidden",display:"block"});break;case"off":J.gui.navigation.bottom.thumbnails.wrapper.css({visibility:"visible",display:"none"})}},thumbnails:{init:function(){this.wrapper=ie('<div class="ls-gui-element ls-thumbnail-wrapper '+("contain"!==J.o.tnFillMode?"ls-thumbnail-fill":"")+'"></div>').appendTo($),ie('<div class="ls-thumbnail"><div class="ls-thumbnail-inner"><div class="ls-thumbnail-slide-container"><div class="ls-thumbnail-slide"></div></div></div></div>').appendTo(this.wrapper),this.$element=$.find(".ls-thumbnail-slide-container"),"ontouchstart"in window?this.$element.addClass("ls-touchscroll"):this.$element.on("mouseenter."+W,function(){ie(this).addClass("ls-thumbnail-slide-hover")}).on("mouseleave."+W,function(){ie(this).removeClass("ls-thumbnail-slide-hover"),J.gui.navigation.bottom.thumbnails.scroll()}).on("mousemove."+W,function(e){e=parseInt(e.pageX-ie(this).offset().left)/ie(this).width()*(ie(this).width()-ie(this).find(".ls-thumbnail-slide").width());ie(this).find(".ls-thumbnail-slide").stop().css({marginLeft:e})});for(var e=0;e<J.slides.count;e++){var t=e+1,i=J.slides[t].data.thumbnail,a="";"contain"!==J.o.tnFillMode&&(i="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",a='style="background-image: url('+J.slides[t].data.thumbnail+"); background-size: "+J.o.tnFillMode+';"');i=ie('<a href="#" class="ls-thumb-'+(e+1)+'"  aria-label="jump to slide '+(e+1)+'"><img '+a+' src="'+i+'"></a>');J.slides[t].data.tnAlt&&i.find("img").attr("alt",J.slides[t].data.tnAlt),i.data("index",t).on("click."+W,function(e){e.preventDefault(),$.layerSlider(ie(this).data("index"))}).appendTo($.find(".ls-thumbnail-slide")),"ontouchstart"in window||i.on("mouseenter."+W,function(){ie(this).children().stop().fadeTo(300,J.o.tnActiveOpacity/100)}).on("mouseleave."+W,function(){ie(this).children().hasClass("ls-thumb-active")||ie(this).children().stop().fadeTo(300,J.o.tnInactiveOpacity/100)})}J.gui.navigation.bottom.buttonStart&&J.gui.navigation.bottom.buttonStop&&(J.gui.navigation.bottom.wrapper=ie('<div class="ls-bottom-nav-wrapper ls-below-thumbnails"></div>').appendTo($),J.gui.navigation.bottom.buttonStart.clone().on("click."+W,function(e){e.preventDefault(),$.layerSlider("start")}).appendTo(J.gui.navigation.bottom.wrapper),J.gui.navigation.bottom.buttonStop.clone().on("click."+W,function(e){e.preventDefault(),$.layerSlider("stop")}).appendTo(J.gui.navigation.bottom.wrapper)),J.o.hoverBottomNav&&this.setHover()},setHover:function(){var e=this;e.wrapper.css("display","none"),J.gui.navigation.bottom.wrapper&&(J.gui.navigation.bottom.wrapper="block"==J.gui.navigation.bottom.wrapper.css("display")?J.gui.navigation.bottom.wrapper:$.find(".ls-above-thumbnails"),J.gui.navigation.bottom.wrapper.css("display","none")),$.on("mouseenter."+W,function(){$.addClass("ls-hover"),J.gui.navigation.forceHide||(e.wrapper.stop(!0,!0).fadeIn(300),J.gui.navigation.bottom.wrapper&&J.gui.navigation.bottom.wrapper.stop(!0,!0).fadeIn(300))}).on("mouseleave."+W,function(){$.removeClass("ls-hover"),e.wrapper.stop(!0,!0).fadeOut(300),J.gui.navigation.bottom.wrapper&&J.gui.navigation.bottom.wrapper.stop(!0,!0).fadeOut(300)})},change:function(e){e=e||J.slides.next.index;$.find(".ls-thumbnail-slide a:not(.ls-thumb-"+e+" )").children().each(function(){ie(this).removeClass("ls-thumb-active").stop().fadeTo(750,J.o.tnInactiveOpacity/100)}),$.find(".ls-thumbnail-slide a.ls-thumb-"+e).children().addClass("ls-thumb-active").stop().fadeTo(750,J.o.tnActiveOpacity/100)},scroll:function(){var e;$.find(".ls-thumbnail-slide-container").hasClass("ls-thumbnail-slide-hover")||(e=!!$.find(".ls-thumb-active").length&&$.find(".ls-thumb-active").parent())&&(e=e.position().left+e.width()/2,e=0<(e=(e=$.find(".ls-thumbnail-slide-container").width()/2-e)<$.find(".ls-thumbnail-slide-container").width()-$.find(".ls-thumbnail-slide").width()?$.find(".ls-thumbnail-slide-container").width()-$.find(".ls-thumbnail-slide").width():e)?0:e,$.find(".ls-thumbnail-slide").animate({marginLeft:e},600))},resize:function(){J.gui.navigation.bottom.switchHelper("on");var e=-1==J.slider.initial.width.indexOf("%")?parseInt(J.slider.initial.originalWidth):$.width(),t=$.find(".ls-thumbnail"),e=-1==J.o.tnContainerWidth.indexOf("%")?parseInt(J.o.tnContainerWidth):parseInt(e/100*parseInt(J.o.tnContainerWidth));$.find(".ls-thumbnail-slide a").css({width:parseInt(J.o.tnWidth*J.resize.ratio),height:parseInt(J.o.tnHeight*J.resize.ratio)}),$.find(".ls-thumbnail-slide a:last").css({margin:0}),$.find(".ls-thumbnail-slide").css({height:parseInt(J.o.tnHeight*J.resize.ratio)}),t.css({width:e*Math.floor(100*J.resize.ratio)/100}),t.width()>$.find(".ls-thumbnail-slide").width()&&t.css({width:$.find(".ls-thumbnail-slide").width()}),J.gui.navigation.bottom.switchHelper("off")}}}},media:{init:function(){0<J.media.properties.$allMediaLayers.length&&ie('<div class="ls-gui-element ls-media-unmute" aria-label="Unmute"><div class="ls-media-unmute-bg"></div><div class="ls-icon-muted"></div><div class="ls-icon-unmuted"></div></div>').on("click."+W,function(e){e.preventDefault(),$.layerSlider("unmute")}).appendTo($)},showUnmute:function(){$.find(".ls-media-unmute").addClass("ls-media-unmute-active")},hideUnmute:function(){$.find(".ls-media-unmute").removeClass("ls-media-unmute-active")}},skin:{load:function(){$.addClass("ls-"+J.o.skin);var e,t=J.o.skinsPath+J.o.skin+"/skin.css",i=ie("head").length?ie("head"):ie("body");ie('link[href="'+t+'"]').length?(e=ie('link[href="'+t+'"]'),J.gui.skin.isLoaded||(J.gui.skin.isLoaded=!0,J.timeouts.skinLoad1=setTimeout(function(){delete J.timeouts.skinLoad1,J.slider.init()},150))):e=document.createStyleSheet?(document.createStyleSheet(t),ie('link[href="'+t+'"]')):ie('<link rel="stylesheet" href="'+t+'" type="text/css" />').appendTo(i),e.on("load."+W,function(){J.gui.skin.isLoaded||(J.gui.skin.isLoaded=!0,J.timeouts.skinLoad2=setTimeout(function(){delete J.timeouts.skinLoad2,J.slider.init()},150))}),M.on("load."+W,function(){J.gui.skin.isLoaded||(J.gui.skin.isLoaded=!0,J.timeouts.skinLoad3=setTimeout(function(){delete J.timeouts.skinLoad3,J.slider.init()},150))}),J.timeouts.skinLoad4=setTimeout(function(){J.gui.skin.isLoaded||(J.gui.skin.isLoaded=!0,delete J.timeouts.skinLoad4,J.slider.init())},1e3)}},shadow:{init:function(){this.set(),this.resize()},set:function(){this.$element=ie('<div class="ls-gui-element ls-shadow"></div>').appendTo($),"block"!=this.$element.css("display")||this.$element.find("img").length||(this.show=function(){J.gui.shadow.$element.css({display:"none",visibility:"visible"}).fadeIn(500,function(){J.gui.shadow.show=!1})},this.image=ie("<img>").attr("src",J.o.skinsPath+J.o.skin+"/shadow.png").appendTo(this.$element),this.btmMod="number"==typeof parseInt($.css("padding-bottom"))?parseInt($.css("padding-bottom")):0)},resize:function(){this.image&&(0<this.image.height()?0<this.btmMod?this.$element.css({height:this.image.height()/2}):this.$element.css({height:this.image.height(),marginTop:-this.image.height()/2}):J.timeouts.resizeShadow=setTimeout(function(){delete J.timeouts.resizeShadow,J.gui.shadow.resize()},50))}},timers:{init:function(){J.o.showBarTimer&&this.bar.create(),J.o.showCircleTimer&&this.circle.create();var e=!1;(e=J.o.showSlideBarTimer?ie("<div>").insertAfter($):ie('[data-slidebar-for="'+$.attr("id")+'"], [data-slidebar-for="'+W+'"]')).length&&(e.addClass("ls-gui-element"),this.slidebar.create(e))},bar:{create:function(){this.$element=ie("<div>").addClass("ls-gui-element ls-bar-timer").appendTo($)}},circle:{create:function(){this.$element=ie("<div>").addClass("ls-gui-element ls-circle-timer").appendTo($),this.$element.append(ie('<div class="ls-ct-center"></div><div class="ls-ct-left"><div class="ls-ct-rotate"><div class="ls-ct-hider"><div class="ls-ct-half"></div></div></div></div><div class="ls-ct-right"><div class="ls-ct-rotate"><div class="ls-ct-hider"><div class="ls-ct-half"></div></div></div></div>')),this.$element.data("original",{opacity:this.$element.css("opacity")})}},slidebar:{$containerElement:[],$element:[],$progressBarElement:[],$sliderContainerElement:[],$sliderElement:[],elementWidth:[],containerElementWidth:[],sliderContainerElementWidth:[],state:{},create:function(e){function i(e,t){(s=(e.pageX||J.device.touchX||0)-o.$element[t].offset().left-o.sliderContainerElementWidth[t]/2)<0&&(s=0),s>o.containerElementWidth[t]-o.sliderContainerElementWidth[t]&&(s="calc( 100% - "+J.gui.timers.slidebar.sliderContainerElementWidth[t]+"px )"),o.$sliderContainerElement[t].css({left:s}),J.transitions._slideTimeline&&J.transitions._slideTimeline.progress("string"==typeof s?J.transitions.layers.timeline.progress:s/(o.containerElementWidth[t]-o.sliderContainerElementWidth[t])*J.transitions.layers.timeline.progress)}function a(e){"dragging"==J.gui.timers.slidebar.state&&(J.transitions._slideTimeline&&J.transitions.layers.timeline.state.finished&&J.transitions._slideTimeline.progress()!==J.transitions.layers.timeline.progress&&J.functions.setStates(J.transitions.layers.timeline,{finished:!1}),ie(document).off("mousemove."+W),ie("body").prop("unselectable",!1).removeClass("ls-unselectable"),J.o.pauseLayers&&!J.slideshow.state.running||J.slider.state.isPaused||!J.transitions._slideTimeline||J.o.playByScroll||(!0===J.transitions.layers.timeline.state.started?J.transitions.layers.timeline.resume():J.transitions.layers.timeline.play()),J.gui.timers.slidebar.state=!1)}var s,r=ie(document),o=this;ie.each(e,function(t,e){o.$containerElement[t]=ie(e).addClass("ls-slidebar-container "+W),o.$element[t]=ie("<div>").addClass("ls-slidebar").appendTo(o.$containerElement[t]),o.$progressBarElement[t]=ie("<div>").addClass("ls-progressbar").appendTo(o.$element[t]),o.$sliderContainerElement[t]=ie("<div>").addClass("ls-slidebar-slider-container").appendTo(o.$containerElement[t]),o.$sliderElement[t]=ie("<div>").addClass("ls-slidebar-slider").appendTo(o.$sliderContainerElement[t]),o.sliderContainerElementWidth[t]=o.$sliderContainerElement[t].width(),o.$sliderContainerElement[t].css({marginTop:-o.$sliderElement[t].outerHeight()/2}),o.$containerElement[t].on("touchmove."+W,function(e){i(e,t)}),o.$containerElement[t].on("touchend."+W,function(e){a()}),o.$containerElement[t].on("mousedown."+W+" touchstart."+W,function(e){J.transitions.layers.timeline.pause(0),ie("body").prop("unselectable",!0).addClass("ls-unselectable"),ie(document).on("mousemove."+W,function(e){i(e,t)}),i(e,t),J.gui.timers.slidebar.state="dragging"}),r=r.add(o.$sliderElement[t])}),ie("body").on("mouseup."+W,function(e){a()})}}},loadingIndicator:{init:function(){this.$element=ie("<div>").css({display:"none"}).addClass("ls-gui-element ls-loading-container").appendTo($),ie("<div>").addClass("ls-loading-indicator").appendTo(this.$element)},show:function(){this.$element.delay(400).fadeIn(300)},hide:function(){this.$element.stop(!0,!0).fadeOut(300)}}},J.navigation={direction:"next",init:function(){1<J.slides.count&&(this.set.keyboard(),this.set.touch())},set:{keyboard:function(){J.o.keybNav&&ie("body").on("keydown."+W,function(e){ie(e.target).is(":input")||J.slider.isAnimating||J.slider.isPreloading||(37==e.which?J.navigation.prev():39==e.which&&J.navigation.next())})},touch:function(){"ontouchstart"in window&&J.o.touchNav&&(J.slider.$innerWrapper.on("touchstart."+W,function(e){e=e.touches||e.originalEvent.touches;1==e.length&&(J.device.touchStartX=J.device.touchEndX=e[0].clientX)}),J.slider.$innerWrapper.on("touchmove."+W,function(e){var t=e.touches||e.originalEvent.touches;1==t.length&&(J.device.touchEndX=t[0].clientX),45<Math.abs(J.device.touchStartX-J.device.touchEndX)&&e.preventDefault()}),J.slider.$innerWrapper.on("touchend."+W,function(e){45<Math.abs(J.device.touchStartX-J.device.touchEndX)&&(0<J.device.touchStartX-J.device.touchEndX?$.layerSlider("touchNext"):$.layerSlider("touchPrev"))}))}},prev:function(){(!J.slider.isPopup||J.slider.isPopup&&J.slider.state.popupIsVisible)&&(this.direction="prev",this.forceDirection="prev",J.slideshow.set.prevNext("prev"))},next:function(){(!J.slider.isPopup||J.slider.isPopup&&J.slider.state.popupIsVisible)&&(this.direction="next",this.forceDirection="next",J.slideshow.set.prevNext("next"))},start:function(){J.functions.setStates(J.slideshow,{running:!0,paused:!1}),!0===J.slideshow.state.pausedByLastCycle&&J.functions.setStates(J.slideshow,{pausedByLastCycle:!1}),J.gui.navigation.bottom.setStartStop("start"),J.slideshow.state.pausedByHover||1!==J.transitions._slideTimeline.timeScale()&&J.transitions.layers.timeline.resume(),J.slideshow.start()},stop:function(){J.gui.navigation.bottom.setStartStop("stop"),J.o.pauseLayers&&J.transitions.layers.timeline.pause(),J.slideshow.stop()}},J.preload={init:function(){J.slider.$hiddenWrapper.find(".ls-slide img").each(function(){var e,t=ie(this),i=t[0],a={};t.is(".ls-layer, .ls-bg")&&(i.getAttribute("width")&&(a.width=i.getAttribute("width")),i.getAttribute("height")&&(a.height=i.getAttribute("height")),i.sizes&&(a.sizes=i.sizes),J.o.useSrcset&&(t.data("srcset")||i.srcset)&&(a.srcSet=t.data("srcset")||i.srcset,a.curSrc=i.currentSrc,e=a.srcSet.split(",").map(function(e){return parseInt(ie.trim(e).split(" ")[1])}),a.maxWidth=Math.max.apply(null,e)),t.removeAttr("width height sizes srcset loading"),ie.isEmptyObject(a)||(t.data(J.defaults.init.dataKey).attributes=a)),t.data("lazy-src")?t.data("src",t.data("lazy-src")):t.attr("data-tgpli-src")?t.data("src",t.attr("data-tgpli-src")):t.attr("data-lazy-src")?t.data("src",t.attr("data-lazy-src")):t.attr("data-lazy")?t.data("src",t.attr("data-lazy")):t.data("src")?a.curSrc&&t.data("src",a.curSrc):t.data("src",a.curSrc||i.src),t.attr("data-lazyset")?t.attr("srcset",t.attr("data-lazyset")):t.attr("data-lazy-srcset")?t.attr("srcset",t.attr("data-lazy-srcset")):t.attr("data-tgpli-srcset")&&t.attr("srcset",t.attr("data-tgpli-srcset")),t.removeAttr("lazy lazyload data-lazy-src data-tgpli-src data-lazy data-lazyset data-lazy-srcset data-tgpli-srcset"),t.attr("src","data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7")})},imagesOfSlide:function(e,t){var i,a,s;!0!==J.slides[e].wrapped?(this.slideIndex=e,t?(this.onCompleteCallback=t,J.functions.setStates(J.slider,{preloadingImages:!0}),J.gui.loadingIndicator.show()):this.onCompleteCallback=!1,J.slider.canShow&&$.css({visibility:"visible"}),this.preImages=[],i=this,J.slider.$hiddenWrapper.find(".ls-slide:eq("+(i.slideIndex-1)+") *").each(function(){a=ie(this),s=this;var e,t=a.data(J.defaults.init.dataKey);a.is("img")?(a.data("src")&&a.attr("src",a.data("src")),t&&t.attributes&&t.attributes.srcSet&&J.o.useSrcset&&(s.srcset=t.attributes.srcSet),e=s.src,(t=!!(t&&t.attributes&&t.attributes.curSrc)&&t.attributes.curSrc)&&e!==t&&a.is(".ls-bg")&&(e=t,J.slides[i.slideIndex].data.$background.attr("src",e)),J.preload.preImages.push([e,a])):"none"!==a.css("background-image")&&-1!==a.css("background-image").indexOf("url")&&J.preload.preImages.push([a.css("background-image").match(/url\(['"]?(.+?)['"]?\)/)[1],a])}),J.transitions.firstSlide&&J.o.globalBGImage&&J.preload.preImages.push([J.o.globalBGImage,ie()]),this.thumbnailsAreLoaded||this.thumbnails(),0===this.preImages.length?this.onComplete():this.start()):J.slider.shouldResize&&t?(J.resize.setLayers(J.layers.get("next, bg")),J.resize.layers({callback:t})):t&&t()},thumbnails:function(){for(var e=J.slider.thumbnails.filter(function(e,t,i){return i.indexOf(e)==t}),t=e.length,i=0;i<t;i++)(new Image).src=e[i];this.thumbnailsAreLoaded=!0},start:function(){J.debugMode&&(J.debug.add("group","preload"),J.debug.add("log","preload.info",this.slideIndex)),this.preloadedImagesCount=0;for(var e,t=this,i=function(){++t.preloadedImagesCount==t.preImages.length&&(J.debugMode&&J.debug.groupEnd(),t.onComplete())},a=function(){J.debugMode&&(e=this.src.substring(this.src.lastIndexOf("/")+1,this.src.length),J.debug.add("log","preload.success",e)),this.originalLayer.data("preloadedWidth",this.width),this.originalLayer.data("preloadedHeight",this.height),i()},s=function(){J.debugMode&&(e=this.src.substring(this.src.lastIndexOf("/")+1,this.src.length),J.debug.add("warn","preload.fail",e)),i()},r=0;r<this.preImages.length;r++){var o=new Image;o.addEventListener("error",s,!1),o.addEventListener("load",a,!1),o.src=this.preImages[r][0],o.originalLayer=this.preImages[r][1]}},onComplete:function(){var s=this;this.onCompleteCallback?(J.layers.wrap(this.slideIndex),function e(){var t,i,a;0!==J.slides[s.slideIndex].$layers.length?J.timeouts.waitForWrap=setTimeout(e,100):(delete J.timeouts.waitForWrap,J.functions.setStates(J.transitions.layers.parallax,{ready:!0}),ie(".ls-thumbnail-wrapper, .ls-nav-next, .ls-nav-prev, .ls-bottom-nav-wrapper").css({visibility:"visible"}),J.slides[s.slideIndex].wrapped=!0,t=!(!window._layerSlider.globals.youTubeIsReady&&J.layers.get("next,in,youtube,bgvideo").length),i=!(!window._layerSlider.globals.vimeoIsReady&&J.layers.get("next,in,vimeo,bgvideo").length),a=function(){J.gui.loadingIndicator.hide(),J.slider.shouldResize?(J.resize.setLayers(J.layers.get("next, bg")),J.resize.layers({callback:s.onCompleteCallback})):s.onCompleteCallback()},t&&i?a():J.intervals.waitForJSApisLoaded=setInterval(function(){(t||window._layerSlider.globals.youTubeIsReady)&&(i||window._layerSlider.globals.vimeoIsReady)&&(clearInterval(J.intervals.waitForJSApisLoaded),delete J.intervals.waitForJSApisLoaded,a())},50))}()):J.layers.wrap(this.slideIndex,!0),J.functions.setStates(J.slider,{preloadingImages:!1})}},J.resize={setLayers:function(e){this.$responsiveLayers=e.add(J.layers.get("active")),J.slides.next.data.$backgroundVideo.length&&(this.$responsiveLayers=this.$responsiveLayers.add(J.slides.next.data.$backgroundVideo))},all:function(e={}){return!(J.slider.state.isHidden&&!J.slider.isPopup)&&(!!document.body.contains(L)&&(J.api.hasEvent("sliderWillResize")&&J.api.triggerEvent("sliderWillResize",J.api.eventData()),this.slider(!!e.forceToGetParentWidth),J.o.performanceMode&&this.performance(),J.slider.isScene&&!J.slider.$spacingWrapper.is("[data-disabled-scene]")&&this.scene(),this.navigation(),this.layers(),this.yourLogo(),this.shadow(),this.timers(),J.slides.indexesCreated&&(J.o.allowRestartOnResize&&J.transitions.layers.timeline.shouldRestart||J.transitions.layers.timeline.forceRestart)&&(J.transitions.layers.timeline.forceRestart=!1,J.functions.resetSlideTimelines(),J.transitions.layers.timeline.create(!0),J.slider.isScrollScene&&J.transitions._slideTimeline&&J.transitions.scrollscene.animate()),void(J.api.hasEvent("sliderDidResize")&&J.api.triggerEvent("sliderDidResize",J.api.eventData()))))},viewport:function(){M.scrollTop(Math.round(J.slider.offset.top)-(te.viewport.height-J.slider.height)/2)},slider:function(e){if(!document.body.contains(L))return!1;te.viewport.width||M.trigger("resize.lsGlobal"),J.o.fixFloatedContainers&&$.css("display","none");var t,i,a,s=J.slider.$parentWithNumericWidthValue&&0<J.slider.$parentWithNumericWidthValue.width()&&!e?J.slider.$parentWithNumericWidthValue:J.functions.getSliderClosestParentElementWithNumericValueOfProperty("width"),r=J.slider.initial,o=J.slider.$parentWithNumericWidthValuePercent?s.width()/100*J.slider.$parentWithNumericWidthValuePercent:s.width(),n=r.type,l=0!==r.maxWidth?r.maxWidth:o,d="auto"===r.marginLeft?0:r.marginLeft,e="auto"===r.marginRight?0:r.marginRight;switch(J.o.fixFloatedContainers&&$.css("display","block"),-1!==l.indexOf("%")?l=o/100*parseInt(l):-1!==l.indexOf("vw")&&(l=te.viewport.width/100*parseInt(l)),J.slider.state.inFullscreen?$[0].style.maxWidth="":0!==r.maxWidth&&($[0].style.maxWidth=l),l<(o-=d+e)&&0<=l&&(o=l),J.o.fitScreenWidth&&("fullwidth"===n||"fullsize"===n&&"fitheight"!==J.o.fullSizeMode&&"fitwidth"!==J.o.fullSizeMode)&&(d=s.offset().left,e=parseInt(s.css("padding-left"))||0,l=parseInt(s.css("border-left-width"))||0,$[0].style.maxWidth="100vw",$[0].style.marginLeft=-(d+e+l)+"px",o=te.viewport.width),o-=r.skinWidth,te.getScreenSize(),J.slider.state.inFullscreen&&(o=te.screen.width),n){case"responsive":0<J.o.maxRatio&&(o=Math.min(r.width*J.o.maxRatio,o)),t=(J.slider.state.inFullscreen?(te.screen.ratio>r.ratio?this.ratio=te.screen.height/r.height:this.ratio=te.screen.width/r.width,o=Math.round(r.width*this.ratio)):this.ratio=o/r.width,Math.round(r.height*this.ratio));break;case"fullwidth":t=o<J.o.responsiveUnder?(this.ratio=o/J.o.responsiveUnder,Math.round(r.height*this.ratio)):J.slider.state.inFullscreen?te.screen.ratio>r.layersWidth/r.height?(this.ratio=te.screen.height/r.height,te.screen.height):(this.ratio=te.screen.width/r.layersWidth,r.height*this.ratio):(this.ratio=1,r.height);break;case"fullsize":switch(J.o.fullSizeMode.toLowerCase()){case"normal":t=te.viewport.height-r.skinHeight;break;case"hero":var p=Math.max($.offset().top,0),c=J.slider.isScene?J.slider.$spacingWrapper:$;J.slider.isScene&&(p=0);var u=ie(J.o.calculateOffsetFrom).length?ie(J.o.calculateOffsetFrom).height()+ie(J.o.calculateOffsetFrom)[0].getBoundingClientRect().top:0;u&&c.css("top",u),(t=te.viewport.height-r.skinHeight-(u||p))<=0&&(t=te.viewport.height-r.skinHeight);break;case"fitheight":o=s.width()-r.skinWidth,t=s.height()-r.skinHeight;break;case"fitwidth":o=s.width()-r.skinWidth,t=te.viewport.height-r.skinHeight}o/t<r.ratio?this.ratio=o/r.layersWidth:this.ratio=t/r.layersHeight;break;case"fixed":case"fixedsize":this.ratio=1,o=r.width,t=r.height,J.o.maxRatio=1,L.style.maxWidth="none"}this.ratio=J.o.maxRatio&&0<J.o.maxRatio&&this.ratio>J.o.maxRatio?J.o.maxRatio:this.ratio,L.style.width=o+"px",L.style.height=t+"px",J.slider.width=o,J.slider.height=t,J.slider.$layersWrapper.css({"--sw":J.slider.width+"px","--sh":J.slider.height+"px"}),te.isMobile?768<=te.viewport.width||te.viewport.width>te.viewport.height?$.removeClass("ls-device-is-phone").addClass("ls-device-is-tablet"):$.removeClass("ls-device-is-tablet").addClass("ls-device-is-phone"):$.removeClass("ls-device-is-phone ls-device-is-tablet").addClass("ls-device-is-desktop"),J.o.marginTop&&(i=-1!=J.o.marginTop.indexOf("sh")||-1!=J.o.marginTop.indexOf("%")?J.slider.height/100*parseInt(J.o.marginTop):J.o.marginTop),J.o.marginBottom&&(a=-1!=J.o.marginBottom.indexOf("sh")||-1!=J.o.marginBottom.indexOf("%")?J.slider.height/100*parseInt(J.o.marginBottom):J.o.marginBottom),J.slider.$spacingWrapper.css({marginTop:i,marginBottom:a})},performance:function(){var e=parseInt(J.o.performanceModeThreshold);-1!==J.o.performanceModeThreshold.indexOf("sh")||-1!==J.o.performanceModeThreshold.indexOf("%")?J.performance.threshold=J.slider.height*(e/100):-1!==J.o.performanceModeThreshold.indexOf("vh")?J.performance.threshold=te.viewport.height*(e/100):-1!==J.o.performanceModeThreshold.indexOf("px")?J.performance.threshold=e:J.performance.threshold=J.slider.height*e,J.performance.threshold=Math.max(parseInt(J.performance.threshold),0)},scene:function(){var e;J.slider.isScrollScene&&J.o.sceneDuration?e=Math.round(J.slider.height+J.o.sceneDuration*J.slider.height/(J.o.sceneSpeed/100))+"px":J.slider.isSticky&&(-1!==J.o.sceneHeight.indexOf("sh")||-1!==J.o.sceneHeight.indexOf("%")?(e=J.slider.height*(parseInt(J.o.sceneHeight)/100),e=Math.round(Math.max(J.slider.height,e)),e+="px"):-1===J.o.sceneHeight.indexOf("px")&&-1===J.o.sceneHeight.indexOf("vh")?(e=J.slider.height*(J.o.sceneHeight||2),e=Math.round(Math.max(J.slider.height,e)),e+="px"):e=J.o.sceneHeight),e&&J.slider.$spacingWrapper.css({height:e}),$.css({top:J.transitions.scrollscene.stickLimit})},borderRadius:function(e,i){ie.isNumeric(e)&&(e=e.toString());var a="";return ie.each(e.split(" "),function(e,t){-1==t.indexOf("%")&&-1==t.indexOf("em")?a+=Math.round(parseInt(t)*i)+"px ":a+=t+" "}),ie.trim(a)},shadows:function(e,t){for(var i=e.split(" "),a=0;a<i.length;a++)-1!==i[a].indexOf("px")&&(i[a]=Math.ceil(parseInt(i[a])*t)+"px");return e=i.join(" ").trim()},convertSingleValue:function(e,t){return-1==e.indexOf("em")?Math.round(parseInt(e)*t)+"px ":e},calculateRatio:function(e,t){return e.settings.minresponsiveratio&&t<e.settings.minresponsiveratio&&(t=e.settings.minresponsiveratio),e.settings.maxresponsiveratio&&t>e.settings.maxresponsiveratio&&(t=e.settings.maxresponsiveratio),e.settings.calculatedratio=t},layers:function(e={}){if(J.slider.hasPinnedLayers&&(J.slider.$layersWrapper.css({clip:"rect(0px "+J.slider.width+"px "+J.slider.height+"px 0px)"}),J.slider.$layersWrapper&&J.slider.$layersWrapper.is(".ls-layers-clip")&&J.slider.$layersWrapper.removeClass("ls-layers-clip")),J.o.animateFirstSlide&&J.slideshow.firstStart&&"inside"!==J.slider.position.toViewport)return J.functions.setStates(J.slider,{readyForStart:!0}),void(e.callback&&(J.api.resumeCallback=e.callback));if(e.singleLayer||this.$responsiveLayers){J.debugMode&&J.debug.add("group","resize");for(var t,i=e.singleLayer||this.$responsiveLayers,a=J.slider.initial,s=J.slider.width,r=J.slider.height,o=s/r,n=[],l=[],d=[],p=[],c=0,u=0,h=window.LS_previewZoom||1,m=0,f=i.length;m<f;m++){var g=i[m],y=ie(g),v=y.data(J.defaults.init.dataKey),x=!!v.is.insideLayerGroup&&y.data("$layerGroup"),w=!!v.is.insideLayerGroup&&x.data(J.defaults.init.dataKey),b=e.original?ie.extend(!0,{},v.original,e.original):v.original,S=this.calculateRatio(v,this.ratio),T=v.elements.$_innerWrappers;b.width||(b.percentWidth=0),b.height||(b.percentHeight=0),!y.is("img, picture")||"auto"!==b.width&&"auto"!==b.height||((T=v.elements.$_innerWrappers).addClass("ls-force-display-block ls-force-visibility-hidden"),"auto"===b.width&&(b.width=y.width()),"auto"===b.height&&(b.height=y.height()),T.removeClass("ls-force-display-block ls-force-visibility-hidden")),t="responsive"===a.type&&-1!==J.o.maxRatio?a.width:a.layersWidth,I="responsive"===a.type&&-1!==J.o.maxRatio?a.height:a.layersHeight,L=t,u="fullsize"===a.type||"fullwidth"===a.type||"responsive"===a.type?(c=0<t?(s-t*S)/2:0,0<I?(r-I*S)/2:0):(c=c<0?0:c,u<0?0:u),"0%"!==b.left&&"100%"!==b.left||(c=c<0?0:c),"0%"!==b.top&&"100%"!==b.top||(u=u<0?0:u);var k="fixed"==v.settings.position||"fixedx"==v.settings.position,C="fixed"==v.settings.position||"fixedy"==v.settings.position,P=s,O=r,I=v.is.insideLayerGroup?(C=k=!1,W=$=0,t=P=w.responsive.width,O=w.responsive.height):(t=L,I);v.is.pinned&&(J.slider.get.offset(),"responsive"!==a.type&&"fixedsize"!==a.type&&("fullsize"!==a.type||"fitheight"!==J.o.fullSizeMode&&!1!==J.o.fitScreenWidth)||(k?(t=P=te.viewport.width,c=0):(P=J.slider.width,c=J.slider.offset.left,t=a.width)),C?(I=O=te.viewport.height,u=0):(O=J.slider.height,u=(te.viewport.height-J.slider.height)/2||0,I=a.height));var L,$=k?0:c,W=C?0:u,M={width:k&&0!==b.percentWidth?P/100*b.percentWidth:parseInt(b.width)*S,height:C&&0!==b.percentHeight?O/100*b.percentHeight:parseInt(b.height)*S,paddingLeft:this.convertSingleValue(b.paddingLeft,S),paddingTop:this.convertSingleValue(b.paddingTop,S),paddingRight:this.convertSingleValue(b.paddingRight,S),paddingBottom:this.convertSingleValue(b.paddingBottom,S),borderLeftWidth:this.convertSingleValue(b.borderLeftWidth,S),borderTopWidth:this.convertSingleValue(b.borderTopWidth,S),borderRightWidth:this.convertSingleValue(b.borderRightWidth,S),borderBottomWidth:this.convertSingleValue(b.borderBottomWidth,S),borderRadius:this.borderRadius(b.borderRadius,S),boxShadow:this.shadows(b.boxShadow,S),textShadow:this.shadows(b.textShadow,S)},B={marginLeft:this.convertSingleValue(b.marginLeft,S),marginTop:this.convertSingleValue(b.marginTop,S),marginRight:this.convertSingleValue(b.marginRight,S),marginBottom:this.convertSingleValue(b.marginBottom,S)},_={},D={borderRadius:M.borderRadius};if(b.minWidth&&(M.minWidth=b.percentMinWidth?P/100*b.percentMinWidth:b.minWidth,M.width=Math.max(M.width,M.minWidth)),b.minHeight&&(M.minHeight=b.percentMinHeight?O/100*b.percentMinHeight:b.minHeight,M.height=Math.max(M.height,M.minHeight)),b.maxWidth&&(M.maxWidth=b.percentMaxWidth?P/100*b.percentMaxWidth:b.maxWidth,M.width=Math.min(M.width,M.maxWidth)),b.maxHeight&&(M.maxHeight=b.percentMaxHeight?O/100*b.percentMaxHeight:b.maxHeight,M.height=Math.min(M.height,M.maxHeight)),b.minWidth&&b.maxWidth&&M.minWidth>M.maxWidth&&(M.width=M.minWidth),b.minHeight&&b.maxHeight&&M.minHeight>M.maxHeight&&(M.height=M.minHeight),v.is.imageLayer&&(M.minWidth||M.minHeight||M.maxWidth||M.maxHeight)&&(T.addClass("ls-force-display-block ls-force-visibility-hidden"),L={minHeight:M.minHeight||0,minWidth:M.minWidth||0,maxHeight:M.maxHeight||"none",maxWidth:M.maxWidth||"none"},!v.original.autoWidth||M.minWidth&&!M.minHeight||M.maxWidth&&!M.maxHeight?L.width=M.width:L.width="auto",!v.original.autoHeight||!M.minWidth&&M.minHeight||!M.maxWidth&&M.maxHeight?L.height=M.height:L.height="auto",y.css(L),M.width=y.width(),M.height=y.height(),delete M.minWidth,delete M.minHeight,delete M.maxWidth,delete M.maxHeight,T.removeClass("ls-force-display-block ls-force-visibility-hidden")),(k||C)&&(b.percentHeight||b.percentWidth)&&v.is.imageLayer&&(b.percentHeight&&!b.percentWidth&&(M.width=b.width*(M.height/b.height)),b.percentWidth&&!b.percentHeight&&(M.height=b.height*(M.width/b.width))),("number"==typeof b.width&&b.width<0||"auto"==b.width||""==b.sWidth)&&J.debugMode&&J.debug.add("warn","resize.width",[m+1,b.width]),("number"==typeof b.height&&b.height<0||"auto"==b.height||""==b.sHeight)&&J.debugMode&&J.debug.add("warn","resize.height",[m+1,b.height]),M.fontSize=b.fontSize*S,v.is.textLayer&&(te.isMobile&&M.fontSize<v.styleSettings.minmobilefontsize?M.fontSize=v.styleSettings.minmobilefontsize:M.fontSize<v.styleSettings.minfontsize&&(M.fontSize=v.styleSettings.minfontsize),T=M.fontSize/b.fontSize,M.fontSize+="px",-1!==b.lineHeight.indexOf("px")&&(M.lineHeight=parseFloat(b.lineHeight)*T+"px"),-1!==b.letterSpacing.indexOf("px")&&(M.letterSpacing=parseFloat(b.letterSpacing)*T+"px"),-1!==b.textStrokeWidth.indexOf("px")&&(M.textStrokeWidth=parseFloat(b.textStrokeWidth)*T+"px"),""==b.sWidth&&(M.width="auto",v.elements.$innerWrapper.addClass("ls-force-width-auto"),"nowrap"!==y.css("white-space")&&(v.elements.$_innerWrappers.addClass("ls-force-left-0"),v.elements.$_outerWrappers.addClass("ls-force-full-size"))),""==b.sHeight&&(M.height="auto",v.elements.$innerWrapper.addClass("ls-force-height-auto")),""!=b.sWidth&&""!=b.sHeight||y.css(M)),v.is.slideBackground||v.is.backgroundVideo)if(v.is.slideBackground){var F=J.slides[v.is.onSlide].data.backgroundSize;switch((void 0!==F&&"inherit"!==F?F:J.o.slideBGSize).replace("100% 100%","stretch")){case"auto":break;case"cover":b.ratio<o?(M.width=s,M.height=M.width/b.ratio):(M.height=r,M.width=M.height*b.ratio);break;case"contain":b.ratio<o?(M.height=r,M.width=M.height*b.ratio):(M.width=s,M.height=M.width/b.ratio);break;case"stretch":M.width=s,M.height=r}M.width=Math.round(M.width),M.height=Math.round(M.height);var A,z=J.slides[v.is.onSlide].data.backgroundPosition;switch((A=(void 0!==z?z:J.o.slideBGPosition).split(" "))[0]){case"left":M.x=0;break;case"center":M.x=(J.slider.width-M.width)/2;break;case"right":M.x=J.slider.width-M.width;break;default:-1!==A[0].indexOf("%")?M.x=(J.slider.width-M.width)/100*parseInt(A[0]):M.x=parseInt(A[0])}if(void 0!==A[1])switch(A[1]){case"top":M.y=0;break;case"center":M.y=(J.slider.height-M.height)/2;break;case"bottom":M.y=J.slider.height-M.height;break;default:-1!==A[1].indexOf("%")?M.y=(J.slider.height-M.height)/100*parseInt(A[1]):M.y=parseInt(A[1])}M.transform="translateX("+M.x+"px) translateY("+M.y+"px)",M["-ms-transform"]="translateX("+M.x+"px) translateY("+M.y+"px)",M["-webkit-transform"]="translateX("+M.x+"px) translateY("+M.y+"px)"}else v.is.backgroundVideo&&(b.ratio<o?(M.width=s,M.height=M.width/b.ratio):(M.height=r,M.width=M.height*b.ratio),M.x=(J.slider.width-M.width)/2,M.y=(J.slider.height-M.height)/2,M.width=Math.round(M.width),M.height=Math.round(M.height),M.transform="translateX("+M.x+"px) translateY("+M.y+"px)",M["-ms-transform"]="translateX("+M.x+"px) translateY("+M.y+"px)",M["-webkit-transform"]="translateX("+M.x+"px) translateY("+M.y+"px)");else!v.mediaProperties||"youtube"!==v.mediaProperties.type&&"vimeo"!==v.mediaProperties.type||(F=M.width/M.height,z={width:M.width,height:M.height},b.ratio<F?(z.height=M.width/b.ratio,z.marginTop=(M.height-z.height)/2):(z.width=M.height*b.ratio,z.marginLeft=(M.width-z.width)/2),v.mediaProperties.$media.css(z)),v.elements.$_innerWrappers.addClass("ls-force-display-block ls-force-no-transform"),y.addClass("ls-force-no-transform"),v.is.insideLayerGroup&&((w=(x=y.data("$layerGroup")).data(J.defaults.init.dataKey)).elements.$_innerWrappers.addClass("ls-force-display-block ls-force-no-transform"),x.addClass("ls-force-no-transform")),"auto"==M.width?M.outerWidth=Math.ceil(y.outerWidth()):M.outerWidth=M.width+J.layers.toNum(M.paddingLeft,M.fontSize)+J.layers.toNum(M.paddingRight,M.fontSize)+J.layers.toNum(M.borderLeftWidth,M.fontSize)+J.layers.toNum(M.borderRightWidth,M.fontSize),"auto"==M.height?M.outerHeight=Math.ceil(y.outerHeight()):M.outerHeight=M.height+J.layers.toNum(M.paddingTop,M.fontSize)+J.layers.toNum(M.paddingBottom,M.fontSize)+J.layers.toNum(M.borderTopWidth,M.fontSize)+J.layers.toNum(M.borderBottomWidth,M.fontSize),v.elements.$_allWrappers.removeClass("ls-force-display-block ls-force-no-transform ls-force-width-auto ls-force-height-auto ls-force-left-0 ls-force-full-size"),y.removeClass("ls-force-no-transform"),v.is.insideLayerGroup&&(w.elements.$_allWrappers.removeClass("ls-force-display-block ls-force-no-transform ls-force-width-auto ls-force-height-auto ls-force-left-0 ls-force-full-size"),x.removeClass("ls-force-no-transform")),B.width=_.width=M.outerWidth,B.height=_.height=M.outerHeight,-1!=b.left.indexOf("%")?"100%"===b.left?M.left=0===$?P/100*parseFloat(b.left)-M.outerWidth:$+t*S/100*parseFloat(b.left)-M.outerWidth:"0%"===b.left?M.left=0===$?0:$:M.left=0===$?P/100*parseFloat(b.left)-M.outerWidth/2:$+t*S/100*parseFloat(b.left)-M.outerWidth/2:M.left=$+parseFloat(b.left)*S,B.left=M.left,-1!=b.top.indexOf("%")?"100%"===b.top?M.top=0===W?O/100*parseFloat(b.top)-M.outerHeight:W+I*S/100*parseFloat(b.top)-M.outerHeight:"0%"===b.top?M.top=0===W?0:W+0:M.top=0===W?O/100*parseFloat(b.top)-M.outerHeight/2:W+I*S/100*parseFloat(b.top)-M.outerHeight/2:M.top=W+parseFloat(b.top)*S,B.top=M.top;v.textIn.$nodesForBackgroundClip&&v.textIn.$nodesForBackgroundClip.length&&((O=v.elements.$outerStyleWrapper.add(v.elements.$outerStyleWrapper.find(".ls-wrapper"))).addClass("ls-force-visibility-hidden ls-force-display-block"),v.elements.$_innerWrappers.addClass("ls-force-no-transform"),I=!1,"auto"===g.style.width&&(g.style.width=Math.ceil(v.original.width*(h*S))+"px",I=!0),v.textIn.$nodesForBackgroundClip.each(function(e,t){var i=ie(t),a=i.children(".ls-textnode-bgclip-wrap"),t=a.children(".ls-textnode"),i=(i.children(".ls-textnode-dummy"),i.position());a.css({width:Math.ceil(B.width),height:Math.ceil(B.height),transform:"translate("+-i.left/h+"px,"+-i.top/h+"px)"}),t.css({marginLeft:i.left/h,marginTop:i.top/h})}),I=I&&!(g.style.width="auto"),v.elements.$_innerWrappers.removeClass("ls-force-no-transform"),O.removeClass("ls-force-visibility-hidden ls-force-display-block")),M.fontSize=parseFloat(M.fontSize)/te.automaticFontSizeRatio+"px",v.responsive=M,n[m]=M,v.is.smartBG&&(M.left=Math.round(M.left),M.top=Math.round(M.top),M.width=Math.ceil(M.width),M.height=Math.ceil(M.height),n[m]["--sw"]=(k?J.slider.width:a.width*S)+"px",n[m]["--sh"]=(C?J.slider.height:a.height*S)+"px",n[m]["--x"]=-M.left-parseFloat(M.borderLeftWidth)-parseFloat(M.paddingLeft)-parseFloat(B.marginLeft)+(k?0:c)+"px",n[m]["--y"]=-M.top-parseFloat(M.borderTopWidth)-parseFloat(M.paddingTop)-parseFloat(B.marginTop)+(C?0:u)+"px"),v.is.slideBackground||v.is.backgroundVideo||(v.settings.wrapperData.responsive=B,l[m]=B,d[m]=_,p[m]=D)}if(e.singleLayer)return n;for(var V=0,N=n.length;V<N;V++){var R=ie(i[V]),E=R.data(J.defaults.init.dataKey);R.css(n[V]),E.is.slideBackground||E.is.backgroundVideo?(E.is.slideBackground||E.is.backgroundVideo)&&(E.elements.$bgOuterWrapper.css({width:J.slider.width,height:J.slider.height}),E.elements.$outerWrapper.css({width:J.slider.width,height:J.slider.height})):(R.find(".split-item").css(p[V]),this.wrappers(R,E,l[V],d[V]))}e.callback&&e.callback(),J.debugMode&&J.debug.groupEnd("resize")}},wrappers:function(e,t,i,a){i&&t.elements.$outerStyleWrapper.css(i),a&&t.loop.enabled&&t.elements.$loopWrapper.css(a),ee.TweenMax.set(t.elements.$wrapper[0],{autoCSS:!1,css:{transformPerspective:t.transformPerspective.layer*J.resize.ratio}}),t.loop.enabled&&ee.TweenMax.set(t.elements.$loopWrapper[0],{autoCSS:!1,css:{transformPerspective:t.transformPerspective.loop*J.resize.ratio}}),t.hover.enabled&&ee.TweenMax.set(e[0],{autoCSS:!1,css:{transformPerspective:t.transformPerspective.hover*J.resize.ratio}}),t.textIn.nodes&&ee.TweenMax.set(t.textIn.nodes,{autoCSS:!1,css:{transformPerspective:t.transformPerspective.text*J.resize.ratio}}),t.textOut.nodes&&ee.TweenMax.set(t.textOut.nodes,{autoCSS:!1,css:{transformPerspective:t.transformPerspective.text*J.resize.ratio}}),t.parallax.enabled&&ee.TweenMax.set(t.elements.$parallaxWrapper[0],{autoCSS:!1,css:{transformPerspective:t.transformPerspective.parallax*J.resize.ratio}}),t.scroll.enabled&&ee.TweenMax.set(t.elements.$scrollTransformWrapper[0],{autoCSS:!1,css:{transformPerspective:t.transformPerspective.scroll*J.resize.ratio}})},performTransformOperations:function(e,t){var i,a,s,r;for(r in t)if("string"==typeof t[r]&&-1!==t[r].indexOf("="))if(a=(i=t[r].split("="))[0].trim()||!1,s=parseFloat(i[1].trim())||!1,a&&s&&ie.isNumeric(s))switch(a){case"+":t[r]=e[r]+s;break;case"-":t[r]=e[r]-s;break;case"*":t[r]=e[r]*s;break;case"/":t[r]=e[r]/s}else t[r]=e[r]},mirrorTransitionProperties:function(o){function n(e){var t=e;switch(e){case"left":t="right";break;case"right":t="left";break;case"top":t="bottom";break;case"bottom":t="top"}return-1!==e.indexOf("lw")||-1!==e.indexOf("lh")||-1!==e.indexOf("sw")||-1!==e.indexOf("sh")?t="-"===e.charAt(0)?e.substring(1):"-"+e:-1!==e.indexOf("%")?t=-1*parseFloat(e.split("%")[0])+"%":ie.isNumeric(e)&&0!==e&&"0"!==e&&(t=-1*e),t}function e(e){var t,i,a,s;for(s in e)if(a=s.toLowerCase(),-1!==o.mirrorProperties.indexOf(a)){if("object"==typeof(t=e[s]))for(var r in i=[],t)i.push(n(t[r]));else i=n(t);e[s]=i}}e(o.transitionProperties),o.transitionPropertiesShouldBeConverted&&e(o.transitionPropertiesShouldBeConverted)},transformProperties:function(e,t,i,a,s,r){if("object"==typeof a.x){for(var o=[],n=0;n<a.x.length;n++)"string"==typeof a.x[n]?o[n]=this.getXY(e,t,a.x[n],"Width"):o[n]=a.x[n]*t.settings.calculatedratio;i.cycle.x=o}else"string"==typeof a.x?-1!==a.x.indexOf("random")||a.x.charAt(0)===J.defaults.init.openingBracket?i.x=J.functions.convert.randomProperties(a.x,"x",e,t):i.x=this.getXY(e,t,a.x,"Width"):void 0!==a.x&&(i.x=a.x*t.settings.calculatedratio);if("object"==typeof a.y){for(var l=[],d=0;d<a.y.length;d++)"string"==typeof a.y[d]?l[d]=this.getXY(e,t,a.y[d],"Height"):l[d]=a.y[d]*t.settings.calculatedratio;i.cycle.y=l}else"string"==typeof a.y?-1!==a.y.indexOf("random")||a.y.charAt(0)===J.defaults.init.openingBracket?i.y=J.functions.convert.randomProperties(a.y,"y",e,t):i.y=this.getXY(e,t,a.y,"Height"):void 0!==a.y&&(i.y=a.y*t.settings.calculatedratio);if(s&&(i=s),"object"==typeof a.transformOrigin||r){var p=[];if(r){s=e.data(J.defaults.init.dataKey).elements.$outerStyleWrapper.add(e.data(J.defaults.init.dataKey).elements.$outerStyleWrapper.find(".ls-wrapper"));s.addClass("ls-force-visibility-hidden ls-force-display-block ls-fix-textnodes"),p=J.functions.convert.nodesTransformOrigin(a.transformOrigin,r,t,e.data(J.defaults.init.dataKey).elements.$outerStyleWrapper),s.removeClass("ls-force-visibility-hidden ls-force-display-block ls-fix-textnodes")}else for(var c=0;c<a.transformOrigin.length;c++)p[c]=J.functions.convert.transformOrigin(a.transformOrigin[c],e,t,e.data(J.defaults.init.dataKey).elements.$outerStyleWrapper);i.cycle.transformOrigin=p}else"string"==typeof a.transformOrigin&&(i.transformOrigin=J.functions.convert.transformOrigin(a.transformOrigin,e,t,e.data(J.defaults.init.dataKey).elements.$outerStyleWrapper))},styleProperties:function(e,t,i,a){a.width&&-1!==a.width.indexOf("%")&&(a.percentWidth=parseInt(a.width),a.width=parseInt(J.layers.getStyle(a.width,J.slider.initial.percW))),a.height&&-1!==a.height.indexOf("%")&&(a.percentHeight=parseInt(a.height),a.height=parseInt(J.layers.getStyle(a.height,J.slider.initial.percH)));var s,r=J.resize.layers({singleLayer:e,original:a})[0];for(s in a)-1===s.indexOf("percent")&&r.hasOwnProperty(s)&&-1===r[s].indexOf("NaN")&&(i[s]=r[s])},getXY:function(e,t,i,a,s){var r=0,o=t.original,n=t.responsive,l=t.settings.wrapperData&&t.settings.wrapperData.responsive?t.settings.wrapperData.responsive:{marginLeft:this.convertSingleValue(o.marginLeft,this.ratio),marginTop:this.convertSingleValue(o.marginTop,this.ratio),marginRight:this.convertSingleValue(o.marginRight,this.ratio),marginBottom:this.convertSingleValue(o.marginBottom,this.ratio)};if(n.outerWidth||(n.outerWidth=Math.ceil(e.outerWidth())),n.outerHeight||(n.outerHeight=Math.ceil(e.outerHeight())),o&&n&&l)switch(i){case"left":r=-1==o.left.indexOf("%")||!parseInt(o.left)||"100%"===o.left?-n.left-n.outerWidth-parseInt(l.marginLeft):-parseFloat(o.left)/100*J.slider.width-n.outerWidth/2-parseInt(l.marginLeft);break;case"right":r=-1==o.left.indexOf("%")||!parseInt(o.left)||"100%"===o.left?J.slider.width-n.left-parseInt(l.marginLeft):(1-parseFloat(o.left)/100)*J.slider.width+n.outerWidth/2-parseInt(l.marginLeft);break;case"top":r=-1==o.top.indexOf("%")||!parseInt(o.top)||"100%"===o.top?-n.top-n.outerHeight-parseInt(l.marginTop):-parseFloat(o.top)/100*J.slider.height-n.outerHeight/2-parseInt(l.marginTop);break;case"bottom":r=-1==o.top.indexOf("%")||!parseInt(o.top)||"100%"===o.top?J.slider.height-n.top-parseInt(l.marginTop):(1-parseFloat(o.top)/100)*J.slider.height+n.outerHeight/2-parseInt(l.marginTop);break;case"width":r=n.outerWidth;break;case"-width":r=-n.outerWidth;break;case"height":r=n.outerHeight;break;case"-height":r=-n.outerHeight;break;default:r=-1!==i.indexOf("%")?n["outer"+a]/100*parseInt(i):-1!==i.indexOf("sw")?parseFloat(i.split("sw")[0])/100*J.slider.width:-1!==i.indexOf("sh")?parseFloat(i.split("sh")[0])/100*J.slider.height:-1!==i.indexOf("lw")?n.outerWidth/100*parseFloat(i.split("lw")[0]):-1!==i.indexOf("lh")?n.outerHeight/100*parseFloat(i.split("lh")[0]):parseFloat(i)*(s?1:t.settings.calculatedratio||this.calculateRatio(t,this.ratio))}return r},navigation:function(){"always"==J.o.thumbnailNavigation&&J.gui.navigation.bottom.thumbnails.resize()},shadow:function(){J.gui.shadow.show&&J.gui.shadow.show(),J.gui.shadow.$element&&J.gui.shadow.resize()},yourLogo:function(){J.yourLogo.$element&&J.yourLogo.resize()},timers:function(){if(0<J.gui.timers.slidebar.$containerElement.length)for(var e=0,t=J.gui.timers.slidebar.$containerElement.length;e<t;e++)J.gui.timers.slidebar.containerElementWidth[e]=J.gui.timers.slidebar.$containerElement[e].width(),J.gui.timers.slidebar.elementWidth[e]=J.gui.timers.slidebar.$element[e].width()}},J.transitions={firstSlide:!0,timelines:{all:["_slideTransition","_slideTimeline","_slideTimelineAlternate","_forceLayersOut","_forceLayersOutMirror","_forceLayersOutPrevious","_forceLayersOutMirrorPrevious"],slide:["_slideTransition"],layers:["_slideTimeline","_slideTimelineAlternate"],allforce:["_forceLayersOut","_forceLayersOutMirror","_forceLayersOutPrevious","_forceLayersOutMirrorPrevious"],prevforce:["_forceLayersOutPrevious","_forceLayersOutMirrorPrevious"],currentforce:["_forceLayersOut","_forceLayersOutMirror"],mirrorforce:["_forceLayersOutMirror","_forceLayersOutMirrorPrevious"],set:function(e,t){for(var i in this[e])J.transitions[this[e][i]]&&t(J.transitions[this[e][i]],this[e][i])}},start:function(){return!!document.body.contains(L)&&(J.slider.isPopup&&J.slider.state.popupIsWaitingForDelay?!(J.slider.state.waitingForPopupTimer=!0):(J.device.scroll.directionAtSlideTransitionStart=J.device.scroll.direction,"always"==J.o.thumbnailNavigation&&(J.gui.navigation.bottom.thumbnails.change(),"ontouchstart"in window||J.gui.navigation.bottom.thumbnails.scroll()),this.layers.out.forced(),void this.slide.init()))},slide:{$wrapper:ie(),init:function(){var e,t,i;J.functions.setStates(J.slider,{animatingSlides:!0}),J.transitions.layers.parallax.reset(),J.slider.$layersWrapper.children('.ls-parallax[data-ls-parallax="active"]').each(function(){ie(this).find(".ls-layer").data(J.defaults.init.dataKey).settings.slideOut===J.slides.current.index&&ie(this).attr("data-ls-parallax","disbaled")}),J.transitions.curSlide=J.slides.current,J.transitions.nextSlide=J.slides.next,J.transitions._slideTransition=new ee.TimelineMax({paused:!0,onComplete:function(){J.transitions.slide.onComplete()}}),(J.transitions.nextSlide.data&&J.transitions.nextSlide.data.transitionorigami||J.slider.isScrollScene)&&(J.o.animateFirstSlide=!1),J.transitions.firstSlide&&J.o.animateFirstSlide&&(J.transitions.curSlide=ie.extend(!0,{},J.transitions.nextSlide),delete J.transitions.curSlide.data.$background,J.transitions.curSlide.data.backgroundColor="transparent"),J.transitions.firstSlide&&!J.o.animateFirstSlide?(void 0!==J.transitions.nextSlide.data.$background&&(e=(i=J.transitions.nextSlide.data.$background.data(J.defaults.init.dataKey)).kenBurns.zoom?i.kenBurns.from.scale:1,t=i.kenBurns.zoom?i.kenBurns.from.rotation:0,i=J.transitions.nextSlide.filter.from||"none",J.transitions._slideTransition.set(J.transitions.nextSlide.data.$background[0],{"-webkit-filter":i,filter:i},0),J.transitions._slideTransition.fromTo(J.transitions.nextSlide.data.$background.closest(".ls-bg-wrap")[0],J.o.sliderFadeInDuration,{autoCSS:!1,css:{scale:e,rotation:t,opacity:0,display:"block"}},{autoCSS:!1,css:{opacity:1}},0)),this.start(!0)):"undefined"==typeof layerSliderTransitions&&"undefined"==typeof layerSliderCustomTransitions?(this.start(!0),J.debugMode&&J.debug.add("warn","slideTransition.noSlideTransition",J.transitions.nextSlide.index)):void 0===J.transitions.curSlide.data.$background&&void 0===J.transitions.nextSlide.data.$background&&"transparent"==J.transitions.curSlide.data.backgroundColor&&"transparent"==J.transitions.nextSlide.data.backgroundColor?this.start(!0):("x"===J.o.clipSlideTransition?te.$overflowWrapper.addClass("ls-overflowx-hidden"):"y"===J.o.clipSlideTransition?te.$overflowWrapper.addClass("ls-overflowy-hidden"):!0===J.o.clipSlideTransition&&te.$overflowWrapper.addClass("ls-overflow-hidden"),void 0!==J.transitions.curSlide.data.$background&&(e=J.transitions.curSlide.data.$background.closest(".ls-bg-wrap")[0]._gsTransform,(t=J.transitions.curSlide.data.$background.data(J.defaults.init.dataKey)).responsive.filter=J.transitions.curSlide.data.$background[0].style.filter,t.responsive.kbRotation=void 0!==e?" rotate("+e.rotation+"deg)":" rotate(0deg)",t.responsive.kbScale=void 0!==e?" scale("+e.scaleX+")":" scale(1)"),J.transitions.slide.$wrapper=ie("<div>").addClass("ls-slide-transition-wrapper").css({width:J.slider.width,height:J.slider.height}),this.select.slideTransitionType())},select:{slideTransitionType:function(){J.transitions.slide.normal.select.transitionType()}},start:function(e){var t,i=!(!J.slides.current.index||!J.slides.current.data.$backgroundVideo.length),a=!(!J.slides.next.index||!J.slides.next.data.$backgroundVideo.length);!J.slideshow.firstStart&&J.api.hasEvent("slideChangeDidStart")&&J.api.triggerEvent("slideChangeDidStart",J.api.eventData()),e||(void 0!==J.transitions.nextSlide.data.transitionDuration?s=J.transitions.nextSlide.data.transitionDuration:void 0!==J.o.slideTransitionDuration&&ie.isNumeric(J.o.slideTransitionDuration)&&-1<J.o.slideTransitionDuration&&(s=J.o.slideTransitionDuration/1e3),ie.isNumeric(s)&&(0===s&&(s+=1e-5),J.transitions._slideTransition.duration(s)),J.transitions.firstSlide&&J.o.animateFirstSlide&&J.o.firstSlideDuration&&J.transitions._slideTransition.duration(J.o.firstSlideDuration/1e3),J.debugMode&&J.debug.options.transitionDuration&&J.transitions._slideTransition.duration(J.debug.options.transitionDuration),.25<J.transitions.layers.timeline.timeScaleModifier&&(r=(r=J.transitions._slideTransition.duration()/(.75+J.transitions.layers.timeline.timeScaleModifier))<.5?.5:r,J.transitions._slideTransition.duration(r)));var e=J.transitions._slideTransition.duration()/J.transitions._slideTransition.timeScale(),s=e,r=J.transitions.nextSlide.data.timeShift||(void 0!==J.o.slideTimeShift&&ie.isNumeric(J.o.slideTimeShift)&&J.o.slideTimeShift<0?J.o.slideTimeShift/1e3:0);0<r?r=0:Math.abs(r)>e&&(r=-e),J.transitions.firstSlide&&J.o.animateFirstSlide&&!1!==J.o.firstSlideTimeShift&&J.o.firstSlideTimeShift<=0&&(r=J.o.firstSlideTimeShift/1e3),J.transitions.nextSlide.data.calculatedTimeShift=r,r=J.transitions.firstSlide&&!J.o.animateFirstSlide?J.o.sliderFadeInDuration+.01:(s+r)*J.transitions._slideTransition.timeScale(),(i||a)&&J.transitions.media.changeBackgroundVideo(J.transitions.firstSlide,i&&a),J.transitions._slideTransition.call(function(){!J.slideshow.firstStart&&J.api.hasEvent("slideChangeWillComplete")&&J.api.triggerEvent("slideChangeWillComplete",J.api.eventData()),J.slideshow.should.change||J.transitions.layers.timeline.prepare(),J.slides.set.slideIndexes(),J.o.hashChange&&(document.location.hash=J.slides[J.slides.current.index].data.deeplink||"_no-deeplink-found_"),J.slideshow.start(),!J.transitions.firstSlide&&J.slides.prev.index&&J.slides.prev.data.$backgroundVideo.length&&!J.slides.prev.data.$backgroundVideo.data(J.defaults.init.dataKey).mediaProperties.willBePaused&&(J.slides.prev.data.$backgroundVideo.trigger("stopBackgroundVideo"),J.slides.prev.data.$backgroundVideo.data(J.defaults.init.dataKey).elements.$bgWrapper.css({display:"none"})),J.slideshow.should.change||J.slides.next.data.$backgroundVideo.length&&J.slides.next.data.$backgroundVideo.data(J.defaults.init.dataKey).mediaProperties&&!J.slides.next.data.$backgroundVideo.data(J.defaults.init.dataKey).mediaProperties.isPreloaded&&(J.slides.next.data.$backgroundVideo.trigger("preloadBackgroundVideo"),J.slides.next.data.$backgroundVideo.data(J.defaults.init.dataKey).mediaProperties.isPreloaded=!0),J.transitions.firstSlide=!1},[],this,r),J.transitions._slideTransition.play(),J.slideshow.forceFastChange&&J.transitions._slideTransition.progress(1),void 0!==J.transitions.curSlide.data&&void 0!==J.transitions.curSlide.data.$background&&(t=J.transitions.curSlide.data.$background.data(J.defaults.init.dataKey),J.timeouts.applyBG=setTimeout(function(){delete J.timeouts.applyBG,J.transitions.curSlide.data.$background.closest(".ls-bg-wrap").hide(),t.kenBurns.zoom&&ee.TweenMax.set(J.transitions.curSlide.data.$background[0],{autoCSS:!1,css:t.kenBurns.from})},5))},onComplete:function(){var e;void 0!==J.transitions.nextSlide.data.$background&&J.transitions.nextSlide.data.$background.closest(".ls-bg-wrap").show(),"transparent"!==J.transitions.nextSlide.data.backgroundColor?J.slider.$slideBGColorWrapper.css("background-color",J.transitions.nextSlide.data.backgroundColor):J.slider.$slideBGColorWrapper.css("background-color","transparent"),J.o.leaveOverflow||te.$overflowWrapper.removeClass("ls-overflowx-hidden ls-overflowy-hidden ls-overflow-hidden"),this.$wrapper&&(this.$wrapper.html("").remove(),this.$wrapper=!1),J.gui.navigation.bottom.bullets.set.active(),0<J.o.cycles&&(J.slideshow.hasOwnProperty("cycleSlideIndex")?J.slideshow.cycles.check(J.transitions.nextSlide.index)&&(J.navigation.stop(),J.functions.setStates(J.slideshow,{pausedByLastCycle:!0}),J.o.forceCycles&&(J.slideshow.curCycle=1)):J.slideshow.cycles.set()),J.functions.setStates(J.slider,{animatingSlides:!1,changingSlides:!1}),!J.slideshow.firstStart&&J.api.hasEvent("slideChangeDidComplete")&&J.api.triggerEvent("slideChangeDidComplete",J.api.eventData()),J.slideshow.firstStart=!1,J.slideshow.forceFastChange=!1,J.slideshow.forceFastChangeCallback&&(J.slideshow.forceFastChangeCallback(),J.slideshow.forceFastChangeCallback=!1),J.slideshow.should.change?J.navigation.forceDirection?(void 0!==J.transitions.curSlide.data&&void 0!==J.transitions.curSlide.data.$background&&(e=J.transitions.curSlide.data.$background.data(J.defaults.init.dataKey),J.transitions.curSlide.data.$background.closest(".ls-bg-wrap").hide(),e.kenBurns.zoom&&ee.TweenMax.set(J.transitions.curSlide.data.$background[0],{autoCSS:!1,css:e.kenBurns.from})),J.slideshow.changeTo(J.slideshow.get.slideInSequence(J.navigation.forceDirection),!0),J.navigation.forceDirection=!1):J.slideshow.forceSlideChangeTo?J.slideshow.changeTo(J.slideshow.forceSlideChangeTo,!0,!0):J.preload.imagesOfSlide(J.slides.next.index):J.preload.imagesOfSlide(J.slides.next.index)},normal:{select:{transitionType:function(){var e,t,i;J.o.slideTransition?J.transitions.slide.normal.setTransition(J.o.slideTransition.type,J.o.slideTransition.obj):(i=!!J.transitions.nextSlide.data.transition2d&&J.transitions.nextSlide.data.transition2d.toString().split(","),J.device.touchPrev&&J.o.slideOnSwipe?(J.device.touchPrev=!1,this.transition("2d","1")):J.device.touchNext&&J.o.slideOnSwipe?(J.device.touchNext=!1,this.transition("2d","1")):J.slides.next.data.$background||i&&(!i||-1!=i.indexOf("1")||-1!=i.indexOf("2")||-1!=i.indexOf("3")||-1!=i.indexOf("4"))?J.browser.supports3D()&&(J.transitions.nextSlide.data.transition3d||J.transitions.nextSlide.data.customtransition3d)?J.transitions.nextSlide.data.transition3d&&J.transitions.nextSlide.data.customtransition3d?(e=Math.floor(2*Math.random()),t=[["3d",J.transitions.nextSlide.data.transition3d],["custom3d",J.transitions.nextSlide.data.customtransition3d]],this.transition(t[e][0],t[e][1])):J.transitions.nextSlide.data.transition3d?this.transition("3d",J.transitions.nextSlide.data.transition3d):this.transition("custom3d",J.transitions.nextSlide.data.customtransition3d):J.transitions.nextSlide.data.transition2d&&J.transitions.nextSlide.data.customtransition2d?(e=Math.floor(2*Math.random()),t=[["2d",J.transitions.nextSlide.data.transition2d],["custom2d",J.transitions.nextSlide.data.customtransition2d]],this.transition(t[e][0],t[e][1])):J.transitions.nextSlide.data.transition2d?this.transition("2d",J.transitions.nextSlide.data.transition2d):J.transitions.nextSlide.data.customtransition2d?this.transition("custom2d",J.transitions.nextSlide.data.customtransition2d):this.transition("2d","1"):this.transition("2d","5"))},transition:function(e,t){J.debugMode&&J.debug.add("group","slideTransition.info"),t+="";var i,a,s=-1==e.indexOf("custom")?J.t:J.ct,r="3d";-1!=e.indexOf("2d")&&(r="2d"),a=-1!=t.indexOf("last")?s["t"+r].length-1:-1!=t.indexOf("all")?Math.floor(Math.random()*J.functions.countProp(s["t"+r])):(t=(i=t.split(",")).length,parseInt(i[Math.floor(Math.random()*t)])-1),void 0===s["t"+r][a]&&(J.debugMode&&J.debug.add("warn","slideTransition.customTransition",[r.toUpperCase()+(-1===e.indexOf("custom")?"":" (CUSTOM)"),a+1]),s=J.t,e=r="2d",a=0),J.debugMode&&J.debug.add("log","slideTransition.info",[r.toUpperCase()+(-1===e.indexOf("custom")?"":" (CUSTOM)"),a+1,s["t"+r][a].name]),J.transitions.slide.normal.setTransition(r,s["t"+r][a])}},setTransition:function(e,t){t.name||(t=_lsConvTrProp(t));var i,a,s,r,o,n=ie.extend(!0,{cols:1,rows:1},t),l=typeof n.cols,d=typeof n.rows,p=[],c=J.navigation.direction,u=0,h=0,m=!!J.transitions.curSlide.data.$background&&J.functions.getURL(J.transitions.curSlide.data.$background),f=!!J.transitions.nextSlide.data.$background&&J.functions.getURL(J.transitions.nextSlide.data.$background),g=J.o.playByScroll&&"up"===J.device.scroll.direction?"to":"from";if(n.rc){switch(typeof n.rc){case"number":J.slider.width>J.slider.height?(d=n.rc,l=Math.floor(J.slider.width/(J.slider.height/n.rc))):(l=n.rc,d=Math.floor(J.slider.height/(J.slider.width/n.rc)));break;case"string":s=parseInt(n.rc),l=Math.floor(J.slider.width/s),d=Math.floor(J.slider.height/s)}n.cols=l,n.rows=d}else{switch(l){case"number":l=n.cols;break;case"string":l=Math.floor(Math.random()*(parseInt(n.cols.split(",")[1])-parseInt(n.cols.split(",")[0])+1))+parseInt(n.cols.split(",")[0]);break;default:l=Math.floor(Math.random()*(n.cols[1]-n.cols[0]+1))+n.cols[0]}switch(d){case"number":d=n.rows;break;case"string":d=Math.floor(Math.random()*(parseInt(n.rows.split(",")[1])-parseInt(n.rows.split(",")[0])+1))+parseInt(n.rows.split(",")[0]);break;default:d=Math.floor(Math.random()*(n.rows[1]-n.rows[0]+1))+n.rows[0]}te.isMobile&&J.o.optimizeForMobile&&(15<=l?l=7:5<=l?l=4:4<=l?l=3:2<l&&(l=2),15<=d?d=7:5<=d?d=4:4<=d?d=3:2<d&&(d=2),2<d&&2<l&&(d=2,4<l&&(l=4)))}J.debugMode&&!J.o.slideTransition&&(J.debug.add("log","slideTransition.properties",[[l,d],l*d]),J.debug.groupEnd()),i=Math.floor(J.slider.width/l),a=Math.floor(J.slider.height/d),l=Math.floor(J.slider.width/i),d=Math.floor(J.slider.height/a),r=J.slider.width-i*l,o=J.slider.height-a*d,"prev"==c&&(t={random:"random",forward:"reverse",reverse:"forward",center:"center",edge:"edge",mirror:"mirror","radial-in":"radial-in","radial-out":"radial-out","col-forward":"col-reverse","col-reverse":"col-forward"},"2d"===e&&n.transition&&"scale"===n.transition.type&&(t.forward="forward",t.reverse="reverse"),n.tile&&n.tile.sequence&&(n.tile.sequence=t[n.tile.sequence]),ie.each(["animation","before","after"],function(e,t){n[t]&&n[t].transition&&((t=n[t].transition).rotateX&&44<Math.abs(t.rotateX)&&(t.rotateX*=-1),t.rotateY&&44<Math.abs(t.rotateY)&&(t.rotateY*=-1),t.rotate&&(t.rotate*=-1))}));for(var y,v,x=0;x<l*d;x++)p.push(x);switch(n.tile.sequence){case"reverse":p.reverse();break;case"col-forward":p=J.functions.sortArray(d,l,"forward");break;case"col-reverse":p=J.functions.sortArray(d,l,"reverse");break;case"random":p=J.functions.shuffleArray(p);break;case"center":p=J.functions.sortArray(d,l,"center");break;case"edge":case"mirror":p=J.functions.sortArray(d,l,"edge");break;case"radial-out":p=J.functions.sortArray(d,l,"radial-out");break;case"radial-in":p=J.functions.sortArray(d,l,"radial-in")}("transparent"===J.transitions.nextSlide.data.backgroundColor||"3d"==e||void 0!==J.transitions.curSlide.data.$background&&void 0!==J.transitions.nextSlide.data.$background)&&J.slider.$slideBGColorWrapper.css("background-color","transparent"),"2d"==e&&(y=-1!=n.name.toLowerCase().indexOf("carousel"),v=-1!=n.name.toLowerCase().indexOf("crossfad"),this.$curTiles=ie("<div>").addClass("ls-curtiles").appendTo(J.transitions.slide.$wrapper),this.$nextTiles=ie("<div>").addClass("ls-nexttiles").appendTo(J.transitions.slide.$wrapper));for(var w=0;w<l*d;w++){var b,S=(w+1)%l==0?r:0,T=(d-1)*l-1<w?o:0,k=ie("<div>").addClass("ls-slide-transition-tile").css({width:i+S,height:a+T}).data("style",{width:i+S,height:a+T}).appendTo(J.transitions.slide.$wrapper),u=(p[w],w%l==0?u+1:u),h=w%l==0?1:h+1;if("3d"==e){k.addClass("ls-3d-container");var C,P,O=i+S,I=a+T,L=new ee.TimelineMax,$=Math.abs(Math.abs(h-l/2-.5)-l/2-.5)*Math.abs(Math.abs(u-d/2-.5)-d/2-.5);k.css({zIndex:$}),C=O/2,P=I/2,T=($="horizontal"==n.animation.direction?90<Math.abs(n.animation.transition.rotateY)&&"large"!=n.tile.depth?Math.floor(O/7)+S:O:90<Math.abs(n.animation.transition.rotateX)&&"large"!=n.tile.depth?Math.floor(I/7)+T:I)/2,this.createCuboids("ls-3d-box",k,0,0,0,0,-T,0,0,C+"px "+P+"px 0px"),this.createCuboids("ls-3d-front",k.find(".ls-3d-box"),O,I,0,0,T,0,0),"vertical"==n.animation.direction&&90<Math.abs(n.animation.transition.rotateX)?this.createCuboids("ls-3d-back",k.find(".ls-3d-box"),O,I,0,0,-T,180,0):this.createCuboids("ls-3d-back",k.find(".ls-3d-box"),O,I,0,0,-T,0,180),this.createCuboids("ls-3d-left",k.find(".ls-3d-box"),$,I,-T,0,0,0,-90),this.createCuboids("ls-3d-right",k.find(".ls-3d-box"),$,I,O-T,0,0,0,90),this.createCuboids("ls-3d-top",k.find(".ls-3d-box"),O,$,0,-T,0,90,0),this.createCuboids("ls-3d-bottom",k.find(".ls-3d-box"),O,$,0,I-T,0,-90,0),O=k.find(".ls-3d-front"),$="horizontal"==n.animation.direction?90<Math.abs(n.animation.transition.rotateY)?k.find(".ls-3d-back"):0<n.animation.transition.rotateY?k.find(".ls-3d-left"):k.find(".ls-3d-right"):90<Math.abs(n.animation.transition.rotateX)?k.find(".ls-3d-back"):0<n.animation.transition.rotateX?k.find(".ls-3d-bottom"):k.find(".ls-3d-top"),I=p[w]*n.tile.delay,T=J.transitions.slide.$wrapper.find(".ls-3d-container:eq( "+w+" ) .ls-3d-box"),n.before&&n.before.transition?(n.before.transition.delay=n.before.transition.delay?(n.before.transition.delay+I)/1e3:I/1e3,L.to(T[0],n.before.duration/1e3,J.functions.convert.transition(n.before.transition,n.before.easing))):n.animation.transition.delay=n.animation.transition.delay?(n.animation.transition.delay+I)/1e3:I/1e3,L.to(T[0],n.animation.duration/1e3,J.functions.convert.transition(n.animation.transition,n.animation.easing)),n.after&&(n.after.transition||(n.after.transition={}),L.to(T[0],n.after.duration/1e3,J.functions.convert.transition(n.after.transition,n.after.easing,"after"))),J.transitions._slideTransition.add(L,0)}else{var W,M,B,_,D,F,A,z,V,N="auto",R="auto",E="auto",H="auto",Y=1,X=1,K="50% 50%",U="50% 50%",j=0,G=100,q={},Q=n.transition.intensity||2,Z="random"==n.transition.direction?(A="scale"==n.transition.type?["top","bottom","middle","right","left","center"]:["top","bottom","right","left"])[Math.floor(Math.random()*A.length)]:n.transition.direction;switch(-1!=n.name.toLowerCase().indexOf("mirror")&&w%2==0&&(c="prev"==c?"next":"prev"),"prev"==c&&(Z={top:"bottom",bottom:"top",middle:"middle",left:"right",right:"left",center:"center",topleft:"bottomright",topright:"bottomleft",bottomleft:"topright",bottomright:"topleft",none:"none"}[Z]),Z){case"top":N=E=-k.data("style").height,R=H=0;break;case"bottom":N=E=k.data("style").height,R=H=0;break;case"left":N=E=0,R=H=-k.data("style").width;break;case"right":N=E=0,R=H=k.data("style").width;break;case"topleft":N=k.data("style").height,E=0,R=k.data("style").width,H=0;break;case"topright":N=k.data("style").height,E=0,R=-k.data("style").width,H=0;break;case"bottomleft":N=-k.data("style").height,E=0,R=k.data("style").width,H=0;break;case"bottomright":N=-k.data("style").height,E=0,R=-k.data("style").width,H=0;break;case"none":H=R=E=N=0}switch(this.scale2D=void 0!==n.transition.scale?n.transition.scale:1,1==y&&1!=this.scale2D&&(N/=2,E/=2,R/=2,H/=2),(n.transition.rotate||n.transition.rotateX||n.transition.rotateY||1!=this.scale2D)&&"slide"!=n.transition.type?k.css({overflow:"visible"}):k.css({overflow:"hidden"}),1==y?this.$curTiles.css({overflow:"visible"}):this.$curTiles.css({overflow:"hidden"}),!0===v||"slide"==n.transition.type||"scale"==n.transition.type||!0===y||n.transition.hasOwnProperty("opacity")?(z=k.appendTo(this.$curTiles),D=k.clone().appendTo(this.$nextTiles),O=ie("<div>").addClass("ls-curtile").appendTo(z)):D=k.appendTo(this.$nextTiles),$=ie("<div>").addClass("ls-nexttile").appendTo(D),F=p[w]*n.tile.delay/1e3,L=n.transition.rotate||0,A=n.transition.rotateX||0,z=n.transition.rotateY||0,"prev"==c&&(L=-L,A=-A,z=-z),n.transition.type){case"fade":Y=N=E=R=H=0,X=1;break;case"mixed":Y=0,(X=1)==this.scale2D&&(E=H=0),W=void 0!==n.transition.scaleX?n.transition.scaleX:void 0!==n.transition.scale?n.transition.scale:1,M=void 0!==n.transition.scaleY?n.transition.scaleY:void 0!==n.transition.scale?n.transition.scale:1;break;case"scale":switch(Y=N=E=R=H=0,M=W=X=1,Z){case"left":case"right":W=1+p[w]/(50/Q);break;case"top":case"bottom":M=1+p[w]/(50/Q);break;case"center":W=1+p[w]/(50/Q);break;case"middle":M=1+p[w]/(50/Q)}switch("mirror"===n.tile.sequence&&l*d/2<w&&(j=100,G=0),Z){case"left":K=j+"% 50%",U=G+"% 0";break;case"right":K=G+"% 50%",U=j+"% 50%";break;case"center":case"middle":_=B=1;break;case"top":K="50% "+j+"%",U="50% "+G+"%";break;case"bottom":K="50% "+G+"%",U="50% "+j+"%"}}J.transitions._slideTransition.fromTo($[0],n.transition.duration/1e3,{immediateRender:!1,autoCSS:!1,css:{x:-R,y:-N,display:"block",opacity:Y,rotation:L,rotationX:A,rotationY:z,borderRadius:n.transition.borderRadius||0,scaleX:void 0!==W?W:this.scale2D,scaleY:void 0!==M?M:this.scale2D,transformOrigin:K}},{autoCSS:!1,css:{x:0,y:0,opacity:X,rotation:0,rotationX:0,rotationY:0,borderRadius:0,scaleX:1,scaleY:1},ease:J.functions.convert.easing(n.transition.easing)},F),1==v&&(void 0===J.transitions.nextSlide.data.$background||void 0!==J.transitions.nextSlide.data.$background&&(-1!=J.transitions.nextSlide.data.$background.attr("src").toLowerCase().indexOf("png")||J.transitions.nextSlide.data.$background.width()<J.slider.width||J.transitions.nextSlide.data.$background.height()<J.slider.height))&&(q.opacity=0),"slide"!=n.transition.type&&1!=y||-1!=n.name.toLowerCase().indexOf("mirror")?"scale"==n.transition.type&&(q.scaleX=B||W,q.scaleY=_||M,q.transformOrigin=U):(V=0!==L?-L:0,q.x=H,q.y=E,q.rotation=V,q.scale=this.scale2D,q.opacity=Y),n.transition.hasOwnProperty("opacity")&&(q.opacity=n.transition.opacity),void 0!==O&&J.transitions._slideTransition.to(O[0],n.transition.duration/1e3,{autoCSS:!1,css:q,ease:J.functions.convert.easing(n.transition.easing)},F)}V=w%l*i,q=Math.floor(w/l)*a,void 0!==J.transitions.curSlide.data.$background&&(b=J.transitions.curSlide.data.$background.data(J.defaults.init.dataKey),"3d"===e||"2d"===e&&(!0===v||"slide"===n.transition.type||"scale"===n.transition.type||!0===y||n.transition.hasOwnProperty("opacity"))?O.append(ie('<div style="position: absolute;">').css({width:J.slider.width,height:J.slider.height,left:-V,top:-q,transform:b.responsive.kbRotation+b.responsive.kbScale}).append(ie("<img>").attr("src",m).css({width:b.responsive.width,height:b.responsive.height,filter:b.responsive.filter,transform:"translateX("+b.responsive.x+"px) translateY("+b.responsive.y+"px)"}))):0===this.$curTiles.children().length&&this.$curTiles.append(ie('<div style="position: absolute;">').css({width:J.slider.width,height:J.slider.height,left:-V,top:-q,transform:b.responsive.kbRotation+b.responsive.kbScale}).append(ie("<img>").attr("src",m).css({width:b.responsive.width,height:b.responsive.height,filter:b.responsive.filter,transform:"translateX("+b.responsive.x+"px) translateY("+b.responsive.y+"px)"})))),"transparent"===J.transitions.curSlide.data.backgroundColor||J.transitions.curSlide.data.$backgroundVideo.length||("3d"===e||"2d"===e&&(!0===v||"slide"===n.transition.type||!0===y)?O:this.$curTiles).css("background-color",J.transitions.curSlide.data.backgroundColor),void 0!==J.transitions.nextSlide.data.$background&&(b=(F=J.transitions.nextSlide.data.$background.data(J.defaults.init.dataKey)).kenBurns[g],$.append(ie('<div style="position: absolute;">').css({width:J.slider.width,height:J.slider.height,left:-V,top:-q,transform:"rotate("+b.rotation+"deg) scale("+b.scale+")"}).append(ie("<img>").attr("src",f).css({width:F.responsive.width,height:F.responsive.height,filter:J.transitions.nextSlide.filter.from||"none",transform:"translateX("+F.responsive.x+"px) translateY("+F.responsive.y+"px)"})))),"transparent"===J.transitions.nextSlide.data.backgroundColor||J.transitions.nextSlide.data.$backgroundVideo.length||$.css("background-color",J.transitions.nextSlide.data.backgroundColor)}J.transitions.slide.$wrapper.prependTo(J.o.preferBlendMode?J.slider.$layersWrapper:J.slider.$innerWrapper),J.transitions.slide.start()},createCuboids:function(e,t,i,a,s,r,o,n,l,d){o="translate3d( "+s+"px, "+r+"px, "+o+"px)";0!==n&&(o+="rotateX( "+n+"deg)"),0!==l&&(o+="rotateY( "+l+"deg)");o={width:i,height:a,transform:o,"-ms-transform":o,"-webkit-transform":o};d&&(o["transform-origin"]=d,o["-ms-transform-origin"]=d,o["-webkit-transform-origin"]=d),ie("<div>").addClass(e).css(o).appendTo(t)}}},layers:{setZIndex:function(e,t,i){t.original._zIndex||(t.original._zIndex=parseInt(t.elements.$outerWrapper.css("zIndex"))),i!==parseInt(t.elements.$outerWrapper.css("zIndex"))&&t.elements.$outerWrapper.css({zIndex:i})},in:{onStart:function(e){var t=e.data(J.defaults.init.dataKey);t.is.countdown&&J.countdown.startInterval(e,t.countdownSettings),t.hover.enabled&&J.transitions.layers.hover.enable(e),J.layers.set.dataAttribute("add",e,"animating-in"),t.in.zIndex&&J.transitions.layers.setZIndex(e,t,t.in.zIndex),"instant"===t.mediaSettings.autoplay&&J.media.functions.playIfAllowed(e)},onComplete:function(e){var t=e.data(J.defaults.init.dataKey);"instant"!==t.mediaSettings.autoplay&&J.media.functions.playIfAllowed(e),t.in.zIndex&&J.transitions.layers.setZIndex(e,t,t.original._zIndex),J.layers.set.dataAttribute("remove",e,"animating-in"),J.layers.set.dataAttribute("add",e,"active")}},out:{forced:function(){var s,r,o,n,e,t,i;J.transitions._forceLayersOut&&(J.transitions._slideTimeline&&(s=new ee.TimelineMax({paused:!0,autoRemoveChildren:!0}),n=[],e=J.layers.get("current, in, static").add(J.layers.get("current, out, static, active")),i=J.layers.get("current, out, notstatic, active"),t=J.layers.get("current, out, active"),i=ie().add(e).add(i),i.each(function(){var e,t=ie(this).data(J.defaults.init.dataKey);if(t.loop._timeline&&(J.transitions._slideTimeline.remove(t.loop._timeline),t.loop._timeline.play()),t.is.static){r=[t.elements.$wrapper[0]],t.elements.$clipWrapper&&(r=r.concat(t.elements.$clipWrapper[0])),t.textIn.nodes&&(r=r.concat(t.textIn.nodes)),t.textOut.nodes&&(r=r.concat(t.textOut.nodes));for(var i=0;i<r.length;i++)n=n.concat(J.transitions._slideTimeline.getTweensOf(r[i],!0),J.transitions._slideTimelineAlternate.getTweensOf(r[i],!0));for(var a=0;a<n.length;a++)n[a].duration&&0!==n[a].duration()&&(o=n[a],e=o,s.add(e,100-e.duration()*e.progress()))}}),t.each(function(){ie(this).data(J.defaults.init.dataKey).should.reset=!0}),s.play().seek(100),J.transitions._slideTimeline.eventCallback("onStart",null),J.transitions._slideTimeline.eventCallback("onComplete",null),J.transitions._slideTimeline.eventCallback("onReverseComplete",null),J.transitions._slideTimeline.eventCallback("onUpdate",null),J.transitions._slideTimeline.stop().clear(),J.transitions._slideTimelineAlternate.stop().clear()),("prev"===J.navigation.direction?J.transitions._forceLayersOutMirror:J.transitions._forceLayersOut).play(),J.slideshow.forceFastChange&&(J.transitions._forceLayersOut.progress(1),J.transitions._forceLayersOutMirror.progress(1))),J.slider.$layersWrapper.find(".ls-link").css({display:"none"})},onStart:function(e){var t=e.data(J.defaults.init.dataKey);t.out.zIndex?J.transitions.layers.setZIndex(e,t,t.out.zIndex):t.in.zIndex&&J.transitions.layers.setZIndex(e,t,t.original._zIndex),J.layers.set.dataAttribute("add",e,"animating-out")},onComplete:function(e){var t=e.data(J.defaults.init.dataKey);!J.slider.state.changingSlides&&t.settings.slideOut===J.slides.current.index||J.transitions.layers.reset(e,t),t.hover.enabled&&J.transitions.layers.hover.disable(e),J.layers.set.dataAttribute("remove",e,"animating-out"),J.layers.set.dataAttribute("add",e,"hidden"),t.is.mediaLayer&&t.mediaProperties&&t.mediaProperties.type&&J.media.functions.stop(!0,e),t.is.countdown&&J.countdown.stopInterval(t.countdownSettings),t.is.counter&&J.counter.stop(t.counterSettings)}},reset:function(e,t){var i;t.original._zIndex&&J.transitions.layers.setZIndex(e,t,t.original._zIndex),t.loop._timeline&&(t.loop._timeline.stop().clear(),delete t.loop._timeline,ee.TweenMax.set(t.elements.$loopWrapper[0],t.reset.altWrapperOnSlideChange)),t.parallax._timeline&&(t.parallax._timeline.stop().clear(),delete t.parallax._timeline),t.parallax._timeout&&(J.functions.clearTimers({timeout:t.parallax._timeout}),delete t.parallax._timeout),t.parallax.reset&&(ee.TweenMax.killTweensOf(t.elements.$parallaxWrapper[0]),ee.TweenMax.set(t.elements.$parallaxWrapper[0],t.reset.altWrapperOnSlideChange)),t.clip.enabled&&(i=t.clip.userDefined?t.clip.default:t.clip.style,t.loop.enabled&&ee.TweenMax.set(t.elements.$loopWrapper[0],i),t.inClipFromCSS.clipPath||ee.TweenMax.set(t.elements.$clipWrapper[0],i)),ee.TweenMax.set(t.elements.$wrapper[0],t.reset.wrapperOnSlideChange),ee.TweenMax.set(e[0],{"-webkit-filter":t.original.filter,filter:t.original.filter}),t.should.update&&(t.textInNodesFrom.random={},t.textOutNodesTo.random={},J.layers.update.data(e)),t.should.reset=!1},timeline:{shouldRestart:!1,create:function(e){var t,i,a,s=e?"current":"next";if(J.transitions.curNext=s,J.o.allowRestartOnResize?J.slider.isScrollScene&&(J.transitions.layers.timeline.shouldRestart=!0):J.transitions.layers.timeline.shouldRestart=!1,J.transitions.layers.timeline.resetStates(),J.transitions.timelines.set("layers",function(e,t){e.pause().progress(0).kill().clear(!0),e=null}),J.transitions._slideTimeline=new ee.TimelineMax({paused:!0,onStart:function(){J.api.hasEvent("slideTimelineDidStart")&&J.api.triggerEvent("slideTimelineDidStart",J.api.eventData())},onComplete:function(){J.o.playByScroll&&J.o.playByScrollSkipSlideBreaks&&("next"===J.slideshow.direction?J.transitions.layers.timeline.scrollForward(!0):J.transitions.layers.timeline.scrollBackwards(!0,!0))},onReverseComplete:function(){J.api.hasEvent("slideTimelineDidReverseComplete")&&J.api.triggerEvent("slideTimelineDidReverseComplete",J.api.eventData()),J.transitions.layers.timeline.shouldReplay&&(J.transitions.layers.timeline.shouldRestart=!1,J.transitions._slideTimeline.play()),J.o.playByScroll&&J.o.playByScrollSkipSlideBreaks&&J.transitions.layers.timeline.scrollBackwards(!0,!1)},onUpdate:function(e){J.api.hasEvent("slideTimelineDidUpdate")&&J.api.triggerEvent("slideTimelineDidUpdate",e)},onUpdateParams:["{self}"]}),J.transitions._slideTimelineAlternate=new ee.TimelineMax({paused:!0}),this.totalDuration=0,this.progress=1,J.transitions.timelines.set("prevforce",function(e,t){e.progress(1).kill().clear(!0),e=null}),J.transitions._forceLayersOut&&(J.transitions._forceLayersOutPrevious=J.transitions._forceLayersOut),J.transitions._forceLayersOutMirror&&(J.transitions._forceLayersOutMirrorPrevious=J.transitions._forceLayersOutMirror),J.transitions._forceLayersOut=new ee.TimelineMax({paused:!0,autoRemoveChildren:!0}),J.transitions._forceLayersOutMirror=new ee.TimelineMax({paused:!0,autoRemoveChildren:!0}),t=J.layers.get(s+", in, notactive"),i=J.layers.get(s+", out, notstatic").add(J.layers.get(s+", out, active, static")),e=J.layers.get(s+", in, bgonly, notactive"),a=ie().add(t).add(i).add(e),this.addLayers(t,"in",J.transitions._slideTimeline,J.transitions._forceLayersOut,J.transitions._slideTimelineAlternate,J.transitions._forceLayersOutMirror),this.addLayers(i,"out",J.transitions._slideTimeline,J.transitions._forceLayersOut,J.transitions._slideTimelineAlternate,J.transitions._forceLayersOutMirror),J.slides[s].data&&-1!==J.slides[s].data.duration&&J.slides[s].data.duration<this.totalDuration?(this.progress=J.slides[s].data.duration/this.totalDuration,J.debugMode&&J.debug.add("warn","slideTimeline.duration",[J.slides[s].data.duration,this.totalDuration])):J.transitions._slideTimeline.duration()>this.totalDuration&&(this.progress=this.totalDuration/J.transitions._slideTimeline.duration()),-1===J.slides[s].data.duration?(J.o.slideDuration&&ie.isNumeric(J.o.slideDuration)&&-1<J.o.slideDuration?this.totalDuration=J.o.slideDuration/1e3:0===this.totalDuration&&(this.totalDuration=J.o.slideDurationWithoutLayers/1e3),J.o.inLayerPreview&&(J.o.hasInfiniteLoop&&"loop-transition"===J.o.layerPreviewPresetType?this.totalDuration+=5:"ending-transition"===J.o.layerPreviewPresetType?this.totalDuration+=.25:"hover-transition"!==J.o.layerPreviewPresetType&&(this.totalDuration+=.5)),J.slides[s].data.duration=this.totalDuration,J.slides[J.slides[s].index].data.duration=this.totalDuration):this.totalDuration=J.slides[s].data.duration,!J.functions.getData("slideDuration",this.totalDuration))return!1;J.slider.isScrollScene&&(J.o.sceneDuration||(J.o.sceneDuration=this.totalDuration,J.resize.scene()),J.slider.$spacingWrapper.attr("data-scene-duration",J.o.sceneDuration)),this.addLayers(e,"in",J.transitions._slideTimeline,J.transitions._forceLayersOut,J.transitions._slideTimelineAlternate),!0===J.transitions.layers.timeline.shouldRestart&&J.debugMode&&J.debug.add("warn","slideTimeline.restart",J.o.allowRestartOnResize?"enabled":"disabled");for(var r=0;r<a.length;r++)ie(a[r]).data(J.defaults.init.dataKey).parallax.enabled&&ie(a[r]).data(J.defaults.init.dataKey).elements.$parallaxWrapper.attr("data-ls-parallax","active");J.transitions.layers.parallax.trigger();var o,e=J.transitions.layers.parallax.wrappers;e.randomGlobal[J.slides.next.index]&&(J.functions.objectIsEmpty(e.randomGlobal[J.slides.next.index].$2d)&&J.functions.objectIsEmpty(e.randomGlobal[J.slides.next.index].$3d)||J.timeouts["rp-global-"+J.slides.next.index]||J.transitions.layers.parallax.random.setTimer({globalSlideParallaxData:J.slides.next.parallax,slideIndex:J.slides.next.index})),J.transitions.layers.scroll.reset(),J.transitions.layers.scroll.trigger(),J.api.hasEvent("slideTimelineDidCreate")&&J.api.triggerEvent("slideTimelineDidCreate",{slideTimeline:J.transitions._slideTimeline,layersOnSlideTimeline:a,slideTimelineDuration:this.totalDuration}),J.transitions.timers.create(),J.transitions.timers.bar._transition&&(J.gui.timers.bar.$element.css("width",0),J.transitions._slideTimeline.add(J.transitions.timers.bar._transition.play(),0)),J.transitions.timers.circle._transition&&J.transitions._slideTimeline.add(J.transitions.timers.circle._transition.play(),0),J.transitions.timers.slidebar._transition&&J.transitions._slideTimeline.add(J.transitions.timers.slidebar._transition.play(),0),J.transitions._slideTimeline.call(function(){if(!J.transitions._slideTimeline.reversed()){var e;if(J.api.hasEvent("slideTimelineDidComplete"))if(J.o.inLayerPreview&&J.o.hasInfiniteLoop&&J.layers.get("current, in").each(function(){(e=ie(this).data(J.defaults.init.dataKey)).loop&&e.loop._timeline&&e.loop._timeline.stop().progress(0)}),!1===J.api.triggerEvent("slideTimelineDidComplete",J.api.eventData()))return;J.functions.setStates(J.transitions.layers.timeline,{finished:!0}),!J.slideshow.isPaused()&&J.slideshow.state.running?J.slideshow.changeTo(J.slides.next.index):J.slideshow.state.pausedByLastCycle&&J.transitions.timers.reverse()}},[],this,J.slides[s].data.duration),J.slides.next.data.$link&&J.slides.next.data.$link.css({display:"block"}),(!J.o.startInViewport||"inside"!==J.slider.position.toViewport&&!J.o.playByScrollStart&&J.slider.state.waitForGettingInViewport)&&J.o.startInViewport||!(J.slider.isPopup&&J.slider.state.popupIsVisible&&J.slider.state.popupShouldStart)&&J.slider.isPopup||(J.o.pauseLayers&&J.slideshow.isPaused()?J.transitions.layers.timeline.pause(0):J.slider.isScrollScene||J.transitions.layers.timeline.play(),J.o.playByScroll&&"up"===J.device.scroll.directionAtSlideTransitionStart&&J.transitions._slideTimeline.progress(1)),J.transitions._slideTimelineAlternate.play(),J.slider.isScrollScene&&J.slider.set.offset(),$.trigger("mouseleave.globalhover"+W),$.off("mouseenter.globalhover"+W+" mouseleave.globalhover"+W+" mousemove.globalhover"+W),J.slides[s].data.globalhover&&(o=J.layers.get(s+",in,notactive").add(J.layers.get("static,active")).not("[data-ls-skipglobalhover]"),$.on("mouseenter.globalhover"+W,function(){o.each(function(){J.transitions.layers.hover.mouseEnter(ie(this),ie(this).data(J.defaults.init.dataKey))})}),$.on("mouseleave.globalhover"+W,function(){o.each(function(){J.transitions.layers.hover.mouseLeave(ie(this),ie(this).data(J.defaults.init.dataKey))})}),$.on("mousemove.globalhover"+W,function(){o.each(function(){J.transitions.layers.hover.mouseMove(ie(this),ie(this).data(J.defaults.init.dataKey))})}))},prepare:function(){J.slides.next.data.overflow&&"hidden"!==J.slides.next.data.overflow?(J.slider.$layersWrapper.addClass("ls-visible"),J.slider.$slideBGWrapper.addClass("ls-visible")):(J.slider.$layersWrapper.removeClass("ls-visible"),J.slider.$slideBGWrapper.removeClass("ls-visible")),this.create()},getTiming:function(e,t,i,a){if("number"==typeof t)return t;t=t.toLowerCase();var s,r,o,n,l,d=J.defaults.layer.timelineHierarchy,p=0;if(-1!==t.indexOf("*")&&(l="*"),-1!==t.indexOf("/")&&(l="/"),-1!==t.indexOf("+")&&(l="+"),-1!==t.indexOf("-")&&(l="-"),l)if(n=t.split(l),s=ie.trim(n[0]),o=parseInt(ie.trim(n[1])),d[s]&&-1!==d[i][1].indexOf(d[s][0])){if(!e.timeline[s]&&1<d[s][0]){var c,u,h=d[s][0]-1||1;for(u in d)d[u][0]===h&&(c=u);s=c}if(r="number"==typeof e.timeline[s]?e.timeline[s]:e.timeline[s](e),a)p=o/1e3;else switch(l){case"*":p=r*o;break;case"/":p=r/o;break;case"+":p=r+o/1e3;break;case"-":p=r-o/1e3}}else J.debugMode&&(d[s]||J.debug.add("warn","layerTransition.timing1",s),-1===d[i][1].indexOf(d[s][0])&&J.debug.add("warn","layerTransition.timing3",[s,d[s],i,d[i]])),"+"!==l&&!a||(p=o/1e3);else d[s=ie.trim(t)]&&-1!==d[i][1].indexOf(d[s][0])?p=a?0:"number"==typeof e.timeline[s]?e.timeline[s]:e.timeline[s](e):J.debugMode&&(d[s]?-1===d[i][1].indexOf(d[s][0])&&J.debug.add("warn","layerTransition.timing3",[s,d[s],i,d[i]]):J.debug.add("warn","layerTransition.timing1",s));return(p!=p||p<0)&&(J.debugMode&&J.debug.add("warn","layerTransition.timing2",[i,s,p]),p=0),p},addLayers:function(e,t,i,a,s,r){for(var o=0,n=e.length;o<n;o++){var l,d,p,c,u,h=e[o],m=ie(h),f=m.data(J.defaults.init.dataKey),g=f.elements.$wrapper,y=f.elements.$clipWrapper,v=f.elements.$loopWrapper,x=f.elements.$parallaxWrapper,w=(f.elements.$scrollWrapper,f.elements.$scrollTransformWrapper,f.settings.skipViewport&&J.slideshow.state.changed<1?s:i),b="loop"==f.settings.skipViewport&&J.slideshow.state.changed<1?s:i,S=ie.extend(!0,{},f.inLayerFrom),T=S.css,k=ie.extend({},f.inLayerShouldBeConverted),C=ie.extend(!0,{},f.outLayerTo),P=C.css,O=ie.extend({},f.outLayerShouldBeConverted),I=ie.extend(!0,{},f.textInNodesFrom),L=ie.extend(!0,{},f.textInShouldBeConverted),$=ie.extend(!0,{},f.textOutNodesTo),W=ie.extend(!0,{},f.textOutShouldBeConverted);if(f.should.reset&&J.transitions.layers.reset(m,f),m.hasClass("ls-bg"))f.kenBurns.zoom&&i.fromTo(m.closest(".ls-bg-wrap"),J.transitions.nextSlide.data.duration+J.transitions.nextSlide.data.calculatedTimeShift,{autoCSS:!1,css:f.kenBurns.from},{autoCSS:!1,css:f.kenBurns.to,ease:ee.Quad.easeInOut},-J.transitions.nextSlide.data.calculatedTimeShift),ie.isEmptyObject(f.filter.values.bgFrom)&&ie.isEmptyObject(f.filter.values.bgTo)||(f.filter.transitions.bg||(f.filter.transitions.bg=J.transitions.layers.filters.createTransition(f,"bg",f.filter.values.bgFrom,f.filter.values.bgTo)),w.to([{p:0},h],J.transitions.nextSlide.data.duration,{p:1,autoCSS:!1,ease:ee.Sine.easeInOut,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.bg]},0));else switch(t){case"in":if(f.in.enabled?(f.settings.timelineIsCalculated&&!f.settings.recalculateTimeline||("startAtFirst"==f.settings.recalculateTimeline&&(f.settings.recalculateTimeline=!1,f.settings.timelineIsCalculated=!1),"number"!=typeof f.in.startAt&&(f.in.startAt=0),"number"!=typeof f.in.startAtFirst&&(f.in.startAtFirst=0),f.timeline.transitioninstart=J.transitions.firstSlide&&f.in.startAtFirst?f.in.startAtFirst:f.in.startAt,f.timeline.transitioninend=f.timeline.transitioninstart+f.in.duration),J.transitions.firstSlide&&f.in.startAtFirst&&(f.settings.recalculateTimeline="startAtFirst"),J.resize.performTransformOperations(f.inLayerToCSS,T),f.in.mirror&&"prev"===J.navigation.direction&&J.resize.mirrorTransitionProperties({transitionProperties:T,transitionPropertiesShouldBeConverted:k,transitionType:"normal",mirrorProperties:f.in.mirror}),J.resize.transformProperties(m,f,T,k),J.resize.styleProperties(m,f,f.inLayerStyleFromCSS,f.inLayerStyleShouldBeConvertedFrom),J.resize.styleProperties(m,f,f.inLayerStyleToCSS,f.inLayerStyleShouldBeConvertedTo),T.transformPerspective=f.transformPerspective.layer*f.settings.calculatedratio,f.clip.enabled&&(f.clip.style.css.clipPath=J.functions.convert.clipProperties(f.clip.style.css.clipPath,f,m,!0),f.inClipFromCSS.clipPath||f.clip.style.css.clipPath===f.clip.default.css.clipPath||(f.inClipFromCSS.clipPath=f.clip.default.css.clipPath)),f.inClipFromCSS.clipPath&&(f.inClipFromCSS.clipPath=J.functions.convert.clipProperties(f.inClipFromCSS.clipPath,f,m),w.fromTo(y[0],f.in.duration,f.inClipFrom,f.inClipTo,f.timeline.transitioninstart)),ie.isEmptyObject(f.filter.values.in)?ie.isEmptyObject(f.filter.values.out)||m.css("filter",f.original.filter):(f.filter.transitions.in||(f.filter.transitions.in=J.transitions.layers.filters.createTransition(f,"in",f.filter.values.in,f.filter.values.style)),w.to([{p:0},h],f.in.duration,{p:1,autoCSS:!1,ease:f.inLayerTo.ease,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.in]},f.timeline.transitioninstart)),f.is.smartBG&&-1!==f.styleSettings.smartbg.indexOf("in")&&(f.inLayerStyleFrom.css["--x"]=parseInt(f.responsive["--x"])-T.x+"px",f.inLayerStyleTo.css["--x"]=parseInt(f.responsive["--x"])+"px",f.inLayerStyleFrom.css["--y"]=parseInt(f.responsive["--y"])-T.y+"px",f.inLayerStyleTo.css["--y"]=parseInt(f.responsive["--y"])+"px"),f.is.smartBG&&(J.slides[J.slides.current.index||1].data.hasSmartBG=!0),f.is.counter&&(w.call(function(e,t){e.text(J.counter.applyNumberFormat(t,parseFloat(t.start)))},[m,f.counterSettings],this,0),w.call(function(e,t){J.counter.start(e,t)},[m,f.counterSettings],this,f.timeline[f.counterSettings.startAt])),0===f.timeline.transitioninstart&&0===f.in.duration?(f.inLayerTo.css.transformOrigin=S.css.transformOrigin,f.inLayerTo.css.transformPerspective=S.css.transformPerspective,w.set(g[0],f.inLayerTo,f.timeline.transitioninstart),w.set(h,f.inLayerStyleTo,f.timeline.transitioninstart)):(w.fromTo(g[0],f.in.duration,S,f.inLayerTo,f.timeline.transitioninstart),w.fromTo(h,f.in.duration,f.inLayerStyleFrom,f.inLayerStyleTo,f.timeline.transitioninstart))):(f.timeline.transitioninstart=0,f.timeline.transitioninend=0),f.is.textLayer&&((f.textIn.type||f.textOut.type)&&J.transitions.layers.splitType.resetNodes(m,f),f.textIn.enabled&&(c=!(!f.textIn.mirror||"prev"!==J.navigation.direction),f.in.enabled||w.to(g[0],0,ie.extend(!0,{},f.inLayerTo,f.init.wrapper),f.timeline.textinstart),d=J.transitions.layers.splitType.setNodesSequence(f.textIn.type.split("_"),f.textIn.ns),f.textIn.nodes=c?d[1]:d[0],c&&J.resize.mirrorTransitionProperties({transitionProperties:I,transitionPropertiesShouldBeConverted:L,transitionType:"text",mirrorProperties:f.textIn.mirror}),J.resize.transformProperties(m,f,I,L,!1,f.textIn.nodes),I.transformPerspective=f.transformPerspective.text*f.settings.calculatedratio,ie.isEmptyObject(L.random)||J.transitions.layers.splitType.setRandomProperties(m,f,L.random,I,"In"),ie.isEmptyObject(I.random)||J.transitions.layers.splitType.setRandomProperties(m,f,I.random,I,"In"),delete I.random,f.settings.timelineIsCalculated||(f.timeline.textinstart=this.getTiming(f,f.textIn.startAt,"textinstart"),f.timeline.textinend=f.timeline.textinstart+(f.textIn.nodes.length-1)*f.textIn.shiftNodes+f.textIn.duration),I.color?f.textInNodesToCSS.color=f.original.color:f.textOutNodesTo.color&&(I.color=f.original.color),w.set(h,f.textIn.layerStyle,f.timeline.textinstart),window.getSelection&&J.browser.isSafari&&w.addCallback(function(e){try{window.getSelection().setBaseAndExtent(e,0,e,1e3)}catch(e){}window.getSelection().removeAllRanges()},f.timeline.textinstart+.001,[h]),w.addCallback(function(e){J.layers.set.dataAttribute("add",e,"text-animating-in")},f.timeline.textinstart,[m]),w.staggerFromTo(f.textIn.nodes,f.textIn.duration,I,f.textInNodesTo,f.textIn.shiftNodes,f.timeline.textinstart,function(e){J.layers.set.dataAttribute("remove",e,"text-animating-in"),J.layers.set.dataAttribute("add",e,"active"),J.transitions.layers.in.onComplete(e)},[m]))),f.is.keyframe&&J.o.playByScroll&&i.addPause(f.timeline.allinend(),function(){setTimeout(function(){delete J.timeouts.scroll,J.transitions.layers.timeline.timeScaleModifier=0,J.device.scroll.timeout=250},500)}),f.parallax.enabled&&"auto"==f.parallax.event){J.transitions.layers.parallax.auto(),f.parallax._timeline=new ee.TimelineMax({paused:!0});var M={repeat:-1==f.parallax.count?-1:f.parallax.count-1,ease:ee.Linear.easeNone};function B(e,t){return{x:e*J.resize.ratio,y:t*J.resize.ratio}}switch(f.parallax.dummy=B(0,-10),f.parallax.path){default:case"circle":M.bezier={curviness:1.5,values:[B(10,0),B(0,10),B(-10,0),B(0,-10)]};break;case"oval-h":M.bezier={type:"thru",curviness:1,values:[B(20,0),B(0,10),B(-20,0),B(0,-10)]};break;case"oval-v":f.parallax.dummy=B(0,-20),M.bezier={type:"thru",curviness:1,values:[B(10,0),B(0,20),B(-10,0),B(0,-20)]};break;case"infinity":f.parallax.dummy=B(0,0),M.bezier={curviness:1.3,values:[B(20,-10),B(30,0),B(20,10),B(0,0),B(-20,-10),B(-30,0),B(-20,10),B(0,0)]};break;case"horizontal":f.parallax.dummy=B(10,0),M.x=-10*J.resize.ratio,M.ease=ee.Quad.easeInOut,M.yoyo=!0,f.parallax.pathDuration/=2,M.repeat=-1==f.parallax.count?-1:2*f.parallax.count-1;break;case"vertical":f.parallax.dummy=B(0,-10),M.y=10*J.resize.ratio,M.ease=ee.Quad.easeInOut,M.yoyo=!0,f.parallax.pathDuration/=2,M.repeat=-1==f.parallax.count?-1:2*f.parallax.count-1;break;case"square":f.parallax.dummy=B(-10,-10),M.bezier={curviness:.5,values:[B(10,-10),B(10,10),B(-10,10),B(-10,-10)]};break;case"diamond":f.parallax.dummy=B(0,-10),M.bezier={curviness:.5,values:[B(10,0),B(0,10),B(-10,0),B(0,-10)]};break;case"triangle":f.parallax.dummy=B(0,10),M.bezier={curviness:.5,values:[B(-10,-10),B(10,-10),B(0,10)]};break;case"pentagon":f.parallax.dummy=B(0,10),M.bezier={curviness:.5,values:[B(-9.51,3.09),B(-5.88,-8.09),B(5.88,-8.09),B(9.51,3.09),B(0,10)]}}f.settings.timelineIsCalculated&&!f.is.static||(f.timeline.autoparallaxstart=this.getTiming(f,f.parallax.startAt,"autoparallaxstart"),f.timeline.autoparallaxend=-1!==f.parallax.count&&f.timeline.autoparallaxstart+f.parallax.pathDuration*(f.parallax.count*(M.yoyo?2:1))),M.onUpdate=function(e,t,i,a,s,r,o){ee.TweenMax.set(i,{x:-t.x*(a/50)*parseInt(s),y:-t.y*(a/50)*parseInt(s),"--rx":t.x*(a/50)*parseInt(s)+"px","--ry":t.y*(a/50)*parseInt(s)+"px",rotationX:"3d"==r?t.y/(100/o):0,rotationY:"3d"==r?-t.x/(100/o):0})};var _=!(M.onUpdateParams=["{self}",f.parallax.dummy,x[0],f.parallax.distance,f.parallax.level,f.parallax.type,f.parallax.rotation]);-1===M.repeat&&(_=!0),"backward"===f.parallax.direction&&_&&(M.repeat=0),f.parallax._timeline.to(f.parallax.dummy,f.parallax.pathDuration,M),"backward"===f.parallax.direction&&(f.parallax._timeline.seek(f.parallax._timeline.duration()).reverse(),_&&f.parallax._timeline.eventCallback("onReverseComplete",function(e){e.seek(e.duration()).resume()},[f.parallax._timeline])),_?i.addCallback(function(e){e.resume()},f.timeline.autoparallaxstart,[f.parallax._timeline]):(i.add(f.parallax._timeline,f.timeline.autoparallaxstart),f.parallax._timeline.resume()),J.transitions.layers.timeline.shouldRestart=!0}f.parallax.enabled&&"randomLocal"==f.parallax._event&&i.addCallback(function(e){J.transitions.layers.parallax.random.setTimer({layerData:e})},f.timeline[f.parallax.randomStartAt],[f]),f.loop.enabled&&(p=new ee.TimelineMax({repeat:f.loop.repeat,repeatDelay:f.loop.repeatDelay,yoyo:f.loop.yoyo,paused:!0}),f.settings.timelineIsCalculated&&!f.is.static||(f.timeline.loopstart=this.getTiming(f,f.loop.startAt,"loopstart"),f.timeline.loopend=-1!==f.loop.count&&f.timeline.loopstart+(f.loop.repeat+1)*f.loop.duration+f.loop.repeat*f.loop.repeatDelay),f.loop._timeline=p,J.resize.transformProperties(m,f,f.loopToCSS,{x:f.loopLayerShouldBeConverted.x,y:f.loopLayerShouldBeConverted.y}),(f.loopToCSS.x&&0!==f.loopToCSS.x||f.loopToCSS.y&&0!==f.loopToCSS.y)&&(J.transitions.layers.timeline.shouldRestart=!0),f.loopFromCSS.transformOrigin=J.functions.convert.transformOrigin(f.loopLayerShouldBeConverted.transformOrigin,m,f,m.data(J.defaults.init.dataKey).elements.$outerStyleWrapper),f.loopFromCSS.transformPerspective=f.transformPerspective.loop*f.settings.calculatedratio,ie.isEmptyObject(f.filter.values.loop)||(f.filter.transitions.loop||(f.filter.transitions.loop=J.transitions.layers.filters.createTransition(f,"loop",ie.isEmptyObject(f.filter.values.afterIn)?f.filter.values.style:f.filter.values.afterIn,f.filter.values.loop)),p.to([{p:0},h],f.loop.duration,{p:1,autoCSS:!1,ease:f.loopTo.ease,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.loop]},0)),p.fromTo(v[0],f.loop.duration,f.loopFrom,f.loopTo,0),f.is.smartBG&&-1!==f.styleSettings.smartbg.indexOf("loop")&&p.fromTo(h,f.loop.duration,{"--lx":"0px","--ly":"0px"},{ease:f.loopTo.ease,"--lx":-f.loopTo.css.x+"px","--ly":-f.loopTo.css.y+"px"},0),f.loopClipToCSS.clipPath&&(f.loopClipToCSS.clipPath=J.functions.convert.clipProperties(f.loopClipToCSS.clipPath,f,m),p.fromTo(v[0],f.loop.duration,f.loopClipFrom,f.loopClipTo,0)),-1!==f.loop.repeat&&("looplayers"===J.o.pauseOnHover||J.gui.timers.slidebar.$element||J.o.playByScroll)?(b.add(p,f.timeline.loopstart),p.play()):b.addCallback(function(e){e.play()},f.timeline.loopstart,[p])),f.is.static&&(f.timeline.staticfrom=f.timeline.transitioninend,f.timeline.staticto="100%",f.settings.timelineIsCalculated||(l=Math.max(f.timeline.allinandloopend(),0),this.totalDuration=Math.max(this.totalDuration,l))),f.is.textLayer&&f.textOut.enabled&&(c=J.transitions.layers.splitType.setNodesSequence(f.textOut.type.split("_"),f.textOut.ns),f.textOut.nodes=c[0],J.resize.transformProperties(m,f,f.textOutNodesTo,f.textOutShouldBeConverted,f.textOutNodesFrom,f.textOut.nodes),f.textOut.mirror?(f.textOut.nodesMirror=c[1],J.resize.mirrorTransitionProperties({transitionProperties:$,transitionPropertiesShouldBeConverted:W,transitionType:"text",mirrorProperties:f.textOut.mirror}),J.resize.transformProperties(m,f,$,W,f.textOutNodesFrom,f.textOut.nodes)):f.textOut.nodesMirror=c[0],f.textOutNodesFrom.transformPerspective=f.transformPerspective.text*f.settings.calculatedratio,ie.isEmptyObject(f.textOutShouldBeConverted.random)||J.transitions.layers.splitType.setRandomProperties(m,f,f.textOutShouldBeConverted.random,f.textOutNodesTo,"Out"),ie.isEmptyObject(f.textOutNodesTo.random)||J.transitions.layers.splitType.setRandomProperties(m,f,f.textOutNodesTo.random,f.textOutNodesTo,"Out"),delete f.textOutNodesTo.random,f.textOut.mirror&&(ie.isEmptyObject(W.random)||J.transitions.layers.splitType.setRandomProperties(m,f,W.random,$,"Out"),ie.isEmptyObject($.random)||J.transitions.layers.splitType.setRandomProperties(m,f,$.random,$,"Out"),delete $.random),f.settings.timelineIsCalculated||(f.timeline.textoutstart=this.getTiming(f,f.textOut.startAt,"textoutstart"),f.timeline.textoutend=f.timeline.textoutstart+(f.textOut.nodes.length-1)*f.textOut.shiftNodes+f.textOut.duration),f.clip.enabled&&!f.loopClipToCSS.clipPath&&f.clip.style.css.clipPath==f.clip.default.css.clipPath&&i.fromTo(y[0],0,f.clip.style,f.clip.none,f.timeline.textoutstart),f.textOutNodesTo.color&&(f.textOutNodesFrom.color=f.original.color),_=function(e){J.layers.set.dataAttribute("add",e,"text-animating-out")},p=function(e){J.layers.set.dataAttribute("remove",e,"text-animating-out")},u=[m],-1!==f.textOut.startAt.indexOf("slidechangeonly")&&(!f.is.static||f.is.static&&f.settings.slideOut===J.slides.next.index)?(a.set(h,f.textOut.layerStyle,0),r.set(h,f.textOut.layerStyle,0),(c=new ee.TimelineMax).addCallback(_,0,u),c.staggerFromTo(f.textOut.nodes,f.textOut.duration,f.textOutNodesFrom,f.textOutNodesTo,f.textOut.shiftNodes,0,p,u),c.timeScale(c.duration()/J.o.forceLayersOutDuration),a.add(c,0),a.to(g[0],0,f.reset.wrapperOnTimelineEnd,J.o.forceLayersOutDuration),(c=new ee.TimelineMax).addCallback(_,0,u),c.staggerFromTo(f.textOut.nodesMirror,f.textOut.duration,f.textOutNodesFrom,$,f.textOut.shiftNodes,0,p,u),c.timeScale(c.duration()/J.o.forceLayersOutDuration),r.add(c,0),r.to(g[0],0,f.reset.wrapperOnTimelineEnd,J.o.forceLayersOutDuration)):(i.addCallback(_,f.timeline.textoutstart,u),i.set(h,f.textOut.layerStyle,f.timeline.textoutstart),i.staggerFromTo(f.textOut.nodes,f.textOut.duration,f.textOutNodesFrom,f.textOutNodesTo,f.textOut.shiftNodes,f.timeline.textoutstart,p,u),(!f.is.static||f.is.static&&f.settings.slideOut===J.slides.next.index)&&(a.to(g[0],J.o.forceLayersOutDuration,{opacity:0},0),r.to(g[0],J.o.forceLayersOutDuration,{opacity:0},0),a.to(g[0],0,f.reset.wrapperOnTimelineEnd,J.o.forceLayersOutDuration),r.to(g[0],0,f.reset.wrapperOnTimelineEnd,J.o.forceLayersOutDuration))));break;case"out":f.out.enabled&&(J.resize.performTransformOperations(f.inLayerToCSS,f.outLayerToCSS),J.resize.transformProperties(m,f,f.outLayerToCSS,f.outLayerShouldBeConverted,f.outLayerFromCSS),J.resize.styleProperties(m,f,f.outLayerStyleFromCSS,f.outLayerStyleShouldBeConvertedFrom),J.resize.styleProperties(m,f,f.outLayerStyleToCSS,f.outLayerStyleShouldBeConvertedTo),f.outLayerFromCSS.transformPerspective=f.transformPerspective.layer*f.settings.calculatedratio,f.out.mirror&&(J.resize.performTransformOperations(f.inLayerToCSS,P),J.resize.mirrorTransitionProperties({transitionProperties:P,transitionPropertiesShouldBeConverted:O,transitionType:"normal",mirrorProperties:f.out.mirror}),J.resize.transformProperties(m,f,P,O,f.outLayerFromCSS)),f.is.smartBG&&-1!==f.styleSettings.smartbg.indexOf("out")&&(f.outLayerStyleFrom.css["--x"]=parseInt(f.responsive["--x"])+"px",f.outLayerStyleTo.css["--x"]=parseInt(f.responsive["--x"])-f.outLayerToCSS.x+"px",f.outLayerStyleFrom.css["--y"]=parseInt(f.responsive["--y"])+"px",f.outLayerStyleTo.css["--y"]=parseInt(f.responsive["--y"])-f.outLayerToCSS.y+"px"),ie.isEmptyObject(f.filter.values.out)||f.filter.transitions.out||(f.filter.transitions.out=J.transitions.layers.filters.createTransition(f,"out",ie.isEmptyObject(f.filter.values.afterLoop)?ie.isEmptyObject(f.filter.values.afterIn)?f.filter.values.style:f.filter.values.afterIn:f.filter.values.afterLoop,f.filter.values.out)),f.clip.enabled&&!f.outClipToCSS.clipPath&&f.clip.style.css.clipPath!==f.clip.default.css.clipPath&&(f.outClipToCSS.clipPath=f.clip.default.css.clipPath),f.outClipToCSS.clipPath&&(f.outClipToCSS.clipPath=J.functions.convert.clipProperties(f.outClipToCSS.clipPath,f,m)),"slidechangeonly"!==f.out.startAt?(f.settings.timelineIsCalculated&&!f.is.static||(f.is.static?(f.timeline.staticfrom=0,f.timeline.transitionoutstart=this.getTiming(f,f.out.startAt,"transitionoutstart",!0),f.timeline.staticto=f.timeline.transitionoutstart):f.timeline.transitionoutstart=Math.max(this.getTiming(f,f.out.startAt,"transitionoutstart"),f.timeline.transitioninend),f.timeline.transitionoutend=f.timeline.transitionoutstart+f.out.duration),f.outClipToCSS.clipPath&&i.to(y[0],f.out.duration,f.outClipTo,f.timeline.transitionoutstart),ie.isEmptyObject(f.filter.values.out)||i.to([{p:0},h],f.out.duration,{p:1,autoCSS:!1,ease:f.outLayerTo.ease,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.out]},f.timeline.transitionoutstart),f.outLayerStyleTo.onComplete=function(e,t){t.is.mediaLayer&&t.mediaProperties.$media[0].hasAttribute("data-ls-playing")&&J.media.functions.stopSingleMedia(e,t)},f.outLayerStyleTo.onCompleteParams=[m,f],i.fromTo(g[0],f.out.duration,f.outLayerFrom,f.outLayerTo,f.timeline.transitionoutstart),i.fromTo(h,f.out.duration,f.outLayerStyleFrom,f.outLayerStyleTo,f.timeline.transitionoutstart),i.fromTo(g[0],0,f.init.wrapper,f.reset.wrapperOnTimelineEnd,f.timeline.transitionoutend)):(f.timeline.staticfrom=0,f.timeline.staticto="100%"),u=Math.min(J.o.forceLayersOutDuration,f.out.duration),(!f.is.static||f.is.static&&f.settings.slideOut===J.slides.next.index)&&(a.fromTo(g[0],u,f.outLayerFrom,f.outLayerTo,0),a.fromTo(h,u,f.outLayerStyleFrom,f.outLayerStyleTo,0),r.fromTo(g[0],u,f.outLayerFrom,f.out.mirror?C:f.outLayerTo,0),r.fromTo(h,u,f.outLayerStyleFrom,f.outLayerStyleTo,0),f.outClipToCSS.clipPath&&(a.to(y[0],u,f.outClipTo,0),r.to(y[0],u,f.outClipTo,0)),ie.isEmptyObject(f.filter.values.out)||(a.to([{p:0},h],u,{p:1,autoCSS:!1,ease:f.outLayerTo.ease,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.out]},0),r.to([{p:0},h],u,{p:1,autoCSS:!1,ease:f.outLayerTo.ease,onUpdate:J.transitions.layers.filters.animate,onUpdateParams:["{self}",f.filter.transitions.out]},0)))),l=Math.max(f.timeline.alloutandloopend(),0),this.totalDuration=Math.max(this.totalDuration,l),f.settings.timelineIsCalculated=!0}}},play:function(){J.transitions._slideTimeline&&(J.transitions._slideTimeline.play(),J.functions.setStates(this,{started:!0,running:!0,stopped:!1,paused:!1}))},pause:function(e){e=ie.isNumeric(e)?e:.75;J.transitions._slideTimeline&&(ee.TweenMax.to(J.transitions._slideTimeline,e,{timeScale:0,onComplete:function(e){e.pause()},onCompleteParams:[J.transitions._slideTimeline]}),J.functions.setStates(this,{paused:!0,stopped:!1}))},resume:function(){J.transitions._slideTimeline&&(this.state.paused&&J.transitions._slideTimeline.play(),ee.TweenMax.to(J.transitions._slideTimeline,.75,{timeScale:1}),J.functions.setStates(this,{paused:!1,stopped:!1}))},reverse:function(){J.transitions._slideTimeline&&J.transitions._slideTimeline.reverse()},scrollForward:function(e){e||(this.play(),this.modifyTimeScale()),J.transitions._slideTimeline&&(J.slider.isBusy()||0!==J.transitions._slideTimeline.totalDuration()&&1!==J.transitions._slideTimeline.progress()||"down"!==J.device.scroll.direction||(J.slideshow.direction="next",(e=J.slideshow.sequence.normalized).indexOf(J.slides.current.index)===e.length-1?(J.slider.positionToViewport="under",J.device.scroll.enable(),J.slideshow.direction="prev"):J.navigation.next()))},scrollBackwards:function(e,t){e&&!t||(this.reverse(),this.modifyTimeScale()),J.transitions._slideTimeline&&(J.slider.isBusy()||0!==J.transitions._slideTimeline.totalDuration()&&0!==J.transitions._slideTimeline.progress()||"up"!==J.device.scroll.direction||(J.slideshow.direction="prev",0===J.slideshow.sequence.normalized.indexOf(J.slides.current.index)?(J.slider.positionToViewport="over",J.device.scroll.enable(),J.slideshow.direction="next"):J.navigation.prev()))},modifyTimeScale:function(){J.transitions._slideTimeline&&ee.TweenMax.to(J.transitions._slideTimeline,.25,{timeScale:1+this.timeScaleModifier})},resetStates:function(){this.state={started:!1,running:!1,paused:!1,stopped:!1,finished:!1}}},hover:{enable:function(e){e.attr("data-ls-canhover","1")},disable:function(e){e.attr("data-ls-canhover","0")},set:function(e,t){t.elements.$wrapper.on("mouseenter."+W,function(){J.transitions.layers.hover.mouseEnter(e,t)}),t.elements.$wrapper.on("mouseleave."+W,function(){J.transitions.layers.hover.mouseLeave(e,t)}),t.elements.$wrapper.on("mousemove."+W,function(){J.transitions.layers.hover.mouseMove(e,t)})},createTimeline:function(e,t){var i,a,s;t.hover._timeline=new ee.TimelineMax({paused:!0,onReverseComplete:function(e,t){t.hover._timeline._reversed&&(t.hover._timeline.stop().clear(),delete t.hover._timeline)},onReverseCompleteParams:[e,t]}),J.resize.transformProperties(e,t,t.hoverToCSS,t.hoverShouldBeConverted,t.hoverFromCSS),J.resize.styleProperties(e,t,t.hoverToCSS,t.hoverShouldBeConverted),t.hoverFromCSS.transformPerspective=t.transformPerspective.hover*t.settings.calculatedratio,t.hover._tween=ee.TweenMax.fromTo(e[0],t.hover.durationIn,t.hoverFrom,t.hoverTo),t.hover._timeline.add(t.hover._tween,0),e.next().is(".ls-layer-link")?(i=e.next(),a=ie.extend(!0,{},t.hoverFrom,{css:{opacity:1,color:"transparent",background:"transparent",z:0}}),s=ie.extend(!0,{},t.hoverTo,{css:{opacity:1,color:"transparent",background:"transparent",z:0}}),t.hover._linkTween=ee.TweenMax.fromTo(i[0],t.hover.durationIn,a,s),t.hover._timeline.add(t.hover._linkTween,0)):t.hover._linkTween=null,t.hover.alwaysOnTop&&(s={zIndex:9999},J.browser.isSafari&&(s.transform="translateZ(999999px)"),t.hover._timeline.to(t.elements.$outerWrapper[0],t.hover.durationIn,{autoCSS:!1,css:s},0)),t.hover.reverseTimeScale=t.hover.durationIn/t.hover.durationOut==1?1:t.hover.durationIn/t.hover.durationOut,this.hoverIn(e,t)},mouseEnter:function(e,t){"1"===e.attr("data-ls-canhover")&&(e.attr("data-ls-hovered",1),t.elements.$wrapper.off("mousemove."+W),t.hover._timeline?(t.hover._timeline.play().stop().progress(0),this.hoverIn(e,t)):this.createTimeline(e,t))},mouseLeave:function(e,t){t.hover._timeline&&(t.hover._timeline.stop().progress(1),this.hoverOut(e,t)),e.removeAttr("data-ls-hovered")},mouseMove:function(e,t){e.attr("data-ls-hovered")||this.mouseEnter(e,t)},hoverIn:function(e,t){t.hover._tween.updateTo({ease:t.hover.easeIn}),t.hover._linkTween&&t.hover._linkTween.updateTo({ease:t.hover.easeIn}),t.hover._timeline.play().timeScale(1)},hoverOut:function(e,t){t.hover._tween.updateTo({ease:t.hover.easeOut}),t.hover._linkTween&&t.hover._linkTween.updateTo({ease:t.hover.easeOut}),t.hover._timeline.reverse().timeScale(t.hover.reverseTimeScale)}},parallax:{defaultProperties:{type:"2d",event:"cursor",duration:5,x:!0,y:!0,rotation:10,distance:10,durationMove:1.5,durationLeave:1.2,transformOrigin:"slidercenter slidermiddle 0",transformPerspective:500},defaults:{scrollModifier:5,centerLayers:"center",centerDegree:40,sensitive:10},state:{enabled:!1,ready:!1},wrappers:{cursor:{$2d:ie(),$3d:ie()},scroll:{$2d:ie(),$3d:ie()},auto:{$2d:ie(),$3d:ie()},randomGlobal:{},randomLocal:{}},globalSlideParallaxData:{},init:function(){var t=this;$.on("mouseenter."+W,function(){(t.wrappers.cursor.$2d.length||t.wrappers.cursor.$3d.length)&&t.calculateTransformProperties()}),$.on("mousemove."+W,function(e){(t.wrappers.cursor.$2d.length||t.wrappers.cursor.$3d.length)&&t.mouseMove(e)}),$.on("mouseleave."+W,function(){(t.wrappers.cursor.$2d.length||t.wrappers.cursor.$3d.length)&&t.reset()}),M.on("scroll.parallax"+W+" touchmove.parallax"+W,function(){(t.wrappers.scroll.$2d.length||t.wrappers.scroll.$3d.length)&&(t.state.paused||t.scroll())}),M.on("resize.parallax"+W,function(){(t.wrappers.auto.$2d.length||t.wrappers.auto.$3d.length||t.wrappers.scroll.$2d.length||t.wrappers.scroll.$3d.length||t.wrappers.cursor.$2d.length||t.wrappers.cursor.$3d.length)&&t.calculateTransformProperties()}),t.defaults.scrollModifier*=J.o.parallaxScrollReverse?-1:1},addLayer:function(e,t,i,a){switch(this.state.enabled||(J.functions.setStates(this,{enabled:!0}),this.init()),i.styleSettings.smartbg&&-1!==i.styleSettings.smartbg.indexOf("parallax")&&(t.smartBG=!0),ie.extend(!0,t,this.defaultProperties,J.slides[a].parallax,i.parallax),i.transformPerspective.parallax?t.transformPerspective=i.transformPerspective.parallax:i.transformPerspective.parallax=t.transformPerspective,t.event&&t.event.match(/(cursor|scroll|auto|random)/)||(t.event="cursor"),"auto"===t.event&&(t.path||(t.path=J.slides[a].parallax.path),t.pathDuration||(t.pathDuration=J.slides[a].parallax.pathDuration),t.startAt||(t.startAt=J.slides[a].parallax.startAt),t.direction||(t.direction=J.slides[a].parallax.direction),t.count||(t.count=J.slides[a].parallax.count)),"random"===t.event&&(null==t.randomX&&(t.randomX=J.slides[a].parallax.randomX),null==t.randomY&&(t.randomY=J.slides[a].parallax.randomY),t.randomDuration||(t.randomDuration=J.slides[a].parallax.randomDuration),t.ease||(t.ease=J.slides[a].parallax.ease),t.ease=J.functions.convert.easing(t.ease),t.randomCount||(t.randomCount=J.slides[a].parallax.randomCount),t.randomStartAt||(t.randomStartAt=J.slides[a].parallax.randomStartAt),null==t.randomWait&&(t.randomWait=J.slides[a].parallax.randomWait),t.type!==J.slides[a].parallax.type||t.randomX!==J.slides[a].parallax.randomX||t.randomY!==J.slides[a].parallax.randomY||t.randomDuration!==J.slides[a].parallax.randomDuration||t.randomCount!==J.slides[a].parallax.randomCount||t.randomStartAt!==J.slides[a].parallax.randomStartAt||t.randomWait!==J.slides[a].parallax.randomWait||t.distance!==J.slides[a].parallax.distance||t.rotation!==J.slides[a].parallax.rotation||i.settings.skipViewport?t._event="randomLocal":t._event="randomGlobal"),t.type&&t.type.match(/^(2d|3d)$/)||(t.type="2d"),(i.parallax=t).axis){case"none":t.x=!1,t.y=!1;break;case"x":t.y=!1;break;case"y":t.x=!1}t.smartBG&&ee.TweenMax.set(e[0],{"--rx":"0px","--ry":"0px"}),"random"===t.event?(this.wrappers[t._event][a]||(this.wrappers[t._event][a]={$2d:ie(),$3d:ie()}),-1!==t._event.indexOf("randomLocal")&&(i.parallax.randomUID=J.functions.getUID(12)),this.wrappers[t._event][a]["$"+t.type]=this.wrappers[t._event][a]["$"+t.type].add(e)):this.wrappers[t.event]["$"+t.type]=this.wrappers[t.event]["$"+t.type].add(e)},random:{setTimer:function(e={}){e.layerData?(e.$wrappers=e.$wrappers||e.layerData.elements.$parallaxWrapper,e.parallaxData=e.parallaxData||e.layerData.parallax,e.timeout=e.timeout||"rp-local-"+e.parallaxData.randomUID,e.eventName=e.eventName||"randomLocal"):(e.$wrappers=J.transitions.layers.parallax.wrappers.randomGlobal[e.slideIndex].$2d.add(J.transitions.layers.parallax.wrappers.randomGlobal[e.slideIndex].$3d),e.parallaxData=J.transitions.layers.parallax.globalSlideParallaxData[e.slideIndex]=e.parallaxData||e.globalSlideParallaxData||J.transitions.layers.parallax.globalSlideParallaxData[e.slideIndex],e.timeout=e.timeout||"rp-global-"+e.slideIndex,e.eventName=e.eventName||"randomGlobal"),e.$layer=e.$layer||e.$wrappers.first().find(".ls-layer, .ls-l").first(),e.layerData=e.layerData||e.$layer.data(J.defaults.init.dataKey),e.x=J.functions.convert.randomProperties(e.parallaxData.randomX,"x",e.$layer,e.layerData),e.y=J.functions.convert.randomProperties(e.parallaxData.randomY,"y",e.$layer,e.layerData),e.duration=e.parallaxData.randomDuration,ie.isNumeric(e.duration)||(e.duration=J.functions.convert.randomProperties(e.duration,"duration")/1e3),e.wait=e.parallaxData.randomWait,ie.isNumeric(e.wait)||(e.wait=J.functions.convert.randomProperties(e.wait,"delay")/1e3),J.transitions.layers.parallax.mouseMove(!1,{x:e.x,y:e.y,eventName:e.eventName,slideIndex:e.slideIndex||null,$wrappers:e.$wrappers,timeout:e.timeout,duration:e.duration,type:e.parallaxData.type}),J.timeouts[e.timeout]=setTimeout(function(){J.transitions.layers.parallax.random.setTimer(e)},1e3*(e.duration+e.wait))}},addShadow:function(){var e,t,i,a=J.gui.shadow.$element,s=(J.slides.current&&J.slides.current.parallax?J.slides.current:J.slides.next).index;J.slides[s].data.$background&&J.slides[s].data.$background.data(J.defaults.init.dataKey).parallax.enabled&&J.slides[s].data.overflow&&"hidden"!==J.slides[s].data.overflow&&(e="50% -"+.25*J.slider.height+"px 0",i=void 0!==(t=J.slides[s].data.$background.data(J.defaults.init.dataKey).parallax).rotation?2*t.rotation:void 0!==J.slides[s].parallax.rotation?2*J.slides[s].parallax.rotation:2*this.defaultProperties.rotation,a.data(J.defaults.init.dataKey,{parallax:ie.extend(!0,{},this.defaultProperties,J.slides[s].parallax,{level:t.level,transformOrigin:e,rotation:i})}),a.attr("data-ls-parallax","active"),ee.TweenMax.set(a[0],{transformOrigin:e,transformPerspective:a.data(J.defaults.init.dataKey).parallax.transformPerspective*J.resize.ratio}),"3d"===J.slides[s].parallax.type||"3d"===t.type?this.wrappers.cursor.$3d=this.wrappers.cursor.$3d.add(a):this.wrappers.cursor.$2d=this.wrappers.cursor.$2d.add(a)),this.shadowIsChecked=!0},removeShadow:function(){var e=J.gui.shadow.$element;this.wrappers.cursor.$2d=this.wrappers.cursor.$2d.not(e),this.wrappers.cursor.$3d=this.wrappers.cursor.$3d.not(e),e.attr("data-ls-parallax","disabled"),this.shadowIsChecked=!1},calculateTransformProperties:function(){var e,a;(e=this.wrappers,a=ie(),function e(t){for(var i in t)t.hasOwnProperty(i)&&(t[i]&&t[i].jquery?a=a.add(t[i]):"object"==typeof t[i]&&null!==t[i]&&e(t[i]))}(e),a).each(function(){var e=ie(this),t=e.data(J.defaults.init.dataKey).parallax,i=e.find(".ls-layer"),a=i.data(J.defaults.init.dataKey),e=J.functions.convert.transformOrigin(t.transformOrigin,i.data(J.defaults.init.dataKey).elements.$wrapper,a,e);ee.TweenMax.set(ie(this)[0],{transformOrigin:e,transformPerspective:t.transformPerspective*a.settings.calculatedratio})}),this.transformPropertiesCalculated=!0},trigger:function(){M.trigger("scroll.parallax"+W),M.trigger("touchmove.parallax"+W)},auto:function(){this.transformPropertiesCalculated||this.calculateTransformProperties()},scroll:function(){var e=(("top"===this.defaults.centerLayers?te.scroll.top:te.scroll.top+(te.viewport.height-J.slider.height)/2)-J.slider.offset.top)*J.resize.ratio*this.defaults.scrollModifier;J.slider.state.inFullscreen&&(e=0),this.transformPropertiesCalculated||this.calculateTransformProperties();e={x:0,y:e,eventName:"scroll"};this.animate2D(e),this.animate3D(e)},mouseMove:function(e,t={}){this.transformPropertiesCalculated||this.calculateTransformProperties(),J.slider.state.animatingSlides||this.shadowIsChecked||!J.gui.shadow.$element||this.addShadow();var i=J.slider.offset.left+J.slider.width/2,a=J.slider.offset.top+J.slider.height/2,a={x:null!=t.x?t.x:e.pageX-i,y:null!=t.y?t.y:e.pageY-a,eventName:t.eventName||"cursor",slideIndex:t.slideIndex||null,$wrappers:t.$wrappers||!1,timeout:t.timeout||!1,duration:t.duration||null};t.type&&"2d"!==t.type||this.animate2D(a),t.type&&"3d"!==t.type||this.animate3D(a)},animate2D:function(s={}){var r,o,n=-1!==s.eventName.indexOf("random"),e=this.wrappers[s.eventName].$2d;"randomGlobal"==s.eventName?e=this.wrappers[s.eventName][s.slideIndex].$2d:"randomLocal"==s.eventName&&(e=s.$wrappers),e.each(function(){var e,t,i,a=ie(this);"active"===a.attr("data-ls-parallax")&&(e=a.data(J.defaults.init.dataKey).parallax,t=n||e.x?-s.x*(e.distance/2e3)*parseInt(e.level):0,i=n||e.y?-s.y*(e.distance/2e3)*parseInt(e.level):0,s.timeout&&(e._timeout=s.timeout),o=n?(r=s.duration,e.ease):(r=e.durationMove,ee.Power1.easeOut),ee.TweenMax.to(a[0],r,{x:t,y:i,ease:o}),e.smartBG&&J.transitions.layers.parallax.animateSmartBG(t,i,a,r,o))})},animate3D:function(o={}){var n,l,d=-1!==o.eventName.indexOf("random"),e=this.wrappers[o.eventName].$3d;"randomGlobal"==o.eventName?e=this.wrappers[o.eventName][o.slideIndex].$3d:"randomLocal"==o.eventName&&(e=o.$wrappers),e.each(function(){var e,t,i,a,s,r=ie(this);"active"===r.attr("data-ls-parallax")&&(e=r.data(J.defaults.init.dataKey).parallax,o.timeout&&(e._timeout=o.timeout),a=d||e.x?(i=-o.x/(4e3/e.rotation),-o.x*(e.distance/2e3)*parseInt(e.level)):i=0,s=d||e.y?(t=o.y/(4e3/e.rotation),-o.y*(e.distance/2e3)*parseInt(e.level)):t=0,l=d?(n=o.duration,e.ease):(n=e.durationMove,ee.Power1.easeOut),ee.TweenMax.to(r[0],n,{rotationX:t,rotationY:i,x:a,y:s,ease:l}),e.smartBG&&J.transitions.layers.parallax.animateSmartBG(a,s,r,n,l))})},animateSmartBG:function(e,t,i,a,s){ee.TweenMax.to(i.find(".ls-layer, .ls-l").first()[0],a,{"--rx":-e+"px","--ry":-t+"px",ease:s})},reset:function(){ie().add(this.wrappers.cursor.$2d).add(this.wrappers.cursor.$3d).each(function(){var e=ie(this);e.find(".ls-layer[data-ls-hidden]").length&&ee.TweenMax.set(e[0],{x:0,y:0,rotationX:0,rotationY:0}),e.find(".ls-layer[data-ls-parallax-reset]").length&&ee.TweenMax.to(e[0],ie(this).data(J.defaults.init.dataKey).parallax.durationLeave,{x:0,y:0,rotationX:0,rotationY:0})}),J.gui.shadow.$element&&this.removeShadow(),this.transformPropertiesCalculated=!1}},scroll:{defaultProperties:{shouldBeConverted:{transformOrigin:"50% 50% 0"},transformPerspective:500,duration:.5,ease:"easeOutQuart"},defaults:{centerLayers:"center"},state:{enabled:!1},$wrappers:ie(),init:function(){var e=this;M.on("scroll.scroll"+W+" touchmove.scroll"+W,function(){e.$wrappers.length&&e.scroll()}),M.on("resize.scroll"+W,function(){e.$wrappers.length&&e.calculateTransformProperties()})},addLayer:function(e,t,i,a){this.state.enabled||(J.functions.setStates(this,{enabled:!0}),this.init()),ie.extend(!0,t,this.defaultProperties,i.scroll),i.transformPerspective.scroll?t.transformPerspective=i.transformPerspective.scroll:i.transformPerspective.scroll=t.transformPerspective,i.settings.skipViewport&&(t.skipViewport=!0),t.shouldBeConverted.transformOriginAlt||(t.shouldBeConverted.transformOriginAlt=t.shouldBeConverted.transformOrigin),t.ease=J.functions.convert.easing(t.ease),t.easeRev=J.functions.convert.easing(t.easeRev||t.ease),t.durationRev=t.durationRev||t.duration,i.scroll=t,this.$wrappers=this.$wrappers.add(e)},getCenter:function(e,t){var i="project";switch(((J.slider.isScrollScene||J.slider.isSticky)&&"scene"===t||"document"===t)&&(i=t),e){case"top":switch(i){case"scene":e=J.slider.wrapperPosition.top-(te.viewport.height-J.slider.height)/2;break;case"document":default:e=te.scroll.top}break;case"center":switch(i){case"scene":e=J.slider.wrapperPosition.middleForScrollTransition;break;case"document":e=te.scroll.top;break;default:e=te.scroll.top+(te.viewport.height-J.slider.height)/2}break;case"bottom":switch(i){case"scene":e=J.slider.wrapperPosition.top+J.slider.wrapperOffset.height-te.viewport.height+(te.viewport.height-J.slider.height)/2;break;case"document":e=te.scroll.top;break;default:e=te.scroll.top+te.viewport.height-J.slider.height}break;case"top-meets-viewport-bottom":e=te.scroll.top+te.viewport.height;break;case"top-meets-viewport-middle":e=te.scroll.top+te.viewport.height/2;break;case"bottom-meets-viewport-top":e=te.scroll.top-J.slider.height;break;case"bottom-meets-viewport-middle":e=te.scroll.top+te.viewport.height/2-J.slider.height}return e},setCenter:function(e,t){return"project"!==e.getPosition||e.center&&this.defaults.centerLayers!==e.center?this.getCenter(e.center||this.defaults.centerLayers,e.getPosition):t},setY:function(e,t){return(J.slider.isScrollScene||J.slider.isSticky)&&"scene"===e.getPosition?-t:"document"===e.getPosition?t:J.slider.state.inFullscreen?0:t-J.slider.offset.top},scroll:function(o){var n,l;J.slider.state.isNotDisplayed||J.slider.state.isHidden||(l=(n=this).getCenter(this.defaults.centerLayers),this.transformPropertiesCalculated||this.calculateTransformProperties(),this.$wrappers.each(function(){var e,t,i,a=ie(this),s=a.find(".ls-scroll-transform"),r=a.data(J.defaults.init.dataKey).scroll;(-1!==J.slider.position.toViewportYForSkipViewportLayers.indexOf("inside")&&r.skipViewport||!n.state.paused)&&(i=n.setCenter(r,l),t={overwrite:"all",c:e=n.setY(r,i),ease:"down"===te.scroll.direction?r.ease:r.easeRev,onUpdate:function(e,t){e=e.target.style.c<=0?"+":"-";r.layerPositionToCenter!==e&&n.setTransformProperties(ie(t),r,e),r.layerPositionToCenter=e},onUpdateParams:["{self}",this,r]},i={overwrite:"all",ease:"down"===te.scroll.direction?r.ease:r.easeRev},r.x&&(i.x=n.calculateTransformations(-e*r.x/20,"x",r,-e)),r.y&&(i.y=n.calculateTransformations(-e*r.y/20,"y",r,-e)),r.rotation&&(i.rotation=n.calculateTransformations(e*r.rotation/40,"rotation",r,-e)),r.rotationX&&(i.rotationX=n.calculateTransformations(e*r.rotationX/40,"rotationX",r,-e)),r.rotationY&&(i.rotationY=n.calculateTransformations(e*r.rotationY/40,"rotationY",r,-e)),r.skewX&&(i.skewX=n.calculateTransformations(-e*r.skewX/40,"skewX",r,-e)),r.skewY&&(i.skewY=n.calculateTransformations(-e*r.skewY/40,"skewY",r,-e)),r.scaleX&&(i.scaleX=n.calculateTransformations(1-e*r.scaleX/-4e3,"scaleX",r,e)),r.scaleY&&(i.scaleY=n.calculateTransformations(1-e*r.scaleY/-4e3,"scaleY",r,e)),r.opacity&&(i.opacity=n.calculateTransformations(1-e*(r.opacityyoyo?Math.abs(r.opacity):r.opacity)/1e3,"opacity",r,e)),e="down"===te.scroll.direction?r.duration:r.durationRev,o||0==e?(ee.TweenMax.set(a[0],t),ee.TweenMax.set(s[0],i)):(ee.TweenMax.to(a[0],e,t),ee.TweenMax.to(s[0],e,i)))}))},calculateTransformations:function(e,t,i,a){if(i[t+"yoyo"])switch(t){case"scaleX":case"scaleY":a<0&&(e=1-(e-1));break;case"opacity":a<1&&(e=1-(e-1));break;default:a<1&&(e=-e)}return"opacity"==t&&i.opacityinvert&&(e=1-e),void 0!==i[t+"min"]&&e<i[t+"min"]&&(e=i[t+"min"]),void 0!==i[t+"max"]&&e>i[t+"max"]&&(e=i[t+"max"]),("x"===t&&i.xresponsive||"y"===t&&i.yresponsive)&&(e*=J.resize.ratio),e},calculateTransformProperties:function(){var a=this;this.$wrappers.each(function(){var e=ie(this),t=e.data(J.defaults.init.dataKey).scroll,i=(e.find(".ls-layer").data(J.defaults.init.dataKey),a.setCenter(t,a.getCenter(a.defaults.centerLayers))),i=a.setY(t,i)<=0?"+":"-";a.setTransformProperties(e,t,i)}),this.transformPropertiesCalculated=!0},setTransformProperties:function(e,t,i){var a=e.find(".ls-layer").data(J.defaults.init.dataKey);ee.TweenMax.set(e.find(".ls-scroll-transform")[0],{transformOrigin:J.functions.convert.transformOrigin("+"===i?t.shouldBeConverted.transformOrigin:t.shouldBeConverted.transformOriginAlt,e,a,a.elements.$outerStyleWrapper),transformPerspective:t.transformPerspective*a.settings.calculatedratio})},trigger:function(){this.scroll(!0)},reset:function(){this.transformPropertiesCalculated=!1}},filters:{createTransition:function(e,t,i,a){var s,r=new J.defaults.layer.properties.filter,o={};for(s in r)switch(t){case"in":o[s]=[r[s],r[s]],o[s][0]=(i.hasOwnProperty(s)?i:a.hasOwnProperty(s)?a:r)[s],o[s][1]=(a.hasOwnProperty(s)?a:r)[s],e.filter.values.afterIn[s]=o[s][1];break;case"hover":case"loop":case"out":o[s]=[],o[s][0]=(i.hasOwnProperty(s)?i:r)[s],o[s][1]=(a.hasOwnProperty(s)?a:i.hasOwnProperty(s)&&i[s]!==r[s]?i:r)[s],"loop"===t&&!0!==e.loop.yoyo&&-1!==e.loop.count&&(e.filter.values.afterLoop[s]=o[s][1]);break;case"bg":o[s]=[r[s],r[s]],i.hasOwnProperty(s)&&(o[s][0]=i[s]),a.hasOwnProperty(s)&&(o[s][1]=a[s])}return o},convert:function(e){for(var t,i,a={},s=/(blur|brightness|contrast|grayscale|hue-rotate|invert|saturate|sepia)/i,r=0,o=(e=e.split(" ")).length;r<o;r++)(t=(i=e[r].split("("))[0]).match(s)&&(i=parseInt(i[1]),a[t]=i);return a},animate:function(e,t){var i=100*e.target[0].p;if("object"==typeof t){var a,s="";for(a in t)if("object"==typeof t[a]&&2===t[a].length)switch(a){case"blur":s+=" blur( "+(t[a][0]<t[a][1]?t[a][0]+Math.abs(t[a][0]-t[a][1])/100*i:t[a][0]-Math.abs(t[a][0]-t[a][1])/100*i)+"px )";break;case"hue-rotate":s+=" hue-rotate( "+(t[a][0]<t[a][1]?t[a][0]+Math.abs(t[a][0]-t[a][1])/100*i:t[a][0]-Math.abs(t[a][0]-t[a][1])/100*i)+"deg )";break;default:s+=" "+a+"( "+(t[a][0]<t[a][1]?t[a][0]+Math.abs(t[a][0]-t[a][1])/100*i:t[a][0]-Math.abs(t[a][0]-t[a][1])/100*i)+"% )"}ee.TweenMax.set(e.target,{"-webkit-filter":s,filter:s})}}},splitType:{setNodesSequence:function(e,o){function t(e,t){if("desc"==t)e=o.slice(0).reverse();else if("rand"==t)e=o.slice(0).sort(function(){return.5-Math.random()});else if("center"==t){var i,a=Math.floor(o.length/2);for(e=[o[a]],i=1;i<=a;i++)e.push(o[a-i],o[a+i]);e.length=o.length}else if("edge"==t){var s,r=Math.floor(o.length/2);for(e=[o[0]],s=1;s<=r;s++)e.push(o[o.length-s],o[s]);e.length=o.length}return e||o}var i=t(o,e[1]),e={asc:"desc",desc:"asc"}[e[1]]||e[1];return[i,t(o,e)]},resetNodes:function(e,t){ie(".ls-ch, .ls-wd, .ls-ln",e).add(t.elements.$wrapper).css({transform:"none",opacity:1}).each(function(){delete this._gsTransform})},setRandomProperties:function(e,t,i,a,s){for(var r in i){for(var o=[],n=0,l=t["text"+s].nodes.length;n<l;n++)o[n]=J.functions.convert.randomProperties(i[r],r,e,t);delete a[r],a.cycle[r]=o}i=null}}},media:{defaults:{delay:500,fadeIn:500,fadeOut:750},changeBackgroundVideo:function(e,t){var i,a,s,r;J.slides.current.index&&J.slides.current.data.$backgroundVideo.length&&(r=(i=J.slides.current.data.$backgroundVideo).data(J.defaults.init.dataKey).elements.$bgWrapper,t&&(i.data(J.defaults.init.dataKey).mediaProperties.willBePaused=!0,r.fadeOut(J.transitions.media.defaults.fadeOut,function(){i.trigger("stopBackgroundVideo"),i.data(J.defaults.init.dataKey).mediaProperties.willBePaused=!1}))),J.slides.next.data.$backgroundVideo.length&&(s=(a=J.slides.next.data.$backgroundVideo).data(J.defaults.init.dataKey).elements.$bgWrapper,r=a.data(J.defaults.init.dataKey).elements.$bgOuterWrapper,te.isMobile&&($.hasClass("ls-device-is-phone")&&r.hasClass("ls-hide-on-phone")||$.hasClass("ls-device-is-tablet")&&r.hasClass("ls-hide-on-tablet"))||setTimeout(function(){a.trigger("playBackgroundVideo")},e?50:0),e||t?s.fadeIn(J.transitions.media.defaults.fadeOut):s.css({display:"block"}),a.data(J.defaults.init.dataKey).mediaProperties.isPreloaded=!0)}},timers:{defaults:{fadeInDuration:.35,reverseDuration:.3},create:function(e){this.curNext=e||"next",this.reset(),J.gui.timers.bar.$element&&this.bar.createTransition(),J.gui.timers.circle.$element&&this.circle.createTransition(),J.gui.timers.slidebar.$element&&this.slidebar.createTransition()},reverse:function(){var e;J.slides.current&&J.slides.current.data&&J.transitions._slideTimeline&&(e=J.transitions._slideTimeline.progress(),e=J.slides.current.data.duration*e/this.defaults.reverseDuration,J.gui.timers.bar.$element&&this.bar._transition&&(J.transitions._slideTimeline.remove(J.transitions.timers.bar._transition),this.bar._transition.reverse().timeScale(e)),J.gui.timers.circle.$element&&this.circle._transition&&(J.transitions._slideTimeline.remove(J.transitions.timers.circle._transition),this.circle._transition.reverse().timeScale(e)),J.gui.timers.slidebar.$element&&this.slidebar._transition&&(J.transitions._slideTimeline.remove(J.transitions.timers.slidebar._transition),this.slidebar._transition.reverse().timeScale(e)))},reset:function(){J.gui.timers.bar.$element&&this.bar._transition&&this.bar.reset(),J.gui.timers.circle.$element&&this.circle._transition&&this.circle.reset(),J.gui.timers.slidebar.$element&&this.slidebar._transition&&this.slidebar.reset()},bar:{reset:function(){this._transition&&(this._transition.kill(),this._transition=!1)},createTransition:function(){this._transition=ee.TweenMax.fromTo(J.gui.timers.bar.$element[0],J.slides[J.transitions.curNext].data.duration,{autoCSS:!1,paused:!0,css:{width:0}},{autoCSS:!1,css:{},ease:ee.Linear.easeNone,onReverseComplete:function(){J.transitions.timers.bar._transition=!1},onReverseCompleteParams:["{self}"],onComplete:function(e){e.target.style.width="100%",e.target.style.width="calc( 100% - "+J.slider.initial.skinWidth+"px )"},onCompleteParams:["{self}"],onUpdate:function(e){e.target.style.width=Math.min(J.slider.width,J.slider.width*e.progress())+"px"},onUpdateParams:["{self}"]})}},circle:{reset:function(){this._transition&&(J.gui.timers.circle.$element.stop(!0,!0),this._transition.kill(),this._transition=!1)},createTransition:function(){var e=J.gui.timers.circle.$element.find(".ls-ct-right .ls-ct-rotate")[0],t=J.gui.timers.circle.$element.find(".ls-ct-left .ls-ct-rotate")[0],i=J.slides[J.transitions.curNext].data.duration;this._transition=new ee.TimelineMax({paused:!0}).fromTo(J.gui.timers.circle.$element[0],J.transitions.timers.defaults.fadeInDuration,{autoCSS:!1,immediateRender:!0,css:{opacity:0,display:"block"}},{autoCSS:!1,css:{opacity:J.gui.timers.circle.$element.data("original").opacity}}).fromTo(e,i/2,{autoCSS:!1,css:{rotation:0}},{autoCSS:!1,css:{rotation:180},ease:ee.Linear.easeNone},0).fromTo(t,i/2,{autoCSS:!1,css:{rotation:0}},{autoCSS:!1,css:{rotation:180},ease:ee.Linear.easeNone},i/2)}},slidebar:{reset:function(){this._transition&&(this._transition.kill(),this._transition=!1)},createTransition:function(){var i=this;i._transition=new ee.TimelineMax({paused:!0,onReverseComplete:function(){J.transitions.timers.slidebar._transition=!1}}),ie.each(J.gui.timers.slidebar.$sliderContainerElement,function(t,e){i._transition.add(ee.TweenMax.fromTo(J.gui.timers.slidebar.$sliderContainerElement[t][0],J.slides[J.transitions.curNext].data.duration,{autoCSS:!1,css:{left:0}},{autoCSS:!1,immediateRender:!1,css:{},ease:ee.Linear.easeNone,onComplete:function(e){e.target.style.left="calc( 100% - "+J.gui.timers.slidebar.sliderContainerElementWidth[t]+"px )"},onCompleteParams:["{self}"],onUpdate:function(e){e.target.style.left=(J.gui.timers.slidebar.containerElementWidth[t]-J.gui.timers.slidebar.sliderContainerElementWidth[t])*e.progress()+"px"},onUpdateParams:["{self}"]}),0),i._transition.add(ee.TweenMax.fromTo(J.gui.timers.slidebar.$progressBarElement[t][0],J.slides[J.transitions.curNext].data.duration,{autoCSS:!1,css:{width:0}},{autoCSS:!1,css:{},ease:ee.Linear.easeNone,onComplete:function(e){e.target.style.width="100%"},onCompleteParams:["{self}"],onUpdate:function(e){e.target.style.width=J.gui.timers.slidebar.elementWidth[t]*e.progress()+"px"},onUpdateParams:["{self}"]}),0)})}}},scrollscene:{animate:function(e){var t,i;J.transitions._slideTimeline&&(t=J.slider.wrapperOffset.height-J.slider.height,i=te.scroll.top+this.stickLimit-J.slider.wrapperOffset.top,e=Math.max(1e-4,J.o.sceneDuration/t*i),t=ee.Quart.easeOut,i=this.initialized&&!this.immediateRender?J.o.smoothScrollDuration/1e3:0,J.transitions._slideTimeline.tweenTo(e,{ease:t}).duration(i),this.initialized=!0,this.immediateRender=!1)}}},J.plugins={load:function(){var e,a;J.o.plugins&&0!==J.o.plugins.length?(e=J.o.plugins[0],a="object"==typeof e?e.namespace:e,window._layerSlider.plugins[a]?(J.plugins.init(a,e,!0),J.plugins.load()):J.browser.usesFileProtocol||"object"!=typeof e?(J.browser.usesFileProtocol?window.console&&(console.error(J.defaults.slider.errorText,"Cannot load plugins on file:// protocol."),console.info("Please include the plugin files manually.")):window.console&&(console.error(J.defaults.slider.errorText,"Plugin files are missing!"),console.info('Plugin "'+a+'" has been added in slider init options, but the source files are not found on page.')),J.o.plugins.splice(0,1),J.plugins.load()):-1===window._layerSlider.pluginsBeingLoaded.indexOf(a)?-1===window._layerSlider.pluginsLoaded.indexOf(a)&&-1===window._layerSlider.pluginsNotLoaded.indexOf(a)?(window._layerSlider.pluginsBeingLoaded.push(a),ie.ajax({url:-1===e.js.indexOf(LS_nsProtocol||"http://")&&-1===e.js.indexOf("https://")?(window._layerSlider.pluginsPath||window._layerSlider.scriptPath+"/../plugins/")+e.js:e.js,dataType:"script",success:function(){J.plugins.init(e.namespace,e,!0),window._layerSlider.pluginsLoaded.push(a)},error:function(e,t,i){window.console&&(console.error(J.defaults.slider.errorText,a,"plugin has not been loaded!"),console.error("Additional error info:",i)),window._layerSlider.pluginsNotLoaded.push(a)},complete:function(){window._layerSlider.pluginsBeingLoaded.splice(window._layerSlider.pluginsBeingLoaded.indexOf(a),1),J.plugins.load()}})):(J[a]||-1!==window._layerSlider.pluginsNotLoaded.indexOf(a)?J.o.plugins.splice(0,1):J.plugins.init(a,e),J.plugins.load()):J.plugins.checkLoaded(a)):J.slider.check.initialized()},init:function(e,t,i){J.initializedPlugins[e]=new window._layerSlider.plugins[e](J,$,W,t.settings),window._layerSlider.checkVersions(J.initializedPlugins[e].pluginData.requiredLSVersion,J.plugin.version)?(t.css&&i&&ie('<link rel="stylesheet" href="'+(-1===t.css.indexOf(LS_nsProtocol||"http://")&&-1===t.css.indexOf("https://")?(window._layerSlider.pluginsPath||window._layerSlider.scriptPath+"/../plugins/")+t.css:t.css)+'">').appendTo("head"),J.initializedPlugins[e].init&&J.initializedPlugins[e].init()):window.console&&console.error(J.defaults.slider.errorText,e,"plugin has not been loaded! Required LayerSlider version:",J.initializedPlugins[e].pluginData.requiredLSVersion,"(you have:",J.plugin.version+")"),J.o.plugins.splice(0,1)},checkLoaded:function(e){J.intervals.pluginLoaded=setInterval(function(){-1===window._layerSlider.pluginsLoaded.indexOf(e)&&-1===window._layerSlider.pluginsNotLoaded.indexOf(e)||-1!==window._layerSlider.pluginsBeingLoaded.indexOf(e)||(clearInterval(J.intervals.pluginLoaded),delete J.intervals.pluginLoaded,J.plugins.load())},100)}},J.performance={},J.slider={shouldResize:!0,thumbnails:[],state:{isHidden:!1,isPaused:!1,preloadingImages:!1,readyForStart:!1,changingSlides:!1,animatingSlides:!1},offset:{},position:{},isBusy:function(){return this.state.preloadingImages||this.state.changingSlides||this.state.animatingSlides},load:function(){if(!document.body.contains(L))return!1;J.api.hasEvent("sliderWillLoad")&&J.api.triggerEvent("sliderWillLoad"),J.slider.set.global()},set:{global:function(){var e;J.originalMarkup=$[0].outerHTML,J.userInitOptions=J.functions.convert.properties(J.functions.convert.oldProperties(i)),J.meta={},J.o=ie.extend(!0,{},J.defaults.init.options,J.userInitOptions),J.o.forceLayersOutDuration/=1e3,J.o.forceLayersOutDuration=0<J.o.forceLayersOutDuration?J.o.forceLayersOutDuration:.75,J.o.sliderFadeInDuration/=1e3,window.console&&!0!==J.o.hideWelcomeMessage&&!0!==window._layerSlider.hideWelcomeMessage&&(window._layerSlider.hideWelcomeMessage=!0,t=window.console.info?"info":"log",e=window.LS_Meta&&window.LS_Meta.v?" | WP Plugin: "+window.LS_Meta.v:"",console[t]("LayerSlider initialized | core: "+J.plugin.version+"-"+J.plugin.release+e),console[t]("Find updates and docs @ https://layerslider.com/"));var t={namespace:"debug",js:"debug/layerslider.debug.js",css:"debug/layerslider.debug.css"};-1!==document.location.hash.indexOf("debug")&&window.console&&("object"==typeof J.o.plugins?J.o.plugins.push(t):J.o.plugins=[t]),(window._layerSlider.currentScript||window._layerSlider.lsScript)&&(window._layerSlider.scriptPath=(window._layerSlider.currentScript||window._layerSlider.lsScript).src.replace(/\\/g,"/").replace(/\/[^\/]*$/,"")),J.o.silentMode&&(J.slider.$silentWrapper=ie("<ls-silent-wrapper></ls-silent-wrapper>"),J.slider.$silentWrapper.append($).prependTo("body")),"string"==typeof J.o.getData&&(J.o.getData=[J.o.getData]),"object"==typeof J.o.plugins?J.plugins.load():J.slider.check.initialized()},styles:function(){var e,t,i,a,s,r,o,n,l,d,p,c,u,h,m,f,g,y=J.slider,v=$.parent(),x=L.style,w=window.getComputedStyle(L,null),b=parseInt(L.clientWidth),S=parseInt(L.clientHeight),T=parseInt(v.width()),k=parseInt(v.height()),C=J.o.layersContainerWidth,P=J.o.layersContainerHeight,O=J.o.type.toLowerCase();switch(J.debugMode&&J.debug.add("group","sliderInit.style"),J.o.width?e=-1==J.o.width.indexOf("%")?parseInt(J.o.width):J.o.width:x.width?e=-1==x.width.indexOf("%")?parseInt(x.width):x.width:0<C?(e=C,J.debugMode&&J.debug.add("warn","sliderInit.noWidth",C)):(e=b,J.debugMode&&J.debug.add("warn","sliderInit.noWidth2",b)),i=e,J.o.height?t=-1==J.o.height.indexOf("%")?parseInt(J.o.height):J.o.height:x.height?t=-1==x.height.indexOf("%")?parseInt(x.height):x.height:0<P?(t=P,J.debugMode&&J.debug.add("warn","sliderInit.noHeight",P)):(t=S,J.debugMode&&J.debug.add("warn","sliderInit.noHeight2",k)),a=t,s=""!==x.maxWidth?-1!==x.maxWidth.indexOf("px")?parseInt(x.maxWidth):x.maxWidth:0,void 0===J.userInitOptions.type&&(0<C&&0<P||"100%"===e&&"100%"===t?O="fullsize":C<=0&&P<=0&&(J.o.responsiveUnder<=0||0<J.o.responsiveUnder&&J.o.sliderVersion)?O=void 0!==J.o.responsive&&!1===J.o.responsive?"fixedsize":"responsive":0<J.o.responsiveUnder&&(O="fullwidth")),O){case"fullwidth":-1!==e.indexOf("%")&&(J.debugMode&&J.debug.add("warn","sliderInit.percWidth",[O,e,b]),e=b),C<=0&&(C=e,J.debugMode&&J.debug.add("warn","sliderInit.conWidth",[O,e])),J.o.responsiveUnder<=0&&(J.o.responsiveUnder=C,J.debugMode&&J.debug.add("warn","sliderInit.fullwidth",C)),-1!==t.indexOf("%")&&(o=k/(100/parseInt(t)),J.debugMode&&J.debug.add("warn","sliderInit.fullwidth2",[O,t,o]),t=o),P<=0&&(P=t);break;case"fullsize":-1!==e.indexOf("%")&&(r=0<C?C:T,J.debugMode&&J.debug.add("warn","sliderInit.fullsize",[O,e,r,T,C]),e=r),C<=0&&(C=e,J.debugMode&&J.debug.add("warn","sliderInit.conWidth",[O,e])),-1!==t.indexOf("%")&&(o=0<P?P:M.height()/(100/parseInt(t)),J.debugMode&&J.debug.add("warn","sliderInit.fullsize2",[O,t,o,M.height(),P]),t=o),P<=0&&(P=t,J.debugMode&&J.debug.add("warn","sliderInit.conHeight",[O,t]));break;case"fixedsize":break;default:J.userInitOptions.type=J.o.type=O="responsive",(J.o.responsiveUnder=-1)!==e.indexOf("%")&&(e=b,J.debugMode&&J.debug.add("warn","sliderInit.percWidth",[O,e,b])),-1!==t.indexOf("%")&&(e=S,J.debugMode&&J.debug.add("warn","sliderInit.responsive",[O,t,S])),J.debugMode&&0<C&&J.debug.add("warn","sliderInit.conWidth2",[O,C]),J.debugMode&&0<P&&J.debug.add("warn","sliderInit.conHeight2",[O,P])}if($.addClass("ls-container ls-"+O),$.parent().addClass("ls-direction-fix"),J.userInitOptions.slideBGSize||"responsive"!==O||!J.userInitOptions.hasOwnProperty("sliderVersion")||J.userInitOptions.sliderVersion||(J.o.slideBGSize="auto",J.debugMode&&J.debug.add("warn","sliderInit.bgCover",O)),J.o.slideBGSize=J.o.slideBGSize.replace("100% 100%","stretch"),n=0<C?C:e,l=0<P?P:t,(d="auto"===(g=L.style.marginLeft)?"auto":""===g?parseInt(w.getPropertyValue("margin-left")):parseInt(L.style.marginLeft))===(p="auto"===(f=L.style.marginRight)?"auto":""===f?parseInt(w.getPropertyValue("margin-right")):parseInt(L.style.marginRight))&&(""===g&&""===f&&(I=d,p=d="auto"),$.css({marginLeft:"auto",marginRight:"auto"})),c=""!==x.paddingLeft?parseInt(x.paddingLeft):parseInt($.css("padding-left")),h=""!==x.paddingRight?parseInt(x.paddingRight):parseInt($.css("padding-right")),u=""!==x.paddingTop?parseInt(x.paddingTop):parseInt($.css("padding-top")),m=""!==x.paddingBottom?parseInt(x.paddingBottom):parseInt($.css("padding-bottom")),v=""!==x.borderLeftWidth?parseInt(x.borderLeftWidth):parseInt($.css("border-left-width")),g=""!==x.borderRightWidth?parseInt(x.borderRightWidth):parseInt($.css("border-right-width")),f=""!==x.borderTopWidth?parseInt(x.borderTopWidth):parseInt($.css("border-top-width")),x=""!==x.borderBottomWidth?parseInt(x.borderBottomWidth):parseInt($.css("border-bottom-width")),y.initial={type:O,width:e,height:t,originalWidth:i,originalHeight:a,percW:e/100,percH:t/100,layersWidth:C,layersHeight:P,ratio:n/l,maxWidth:s,marginLeft:d,marginRight:p,marginTop:J.o.marginTop,marginBottom:J.o.marginBottom,paddingLeft:c,paddingTop:u,paddingRight:h,paddingBottom:m,borderLeftWidth:v,borderTopWidth:f,borderRightWidth:g,borderBottomWidth:x,skinWidth:c+h+v+g,skinHeight:u+m+f+x},J.debugMode&&(J.debug.add("log","sliderInit.style",[e,t,i,a,C,P,parseInt(n/l*100)/100,0<s?s:void 0,[d,p]]),I&&J.debug.add("warn","sliderInit.margin",I)),ie("#ls-global").length||(ie("html").attr("id")?ie("body").attr("id")||ie("body").attr("id","ls-global"):ie("html").attr("id","ls-global")),"static"!==w.getPropertyValue("position")&&"absolute"!==w.getPropertyValue("position")&&(L.style.position="relative"),J.o.insertSelector&&$[J.o.insertMethod](J.o.insertSelector),J.slider.$hiddenWrapper=ie('<div class="ls-wp-container fitvidsignore ls-hidden" data-layerslider-uid="'+W+'"></div>').addClass($.attr("class")).prependTo("body"),J.slider.$innerWrapper=ie('<div class="ls-inner"></div>'),J.slider.$slideBGColorWrapper=ie('<div class="ls-slide-bgcolor"></div>').appendTo(J.slider.$innerWrapper),J.slider.$layersWrapper=ie('<div class="ls-layers"></div>').appendTo(J.slider.$innerWrapper),J.slider.$bgVideosWrapper=ie('<div class="ls-background-videos"></div>').appendTo(J.slider.$layersWrapper),J.slider.$slideBGWrapper=ie('<div class="ls-slide-backgrounds"></div>').appendTo(J.slider.$layersWrapper),J.slider.$innerWrapper.appendTo($),!0===J.o.hideOnMobile&&te.isMobile?($.addClass("ls-forcehide"),$.closest(".ls-wp-fullwidth-container").addClass("ls-forcehide"),J.o.autoStart=!1):J.slider.check.showHide(),"sticky"===J.o.scene||"scroll"===J.o.scene){if(J.o.scene)switch(J.slider.isScene=!0,J.o.scene){case"scroll":J.slider.isScrollScene=!0;case"sticky":J.slider.isSticky=!0}$.parent().is("ls-scene-wrapper")?$.parent().attr("data-scene",J.o.scene).attr("data-layerslider-uid",W):$.wrap('<ls-scene-wrapper data-scene="'+J.o.scene+'" data-layerslider-uid="'+W+'"></ls-scene-wrapper>'),J.slider.$spacingWrapper=ie('ls-scene-wrapper[data-layerslider-uid="'+W+'"]'),$.attr("data-scene",J.o.scene)}else J.slider.$spacingWrapper=$;var I=J.slider.isScene&&"scene"===J.o.globalBGFor?J.slider.$spacingWrapper:J.slider.$innerWrapper,w=!1;-1==J.o.globalBGColor.indexOf("gradient")?I.css({backgroundColor:J.o.globalBGColor}):(w=J.o.globalBGColor,J.o.globalBGColor="transparent"),J.o.globalBGImage?I.css({backgroundImage:"url( "+J.o.globalBGImage+" )"+(w?", "+w:""),backgroundRepeat:J.o.globalBGRepeat,backgroundAttachment:J.o.globalBGAttachment,backgroundSize:J.o.globalBGSize,backgroundPosition:J.o.globalBGPosition}):w&&I.css({backgroundImage:w}),"transparent"!=J.o.globalBGColor||!1!==J.o.globalBGImage||w||I.css({background:"none transparent"}),(J.o.preventSliderClip&&J.o.fitScreenWidth&&("fullwidth"===O||"fullsize"===O&&"fitheight"!==J.o.fullSizeMode)||J.slider.isScrollScene||J.slider.isSticky)&&$.parents(":not(body, html)").each(function(){ie(this).addClass("ls-overflow-visible")})},options:function(){var t,i,a,s,r,e;ie("html").find('meta[content*="WordPress"]').length&&(J.meta.wpVersion=ie("html").find('meta[content*="WordPress"]').attr("content").split("WordPress")[1]),window.LS_Meta&&window.LS_Meta.v?J.meta.lswpVersion=window.LS_Meta.v:ie("html").find('script[src*="layerslider"]').length&&-1!=ie("html").find('script[src*="layerslider"]').attr("src").indexOf("?")&&(J.meta.lswpVersion=ie("html").find('script[src*="layerslider"]').attr("src").split("?")[1].split("=")[1]),"undefined"!=typeof layerSliderTransitions&&(J.t=ie.extend({},layerSliderTransitions)),"undefined"!=typeof layerSliderCustomTransitions&&(J.ct=ie.extend({},layerSliderCustomTransitions)),J.debugMode&&("undefined"!=typeof layerCustomSliderTransitions?(J.debug.add("log","sliderInit.customTransitions",!1),"undefined"==typeof layerSliderTransitions&&J.debug.add("warn","sliderInit.slideTransitions")):"undefined"==typeof layerSliderTransitions&&J.debug.add("warn","sliderInit.noSlideTransitions")),"number"==typeof J.o.parallaxCenterDegree&&(J.transitions.layers.parallax.defaults.centerDegree=J.o.parallaxCenterDegree),"number"==typeof J.o.parallaxSensitivity&&(J.transitions.layers.parallax.defaults.sensitive=J.o.parallaxSensitivity),J.o.parallaxCenterLayers&&(J.transitions.layers.parallax.defaults.centerLayers=J.o.parallaxCenterLayers),J.o.scrollCenterLayers&&(J.transitions.layers.scroll.defaults.centerLayers=J.o.scrollCenterLayers),J.slider.isSticky&&(ie.extend(J.o,{allowFullscreen:!1,playByScroll:!1}),J.slider.isScrollScene&&ie.extend(J.o,{autoPauseSlideshow:!1,autoStart:!1,pauseLayers:!1,pauseOnHover:!1,startInViewport:!1})),J.o.playByScroll&&ie.extend(J.o,{cycles:-1,startInViewport:!0,pauseOnHover:!1,autoStart:!1}),te.isMobile&&(J.o.pauseOnHover=!1),J.o.noContextMenu&&$.on("contextmenu."+W,function(){return!1}),J.o.startInViewport&&(J.slider.state.waitForGettingInViewport=!0,J.o.playByScroll&&(J.slider.positionToViewport=te.scroll.top>J.slider.offset.top-(te.viewport.height-J.slider.height)/2?"under":"over",t=!0,i=4*J.o.playByScrollSpeed,J.device.scroll.timeout=250,J.transitions.layers.timeline.timeScaleModifier=0,ie(document).on("wheel."+W+" touchmove."+W,function(e){te.isMobile?(a=e.originalEvent.touches[0].clientY,s<a?J.device.scroll.direction="up":a<s&&(J.device.scroll.direction="down"),r=s-a,s=a):(0<e.originalEvent.deltaY?J.device.scroll.direction="down":J.device.scroll.direction="up",r=e.originalEvent.deltaY),0!==Math.abs(r)&&(J.device.scroll.lastDirection?J.device.scroll.lastDirection!==J.device.scroll.direction&&(J.device.scroll.lastDirection=J.device.scroll.direction,J.transitions.layers.timeline.timeScaleModifier=0):J.device.scroll.lastDirection=J.device.scroll.direction,"inside"===J.slider.positionToViewport&&(J.resize.viewport(),0<=r?J.transitions.layers.timeline.scrollForward():J.transitions.layers.timeline.scrollBackwards(),t&&(clearTimeout(J.timeouts.scroll),t=!1,J.transitions.layers.timeline.timeScaleModifier=J.transitions.layers.timeline.timeScaleModifier<i?J.transitions.layers.timeline.timeScaleModifier+.25:i,J.timeouts.scroll2=setTimeout(function(){delete J.timeouts.scroll2,t=!0,J.device.scroll.timeout=50<J.device.scroll.timeout?J.device.scroll.timeout-50:50},J.device.scroll.timeout))),J.slider.check.positionToViewport(),J.timeouts.checkPosition=setTimeout(function(){J.slider.check.positionToViewport()},25))}))),J.slider.canShow=!0,te.automaticFontSizeRatio||(e=ie('<ls-dummy style="font-size: 100px; width: 0; height: 0; opacity: 0; position: absolute; overflow: hidden; pointer-events: none;">Lorem</ls-dummy>').appendTo("body"),te.automaticFontSizeRatio=parseFloat(e.css("font-size"))/parseFloat(e[0].style.fontSize)||1,e.remove())},pictureElements:function(){for(var e=$.find("picture"),t=$.find("source"),i=0,a=t.length;i<a;i++){var s=ie(t[i]);!s.attr("src")&&s.attr("data-src")&&s.attr("src",s.attr("data-src")),!s.attr("srcset")&&s.attr("data-srcset")&&s.attr("srcset",s.attr("data-srcset"))}for(var r=0,o=e.length;r<o;r++){var n,l,d=ie(e[r]),p=d.find("img");d.is(".ls-bg, .ls-tn")||p.is(".ls-bg, .ls-tn")?(n=p[0],l=d.attr("class"),!p.attr("src")&&p.attr("data-src")&&p.attr("src","data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=="),!p.attr("srcset")&&p.attr("data-srcset")&&p.attr("srcset",p.attr("data-srcset")),p.attr("data-src",n.currentSrc||p.attr("data-src")||p.attr("src")),p.removeAttr("src srcset").addClass(l).prependTo(p.closest(".ls-slide")),d.remove()):d.find("img").appendTo(d).removeAttr("width height data-ls")}},events:function(){M.on("scroll."+W,function(){J.slider.set.offset()}),M.on("touchmove."+W,function(e){e=e.touches||e.originalEvent.touches;1==e.length&&(self.touchX=e[0].clientX)}),M.on("resize."+W,function(){J.slider.check.showHide(),J.transitions.scrollscene.immediateRender=!0,"inside"===J.slider.positionToViewport&&J.o.playByScroll&&J.resize.viewport(),J.slider.shouldResize&&(!te.isMobile||te.isMobile&&te.viewport.width!==te.viewport.lastWidth||J.slider.isPopup)&&J.resize.all(),J.slider.set.offset(),J.slider.isScene&&J.resize.scene(),J.slider.isPopup||(J.timeouts.resize&&clearTimeout(J.timeouts.resize),J.resize.once?J.resize.once=!1:J.timeouts.resize=setTimeout(function(){J.resize.once=!0,M.trigger("resize."+W),J.api.methods("resetScroll")},100))}),J.debugMode&&(M.off(".debug"+W),M.on("resize.debug"+W,function(){J.debug.add("log","resize.window",te.viewport.width,!0)})),M.on("hashchange."+W,function(){document.location.hash&&J.slides.deeplink(document.location.hash)}),J.slider.set.offset(),M.trigger("resize."+W),J.o.refreshWaypoint&&window.Waypoint&&Waypoint.refreshAll()},offset:function(){if("none"!==$.css("display")){var e=L.getBoundingClientRect(),t=J.slider.position.toViewportForPerformance;J.slider.position.toViewport;if(J.slider.get.offset(e),J.slider.get.position(e),0<J.slider.position.left-te.viewport.width?J.slider.position.left-te.viewport.width>J.performance.threshold?J.slider.position.toViewportX="after":J.slider.position.toViewportX="insideLimit":J.slider.position.right<0?J.slider.position.right<-J.performance.threshold?J.slider.position.toViewportX="before":J.slider.position.toViewportX="insideLimit":J.slider.position.toViewportX="inside",0<J.slider.position.top-te.viewport.height?J.slider.position.top-te.viewport.height>J.performance.threshold?J.slider.position.toViewportY="below":J.slider.position.toViewportY="insideLimit":J.slider.position.bottom<0?J.slider.position.bottom<-J.performance.threshold?J.slider.position.toViewportY="above":J.slider.position.toViewportY="insideLimit":J.slider.position.toViewportY="inside",J.slider.position.toViewportYForSkipViewportLayers=J.slider.position.toViewportY,J.slider.state.waitForGettingInViewport&&(J.slider.position.middle<te.viewport.height&&0<J.slider.position.middle?((J.slider.position.toViewportY="inside")===J.slider.position.toViewportX&&(J.slider.position.toViewport="inside"),J.slider.state.waitForGettingInViewport=!1,J.transitions._slideTimeline&&J.transitions._slideTimeline.play(),J.o.animateFirstSlide&&J.slideshow.firstStart&&J.slider.state.readyForStart&&(J.resize.layers({callback:J.api.resumeCallback}),J.api.resumeCallback=!1),J.debugMode&&J.debug.add("log","slideshow.inviewport",!1)):J.slider.position.toViewportY="outside"),-1!==J.slider.position.toViewportX.indexOf("inside")&&"insideLimit"==J.slider.position.toViewportY||-1!==J.slider.position.toViewportY.indexOf("inside")&&"insideLimit"==J.slider.position.toViewportX?(J.slider.position.toViewport="insideLimit",J.slider.position.toViewportForPerformance="inside"):"inside"==J.slider.position.toViewportX&&"inside"==J.slider.position.toViewportY?(J.slider.position.toViewport="inside",J.slider.position.toViewportForPerformance="inside"):(J.slider.position.toViewport="outside",J.slider.position.toViewportForPerformance="outside"),J.o.performanceMode&&t!==J.slider.position.toViewportForPerformance&&("inside"===J.slider.position.toViewportForPerformance?(J.performance.sliderIsInviewport=!0,J.api.methods("resumeSlider","performanceMode")):(J.performance.sliderIsInviewport=!1,J.api.methods("pauseSlider","performanceMode"))),(J.slider.isScrollScene||J.slider.isSticky)&&!J.slider.$spacingWrapper.is("[data-disabled-scene]")){var t=J.slider.$spacingWrapper[0].getBoundingClientRect(),i=(te.viewport.height-J.slider.height)/2,a=0;switch(J.o.stickTo||"center"){case"top":break;case"center":a=i;break;case"bottom":a=2*i}J.transitions.scrollscene.stickLimit=a,J.slider.wrapperPosition={left:t.left,top:t.top,middleForScrollTransition:t.top+t.height/2-te.viewport.height/2},J.slider.wrapperOffset={left:t.left+te.scroll.left,right:t.left+J.slider.width+te.scroll.left,top:t.top+te.scroll.top,bottom:t.bottom+J.slider.height+te.scroll.top,width:t.width,height:t.height},J.slider.state.shouldAnimateScrollSnene=!1,t.top>a?("disabled"!==J.slider.state.sticky||J.slider.isScrollScene&&!J.transitions.scrollscene.initialized)&&(J.slider.state.sticky="disabled",J.slider.state.shouldAnimateScrollSnene=!0):t.bottom-J.slider.height-a<0?("over"!==J.slider.state.sticky||J.slider.isScrollScene&&!J.transitions.scrollscene.initialized)&&(J.slider.state.sticky="over",J.slider.state.shouldAnimateScrollSnene=!0):t.top<=a&&t.bottom>-a&&("enabled"!==J.slider.state.sticky&&(J.slider.state.sticky="enabled"),J.slider.state.shouldAnimateScrollSnene=!0),J.slider.isScrollScene&&J.transitions._slideTimeline&&J.slider.state.shouldAnimateScrollSnene&&J.transitions.scrollscene.animate()}J.slider.state.isNotDisplayed&&(J.slider.state.isNotDisplayed=!1,J.resize.all(),J.slider.check.positionToViewport(),M.trigger("scroll"))}else J.slider.state.isNotDisplayed=!0},attributes:function(){$.attr("data-current-slide",J.slides.current.index)}},get:{offset:function(e){e=e||L.getBoundingClientRect(),J.slider.offset={left:e.left+te.scroll.left,right:e.left+J.slider.width+te.scroll.left,top:e.top+te.scroll.top,bottom:e.bottom+J.slider.height+te.scroll.top}},position:function(e){e=e||L.getBoundingClientRect(),J.slider.position={left:e.left,center:e.left+J.slider.width/2,right:e.right,top:e.top,middle:e.top+J.slider.height/2,bottom:e.bottom}},scrollTop:function(e,t){var i=parseFloat(e.offset().top)+(t.offset||0);switch(t.scrollposition){default:case"top":break;case"middle":i=i-window._lsData.viewport.height/2+e.height()/2;break;case"bottom":i=i+e.height()-window._lsData.viewport.height}return i}},check:{initialized:function(){J.debugMode&&J.debug.add("log","sliderInit.info",[J.plugin.version,J.plugin.releaseDate,J.userInitOptions.sliderVersion||"n/a or slider version is pre 6.0.0",$.attr("id"),W,ie.fn.jquery,J.meta.lswpVersion,J.meta.wpVersion],!0),J.slider.initialized||(J.slider.initialized=!0,this.skins())},skins:function(){J.o.skin&&""!==J.o.skin&&J.o.skinsPath&&""!==J.o.skinsPath?J.gui.skin.load():J.slider.init()},showHide:function(){te.isMobile&&!1!==J.o.hideOnMobile||(te.viewport.width<J.o.hideUnder||te.viewport.width>J.o.hideOver&&0<J.o.hideOver?J.slider.hide():J.slider.show())},visibility:function(){!!J.slider.$innerWrapper.width()?J.slider.state.isHidden&&(J.slider.state.isHidden=!1,J.resize.all({forceToGetParentWidth:!0})):J.slider.state.isHidden=!0},positionToViewport:function(){var e;delete J.timeouts.checkPosition,J.o.playByScroll&&J.device.scroll.direction&&(e="down"===J.device.scroll.direction?te.scroll.top:J.slider.offset.top-(te.viewport.height-J.slider.height)/2,(("down"===J.device.scroll.direction?J.slider.offset.top-(te.viewport.height-J.slider.height)/2:te.scroll.top)<e&&("up"===J.device.scroll.direction&&"under"===J.slider.positionToViewport||"down"===J.device.scroll.direction&&"over"===J.slider.positionToViewport)||te.document.height<=te.viewport.height||J.slider.height<te.viewport.height&&("up"===J.device.scroll.direction&&te.scroll.top<=0&&J.slider.offset.top+J.slider.height/2<te.viewport.height/2||"down"===J.device.scroll.direction&&te.scroll.top>=te.document.height-te.viewport.height&&J.slider.offset.top+J.slider.height/2>te.scroll.top+te.viewport.height/2))&&(J.slider.positionToViewport="inside",J.resize.viewport(),J.device.scroll.disable()))}},init:function(){clearTimeout(J.timeouts.skinLoad1),clearTimeout(J.timeouts.skinLoad2),clearTimeout(J.timeouts.skinLoad3),clearTimeout(J.timeouts.skinLoad4),J.slider.set.styles(),J.slider.set.options(),J.slider.set.pictureElements(),J.slides.init(),J.device.fullscreen.set(),J.media.init(),J.gui.timers.init(),J.gui.loadingIndicator.init(),J.preload.init(),J.gui.shadow.init(),J.navigation.init(),J.slideshow.init(),J.slides.set.firstSlide(),J.gui.navigation.init(),J.gui.media.init(),J.resize.slider(),J.yourLogo.init(),J.slider.set.events(),J.api.hasEvent("sliderDidLoad")&&J.api.triggerEvent("sliderDidLoad",J.api.eventData()),J.functions.setStates(J.slider,{isLoaded:!0}),J.slider.state.shouldBeDestroyed?J.api.methods("destroy"):(J.slider.isPopup||(J.intervals.checkSliderVisibility=setInterval(function(){J.slider.check.visibility()},500)),J.slideshow.changeTo(J.slides.first.index))},hide:function(){$.addClass("ls-forcehide"),$.closest(".ls-wp-fullwidth-container").addClass("ls-forcehide"),$.closest(".ls-popup").addClass("ls-forcehide"),$.closest(".ls-popup").prev(".ls-popup-overlay").addClass("ls-forcehide")},show:function(){$.removeClass("ls-forcehide"),$.closest(".ls-wp-fullwidth-container").removeClass("ls-forcehide"),$.closest(".ls-popup").removeClass("ls-forcehide"),$.closest(".ls-popup").prev(".ls-popup-overlay").removeClass("ls-forcehide")}},J.functions={getData:function(e,t){return!J.o.getData||-1===J.o.getData.indexOf(e)||(J.api.triggerEvent("getData",{property:e,value:t}),!J.o.destroyAfter)||(J.api.methods("destroy",!0),!1)},convert:{transformOrigin:function(e,t,i,a){for(var s=(d=ie.trim(e)).split(" "),r=a[0].style,o="",n=["Left","Top"],l=[J.slider.width,J.slider.height],d=d.replace("sliderleft","0").replace("sliderright","100%").replace("slidercenter","50%").replace("slidermiddle","50%").replace("slidertop","0").replace("sliderbottom","100%").replace("left","0").replace("right","100%").replace("center","50%").replace("middle","50%").replace("top","0").replace("bottom","100%").split(" "),p=0;p<d.length;p++)-1!==s[p].indexOf("slider")?(J.transitions.layers.timeline.shouldRestart=!0,o+=p<2?l[p]/(100/parseInt(d[p]))-parseInt(r[n[p].toLowerCase()])-parseInt(r["margin"+n[p]])+"px ":"0px"):-1!==d[p].indexOf("%")?o+=d[p]+" ":-1!==d[p].indexOf("em")?o+=parseFloat(d[p])*parseInt(t.css("font-size"))+"px ":o+=parseInt(d[p])*i.settings.calculatedratio+"px ";return ie.trim(o)},specialValuesOfTransformOrigin:function(e){return e=e.replace("sliderleft","0").replace("sliderright","100%").replace("slidercenter","50%").replace("slidermiddle","50%").replace("slidertop","0").replace("sliderbottom","100%").replace("left","0").replace("right","100%").replace("center","50%").replace("middle","50%").replace("top","0").replace("bottom","100%").split(" ")},nodesTransformOrigin:function(e,d,p,t){for(var i="object"==typeof e,c=(d.length,[]),u=t[0].style,h=["left","top"],a=function(e,t){for(var t=ie(d[t]),i=parseInt(t.css("font-size")),a=t.position(),s=(n=ie.trim(e)).split(" "),r="",o=[J.slider.width,J.slider.height],n=J.functions.convert.specialValuesOfTransformOrigin(n),l=0;l<n.length;l++)-1!==s[l].indexOf("slider")?(J.transitions.layers.timeline.shouldRestart=!0,r+=l<2?o[l]/(100/parseInt(n[l]))-parseInt(a[h[l]])-parseInt(u[h[l].toLowerCase()])+"px ":"0px"):-1!==n[l].indexOf("%")?r+=n[l]+" ":-1!==n[l].indexOf("em")?r+=parseFloat(n[l])*i+"px ":r+=parseInt(n[l])*p.settings.calculatedratio+"px ";c.push(ie.trim(r))},s=0,r=i&&e.length,o=0;o<d.length;o++)i?(a(e[s],o),++s==r&&(s=0)):a(e,o);return c},easing:function(e,t){return"string"!=typeof e?e:(-1!==(e=e.toLowerCase()).indexOf("swing")||-1!==e.indexOf("linear")?i=ee.Linear.easeNone:(a=e.match(/(easeinout|easein|easeout)(.+)/)[2],a=ee[a.charAt(0).toUpperCase()+a.slice(1)],-1!==e.indexOf("easeinout")?i=a.easeInOut:-1!==e.indexOf("easeout")?i=t?a.easeIn:a.easeOut:-1!==e.indexOf("easein")&&(i=t?a.easeOut:a.easeIn)),i);var i,a},transition:function(e,t,i,a){var s=ie.extend({},e);return ie.each({de:"delay",rotate:"rotation",rotateX:"rotationX",rotateY:"rotationY"},function(e,t){e in s&&(s[t]=s[e],delete s[e])}),"after"===i?s.scaleX=s.scaleY=s.scaleZ=1:s.scale3d!==a&&(s.scaleX=s.scaleY=s.scaleZ=s.scale3d,delete s.scale3d),s.delay&&(s.delay="after"===i?s.delay/1e3:s.delay),void 0===t&&(t="easeInOutQuart"),s.ease=J.functions.convert.easing(t),s},properties:function(e,t){if("string"==typeof e)return J.functions.convert._properties(e,t);if("object"!=typeof e)return e;for(var i in e)e[i]=J.functions.convert._properties(e[i],t);return e},_properties:function(e,t){if(-1!==["enable","enabled","true"].indexOf(e))return!0;if(-1!==["disable","disabled","false","undefined","null"].indexOf(e))return!1;if("string"!=typeof e||-1===e.indexOf(J.defaults.init.staggerSplitChar)||e.charAt(0)===J.defaults.init.openingBracket)return t?""+parseInt(e)=="NaN"?0:parseInt(e):ie.isNumeric(e)?parseFloat(e):e;for(var i=e.split(J.defaults.init.staggerSplitChar),a=[],s=0;s<i.length;s++)a[s]=ie.isNumeric(i[s])?parseFloat(ie.trim(i[s])):ie.trim(i[s]);return a},oldProperties:function(i){return ie.each({firstLayer:"firstSlide",loops:"cycles",forceLoopNum:"forceCycles",layersContainer:"layersContainerWidth",sublayerContainer:"layersContainerWidth",randomSlideshow:"shuffleSlideshow"},function(e,t){e in i&&(i[t]=i[e],delete i[e])}),i},randomProperties:function(e,t,i,a){var i=i||!1,a=a||!!i&&i.data(J.defaults.init.dataKey),s="Width",r=!1;"number"!=typeof e||isNaN(e)||(e=e.toString()),-1===t.indexOf("height")&&-1===t.indexOf("top")&&-1===t.indexOf("y")||(s="Height"),-1===t.indexOf("duration")&&-1===t.indexOf("delay")&&-1===t.indexOf("start")&&-1===t.indexOf("scale")&&-1===t.indexOf("opacity")&&-1===t.indexOf("rotat")&&-1===t.indexOf("skew")||(r=!0),-1!==e.indexOf("random")&&-1!==e.indexOf("(")&&-1!==e.indexOf(",")&&-1!==e.indexOf(")")?e=e.split("(")[1].split(")")[0].replace(",",J.defaults.init.rangeSplitChar):e.charAt(0)===J.defaults.init.openingBracket&&e.charAt(e.length-1)===J.defaults.init.closingBracket&&(e=e.substring(1,e.length-1)),e=e.replace(/,/g,".").replace(/٫/g,".");var o,n=-1===t.indexOf("scale")&&-1===t.indexOf("opacity")?1:100,l=e;return-1!==e.indexOf(J.defaults.init.randomSplitChar)?l=(l=(o=e.split(J.defaults.init.randomSplitChar))[Math.floor(Math.random()*o.length)].trim()).charAt(0)===J.defaults.init.openingBracket&&l.charAt(l.length-1)===J.defaults.init.closingBracket?J.functions.convert.randomProperties(l,t,i,a):parseFloat(i?J.resize.getXY(i,a,l,s,r):l):-1!==e.indexOf(J.defaults.init.rangeSplitChar)&&((o=e.split(J.defaults.init.rangeSplitChar))[0]=parseFloat((i?J.resize.getXY(i,a,o[0],s,r):o[0])*n),o[1]=parseFloat((i?J.resize.getXY(i,a,o[1],s,r):o[1])*n),o.sort(function(e,t){return e-t}),l=Math.floor(Math.random()*(o[1]-o[0]+1)+o[0])/n),"string"==typeof l&&ie.isNumeric(l)&&(l=parseFloat(l)),l},clipProperties:function(e,t,i,a){if(-1!==e.indexOf("polygon")&&-1===e.indexOf("clip")&&-1===e.indexOf("px")&&-1===e.indexOf(", "))return e;for(var s,r,o=e,n=-1===(e=-1!==e.indexOf("(")?ie.trim(e.split("(")[1].split(")")[0]):ie.trim(e)).indexOf(",")?e.split(" "):e.split(","),l=[["0%","0%"],["100%","0%"],["100%","100%"],["0%","100%"]],d=ie.isNumeric(t.original.width)?t.original.width:i.outerWidth(),p=ie.isNumeric(t.original.height)?t.original.height:i.outerHeight(),c=function(e){return Math.round(100*(e+Number.EPSILON))/100},u=a&&-1!==o.indexOf("rect")?0:100,h=0;h<n.length;h++)if(-1===(s=n[h].trim()).indexOf(" ")){if("number"==typeof parseInt(s))switch(0===parseInt(s)&&(s="0%"),h){case 0:-1===s.indexOf("%")&&(s=c(parseInt(s)/(p/100))+"%"),l[0][1]=s,l[1][1]=s;break;case 1:-1!==s.indexOf("%")?(l[1][0]=100-parseInt(s)+"%",l[2][0]=100-parseInt(s)+"%"):(-1===s.indexOf("%")&&(s=c(Math.abs(parseInt(s)/(d/100)-u))+"%"),l[1][0]=s,l[2][0]=s);break;case 2:-1!==s.indexOf("%")?(l[2][1]=100-parseInt(s)+"%",l[3][1]=100-parseInt(s)+"%"):(-1===s.indexOf("%")&&(s=c(Math.abs(parseInt(s)/(p/100)-u))+"%"),l[2][1]=s,l[3][1]=s);break;case 3:-1===s.indexOf("%")&&(s=c(parseInt(s)/(d/100))+"%"),l[0][0]=s,l[3][0]=s}}else-1===(r=s.split(" "))[0].indexOf("%")&&(r[0]=c(parseInt(r[0])/(d/100))+"%"),l[h][0]=r[0],-1===r[1].indexOf("%")&&(r[1]=c(parseInt(r[1])/(p/100))+"%"),l[h][1]=r[1];for(var m="polygon(",f=0;f<l.length;f++)m+=l[f].join(" "),f<l.length-1&&(m+=",");return m+=")"}},getSliderClosestParentElementWithNumericValueOfProperty:function(e){for(var t,i=$.parents().not(".ls-fullscreen-wrapper"),a=i.length,s=100,r=0;r<a;r++)if("auto"!==(t=window.getComputedStyle(i[r]).getPropertyValue(e))){if(-1!==t.indexOf("px")&&(0<parseInt(t)||J.slider.isPopup))return J.slider.$parentWithNumericWidthValue=ie(i[r]),ie(i[r]);-1!==t.indexOf("%")&&(s=s/100*parseInt(t),J.slider.$parentWithNumericWidthValuePercent=s)}},sortArray:function(e,t,i){var a,s,r,o,n,l,d,p=[],c=e*t;switch(i){case"forward":for(a=0;a<e;a++)for(s=0;s<t;s++)p.push(a+s*e);break;case"reverse":for(a=e-1;-1<a;a--)for(s=t-1;-1<s;s--)p.push(a+s*e);break;case"center":for(n=Math.floor(c/2),r=0;r<n;r++)p.push(r);for(o=n;0<=o;o--)p.push(o);break;case"edge":case"mirror":for(r=n=Math.floor(c/2);0<r;r--)p.push(r);for(o=0;o<=n;o++)p.push(o);break;case"radial-out":for(l=e/2,d=t/2,a=0;a<e;a++)for(s=0;s<t;s++)p.push(Math.floor(Math.abs(d-s-.5))+Math.floor(Math.abs(l-a-.5)));break;case"radial-in":for(l=e/2,d=t/2,a=0;a<e;a++)for(s=0;s<t;s++)p.push(Math.floor(d-Math.ceil(Math.abs(d-s-.5)))+Math.floor(l-Math.ceil(Math.abs(l-a-.5))))}return p},shuffleArray:function(e){for(var t,i,a=e.length;0!==a;)i=Math.floor(Math.random()*a),t=e[--a],e[a]=e[i],e[i]=t;return e},countProp:function(e){var t,i=0;for(t in e)e.hasOwnProperty(t)&&++i;return i},getURL:function(e){return e[0].currentSrc||(e.data("src")?e.data("src"):e.attr("src"))},getALT:function(e){return!!e.attr("alt")&&e.attr("alt")},setStates:function(e,t,i){if(e&&e.state){var a=J.slideshow.isPaused();if(i)e.state[t]=i;else for(var s in t)e.state[s]=t[s];i=J.slideshow.isPaused();e==J.slideshow&&(J.api.hasEvent("slideshowStateDidChange")&&J.api.triggerEvent("slideshowStateDidChange",J.api.eventData()),i!=a&&(i?J.api.hasEvent("slideshowDidPause")&&J.api.triggerEvent("slideshowDidPause",J.api.eventData()):(J.slideshow.start(),J.api.hasEvent("slideshowDidResume")&&J.api.triggerEvent("slideshowDidResume",J.api.eventData()))))}},clearTimers:function(e={}){if(e.timeout&&J.timeouts[e.timeout])clearTimeout(J.timeouts[e.timeout]),delete J.timeouts[e.timeout];else if(!e.timeout)for(var t in J.timeouts)clearTimeout(J.timeouts[t]),delete J.timeouts[t];if(e.interval&&J.intervals[e.interval])clearInterval(J.intervals[e.interval]),delete J.intervals[e.interval];else if(!e.interval)for(var t in J.intervals)clearInterval(J.intervals[t]),delete J.intervals[t]},clearTimelines:function(){J.transitions.timelines.set("all",function(e,t){e.pause().clear().kill(),delete J.transitions[t]}),ee.TweenMax.killTweensOf($.find(".ls-bg, .ls-layer, .ls-wrapper, .ls-curtile, .ls-nexttile").get()),$.find(".ls-layer").each(function(){var e=ie(this).data(J.defaults.init.dataKey);e.is.countdown&&J.countdown.stopInterval(e.countdownSettings),e.is.counter&&J.counter.stop(e.counterSettings)})},resetSlideTimelines:function(){J.transitions.timelines.set("layers",function(e,t){e.pause().progress(0).clear().kill(),delete J.transitions[t]}),J.transitions.timelines.set("allforce",function(e,t){e.pause().progress(1).clear().kill(),delete J.transitions[t]}),$.find(".ls-layer:not(.ls-bg-video)").each(function(){var e=ie(this);if(void 0!==e.attr("data-ls-active")&&!1!==e.attr("data-ls-active")&&void 0!==e.attr("data-ls-static")&&!1!==e.attr("data-ls-static")&&parseInt(e.attr("data-ls-slidein"))!==J.slides.current.index)return!0;e=e.data(J.defaults.init.dataKey);e.loop._timeline&&(e.loop._timeline.stop().clear(),delete e.loop._timeline,ee.TweenMax.set(e.elements.$loopWrapper[0],e.reset.altWrapperOnSlideChange)),ee.TweenMax.set(e.elements.$wrapper[0],e.reset.wrapperOnSlideChange)})},clearEvents:function(){M.add("body").add(document).add($).add($.find("*")).add("."+W).off("."+W+" .debug"+W+" .parallax"+W+" .scroll"+W),window._layerSlider.closePopupsOnEsc&&window._layerSlider.closePopupsOnEsc[W]&&delete window._layerSlider.closePopupsOnEsc[W],delete window._layerSliders[W],$.off()},objectIsEmpty:function(e){return!e||"object"!=typeof e||0===Object.keys(e).length},getUID:function(e){for(var t="";t.length<e+2;)t+=Math.random().toString(36).substring(2);return t.substring(2,e+2)}},J.device={scroll:{keys:[32,33,34,35,36,37,38,39,40],disable:function(){window.addEventListener("scroll",this.preventDefault,{passive:!1,capture:!0}),window.addEventListener("wheel",this.preventDefault,{passive:!1,capture:!0}),window.addEventListener("mousewheel",this.preventDefault,{passive:!1,capture:!0}),window.addEventListener("touchmove",this.preventDefault,{passive:!1,capture:!0}),window.addEventListener("keydown",this.preventDefaultForScrollKeys,{capture:!0})},enable:function(){window.removeEventListener("scroll",this.preventDefault,{passive:!1,capture:!0}),window.removeEventListener("wheel",this.preventDefault,{passive:!1,capture:!0}),window.removeEventListener("mousewheel",this.preventDefault,{passive:!1,capture:!0}),window.removeEventListener("touchmove",this.preventDefault,{passive:!1,capture:!0}),window.removeEventListener("keydown",this.preventDefaultForScrollKeys,{capture:!0})},preventDefault:function(e){(e=e||window.event).preventDefault&&e.preventDefault(),e.returnValue=!1},preventDefaultForScrollKeys:function(e){if(-1!==J.device.scroll.keys.indexOf(e.keyCode))return J.device.scroll.preventDefault(e),!1}},removeSelection:function(){window.getSelection?window.getSelection().empty?window.getSelection().empty():window.getSelection().removeAllRanges&&window.getSelection().removeAllRanges():document.selection&&document.selection.empty()},fullscreen:{enter:function(){J.functions.setStates(J.slider,{inFullscreen:!0}),ie("body, html").addClass("ls-fullscreen"),J.slider.fullscreenWrapper.requestFullscreen(),$.trigger("mouseleave"),J.device.removeSelection()},exit:function(){J.functions.setStates(J.slider,{inFullscreen:!1}),J.resize.all(),ie("body, html").removeClass("ls-fullscreen"),J.device.removeSelection()},toggle:function(){J.device.fullscreen.element()?(J.device.fullscreen.exit(),document.exitFullscreen()):J.device.fullscreen.enter()},set:function(){J.o.allowFullscreen&&(document.fullscreenEnabled||document.webkitFullscreenEnabled||document.mozFullScreenEnabled||document.msFullscreenEnabled)&&($.wrap('<div class="ls-fullscreen-wrapper"></div>'),J.slider.$fullscreenWrapper=$.closest(".ls-fullscreen-wrapper"),J.slider.fullscreenWrapper=J.slider.$fullscreenWrapper[0],J.slider.$spacingWrapper=J.slider.$fullscreenWrapper,J.slider.fullscreenWrapper.requestFullscreen=J.slider.fullscreenWrapper.requestFullscreen||J.slider.fullscreenWrapper.webkitRequestFullscreen||J.slider.fullscreenWrapper.mozRequestFullScreen||J.slider.fullscreenWrapper.msRequestFullscreen,document.exitFullscreen=document.exitFullscreen||document.webkitExitFullscreen||document.mozCancelFullScreen||document.msExitFullscreen,ie(document).on("fullscreenchange."+W+" webkitfullscreenchange."+W+" mozfullscreenchange."+W+" msfullscreenchange."+W,function(){J.device.fullscreen.element()||J.device.fullscreen.exit()}),J.slider.$fullscreenWrapper.on("dblclick."+W,function(){J.device.fullscreen.toggle()}))},element:function(){return document.fullscreenElement||document.webkitFullscreenElement||document.mozFullScreenElement||document.msFullscreenElement}}},J.actions={do:function(t){var e,i,a=!1,s="";switch(t.action){case"scrollBelowProject":case"scrollToNextProject":case"scrollToPrevProject":case"scrollToElement":case"scrollToSelf":case"scrollToScenePosition":case"scrollToTimelinePosition":J.slider.isScene||$.addClass("ls-action-trigger"),J.slider.isScrollScene&&"scrollToTimelinePosition"===t.action&&(t.action="scrollToScenePosition",t.target="currentScene");var r,o,n,l=ie(".ls-wp-container:not(.ls-hidden):not([data-scene]), ls-scene-wrapper"),d=ie("ls-scene-wrapper, .ls-action-trigger"),p=ie([]),c=J.slider.isScene?J.slider.$spacingWrapper:$,u=d.length,h=d.index(c);if(J.slider.isScene||$.removeClass("ls-action-trigger"),"scrollToSelf"===t.action)v=J.slider.$spacingWrapper.offset().top+(t.offset||0);else if("scrollBelowProject"===t.action)v=J.slider.$spacingWrapper.offset().top+J.slider.$spacingWrapper.height()+(t.offset||0);else if("scrollToPrevProject"===t.action)0!==l.index(c)?(p=l.eq(l.index(c)-1),v=J.slider.get.scrollTop(p,t)):a="There’s no previous project to scroll to.";else if("scrollToNextProject"===t.action)l.index(c)<l.length-1?(p=l.eq(l.index(c)+1),v=J.slider.get.scrollTop(p,t)):a="There’s no next project to scroll to.";else if(-1!==t.action.indexOf("scrollToElement")){try{p=ie(t.selector.toString()).not(".ls-wp-container.ls-hidden")}catch(e){s=e}p.length?((n=p.closest("ls-scene-wrapper")).length&&(p=n),v=J.slider.get.scrollTop(p,t)):a='Couldn’t find the scroll target element "'+t.selector+'". Please verify the selector you’ve entered.'}else if("scrollToScenePosition"===t.action){switch(t.target){case"currentScene":J.slider.isScene?p=c:a="Current project is not a scene.";break;case"nextScene":h+1<u?p=ie(d[h+1]):a="There are no next scenes in the DOM.";break;case"previousScene":0<=h-1?p=ie(d[h-1]):a="There are no previous scenes in the DOM."}p.length&&(r=parseFloat(p.attr("data-scene-duration")),o=p.height(),n=(l=p.find(".ls-container")).height(),t.position=ie.trim(t.position)||"0ms",v=p.is('[data-scene="scroll"]')?(m=-1!==t.position.indexOf("%")||-1===t.position.indexOf("ms")?r*(Math.min(100,Math.max(0,parseFloat(t.position)))/100):Math.min(Math.max(0,parseFloat(t.position)/1e3),r),Math.round(parseFloat(p.offset().top)-parseFloat(l.css("top"))+m/r*(o-n))):(m=Math.min(100,Math.max(0,parseFloat(t.position)))/100,Math.round(parseFloat(p.offset().top)-parseFloat(l.css("top"))+(o-n)*m)))}else if("scrollToTimelinePosition"===t.action){var m=J.transitions._slideTimeline.duration(),f=m*(100*J.transitions._slideTimeline.progress()/100),g=0,g=-1!==t.position.indexOf("%")||-1===t.position.indexOf("ms")?m*parseFloat(t.position)/100:parseFloat(t.position)/1e3;switch(g=Math.abs(g),(t.position+"").charAt(0)){case"+":g=f+g;break;case"-":g=f-g}g=Math.max(Math.min(g,m),0)}a||(t.duration=ie.isNumeric(t.duration)?t.duration:1e3,"scrollToTimelinePosition"===t.action?J.transitions._slideTimeline.tweenTo(g,{ease:J.functions.convert.easing(t.easing)}).duration(t.duration/1e3):ee.TweenMax.to("html, body",t.duration/1e3,{scrollTop:v,ease:J.functions.convert.easing(t.easing)}));break;case"switchSlide":J.slideshow.changeTo(t.slide,!0,!0);break;case"nextSlide":case"prevSlide":case"lastSlide":case"firstSlide":case"stopSlideshow":case"startSlideshow":"nextSlide"===t.action&&(x="next"),"prevSlide"===t.action&&(x="prev"),"lastSlide"===t.action&&(x="last"),"firstSlide"===t.action&&(x="first"),"stopSlideshow"===t.action&&(x="stop"),"startSlideshow"===t.action&&(x="start"),J.navigation[x]("clicked");break;case"replaySlide":J.api.methods("replay");break;case"reverseSlide":J.api.methods("reverse",t.replay);break;case"resetSlide":J.api.methods("resetSlide");break;case"pauseProject":J.api.methods("pauseSlider");break;case"resumeProject":J.api.methods("resumeSlider");break;case"toggleProject":J.api.methods("toggleSlider");break;case"playMedia":J.media.functions.playActiveMedia();break;case"pauseMedia":J.media.functions.pauseActiveMedia();break;case"unmuteMedia":J.media.unmute.multipleMediaElements();break;case"openPopup":var y,v=ie('[data-ls-slug="'+t.popup+'"], [id^=layerslider_'+t.popup+"]").first(),x=v.data("lsSliderUID");v.length?((y=window._layerSliders[x].initializedPlugins.popup).plugin.settings.showOnce=!1,"opened"==y.state.is?t.toggle&&y.events.hide():t.slide?v.layerSlider("fastChangeTo",t.slide,function(){y.events.show()}):y.events.show()):(v={action:"ls_get_popup_markup",id:t.popup,slide:t.slide},ie.get(J.o.ajaxURL||window.ajaxurl||"/wp-admin/admin-ajax.php",v,function(e){ie(e).appendTo("body")}));break;case"launchPopups":for(i in window._layerSliders)(e=window._layerSliders[i]).initializedPlugins.popup&&e.api.methods("openPopup");break;case"closePopup":J.api.methods("closePopup");break;case"closeAllPopups":J.api.methods("closeAllPopups");break;case"jsFunction":try{window[t.function]()}catch(e){s=e,a="The browser thrown the following error after calling "+t.function+"() JavaScript function."}}a&&console.error('LayerSlider: Error while calling layer action "'+t.action+'". '+a+(""!==s?"\n\r\n\r":""),s)}},J.api={hasEvent:function(e,t){t=ie._data(t||L,"events");return!(!t||!t[e])},methods:function(e,t,i,a){if(!J.slider.isBusy())if("number"==typeof e)0<e&&e<J.slides.count+1&&e!=J.slides.current.index&&J.slideshow.changeTo(e,!0,!0);else switch(e){case"touchPrev":J.device.touchPrev=!0;case"previousSlide":case"prev":J.navigation.prev();break;case"touchNext":J.device.touchNext=!0;case"nextSlide":case"next":J.navigation.next();break;case"startSlideshow":case"start":J.navigation.start()}switch(e){case"openPopup":J.initializedPlugins.popup&&(t&&ie.isNumeric(t)?$.layerSlider("fastChangeTo",t,function(){J.initializedPlugins.popup.events.show()}):J.initializedPlugins.popup.events.show());break;case"launchPopups":J.actions.do("launchPopups");break;case"fastChangeTo":t&&ie.isNumeric(t)&&(J.slides.current.index!==t?(J.slideshow.forceFastChange=!0,J.slider.shouldResize=!0,i&&"function"==typeof i&&(J.slideshow.forceFastChangeCallback=i),J.slideshow.changeTo(t,!0,!0)):i&&i());break;case"resetScroll":J.slider.set.offset(),M.trigger("scroll.scroll"+W).trigger("touchmove.scroll"+W).trigger("scroll.parallax"+W).trigger("touchmove.parallax"+W);break;case"enableScene":J.slider.isScene&&(J.slider.$spacingWrapper.removeAttr("data-disabled-scene"),J.api.methods("resetScroll"));break;case"disableScene":J.slider.isScene&&(J.slider.$spacingWrapper.attr("data-disabled-scene",""),$.css({top:"auto",bottom:"auto"}),J.transitions.scrollscene.animate("start"),J.api.methods("resetScroll"));break;case"closePopup":J.initializedPlugins.popup&&J.initializedPlugins.popup.events.hide();break;case"closeAllPopups":ie('<div class="ls-close-all-popups-button"></div>').css("display","none").appendTo("body").trigger("click").remove();break;case"updateLayerData":t&&J.layers.update.data(t,i,a);break;case"redrawSlider":case"redraw":J.resize.all(),J.api.methods("resetScroll");break;case"replaySlide":case"replay":J.transitions._slideTimeline&&J.transitions._slideTimeline.restart();break;case"reverseSlide":case"reverse":J.transitions._slideTimeline&&(J.transitions._slideTimeline.reversed()?J.transitions._slideTimeline.play():J.transitions._slideTimeline.reverse(),t&&(J.transitions.layers.timeline.shouldReplay=!0));break;case"unmute":case"unmuteMedia":J.media.unmute.multipleMediaElements();break;case"stopSlideshow":case"stop":J.navigation.stop();break;case"pauseSlider":case"pause":t&&"performanceMode"===t&&J.functions.setStates(J.slideshow,{pausedByPerformance:!0}),J.transitions._slideTimeline&&!J.slider.isScrollScene&&J.transitions._slideTimeline.stop(),J.layers.get("active").each(function(){var e=ie(this).data(J.defaults.init.dataKey);e.loop._timeline&&e.loop._timeline.stop()}),J.transitions.layers.parallax.state.paused=!0,J.transitions.layers.scroll.state.paused=!0,J.media.functions.pauseActiveMedia(!0);break;case"resumePopup":J.layers.get("active").each(function(){J.media.functions.playIfAllowed(ie(this))}),J.transitions._slideTimeline&&(J.transitions._slideTimeline.timeScale()<.001&&J.transitions.layers.timeline.resume(),J.transitions._slideTimeline.play()),J.transitions._slideTransition&&J.transitions._slideTransition.play();break;case"resumeSlider":case"resume":J.transitions._slideTimeline&&(J.transitions._slideTimeline.timeScale()<.001&&J.transitions.layers.timeline.resume(),J.slider.isScrollScene||J.transitions._slideTimeline.play()),J.media.functions.playActiveMedia(!0),J.layers.get("active").each(function(){var e=ie(this).data(J.defaults.init.dataKey);e.loop._timeline&&e.loop._timeline.play()}),J.transitions.layers.parallax.state.paused=!1,J.transitions.layers.scroll.state.paused=!1,J.transitions._slideTransition?J.transitions._slideTransition.play():J.o.animateFirstSlide&&J.slideshow.firstStart&&J.slider.state.readyForStart&&(J.resize.layers({callback:J.api.resumeCallback}),J.api.resumeCallback=!1),t&&"performanceMode"===t&&J.functions.setStates(J.slideshow,{pausedByPerformance:!1});break;case"playMedia":J.media.functions.playActiveMedia();break;case"pauseMedia":J.media.functions.pauseActiveMedia();break;case"toggleSlider":case"toggle":J.slider.state.isPaused?($.layerSlider("resume"),J.slider.state.isPaused=!1):($.layerSlider("pause"),J.slider.state.isPaused=!0);break;case"reset":case"resetSlider":break;case"resetSlide":case"resetCurrentSlide":J.transitions.timelines.set("layers",function(e,t){e.progress(0),e.stop()}),J.media.functions.stop(!0);break;case"destroy":case"kill":if(J.slider.state.isLoaded){if(J.functions.clearTimers(),J.functions.clearTimelines(),J.layers.$all.removeData(),J.api.hasEvent("sliderDidDestroy")&&J.api.triggerEvent("sliderDidDestroy"),J.slider.state.sholudBeRemoved||t){if(J.slider.$hiddenWrapper.remove(),J.gui.timers.slidebar.$containerElement)for(var s=0;s<J.gui.timers.slidebar.$containerElement.length;s++)J.gui.timers.slidebar.$containerElement[s]instanceof jQuery&&J.gui.timers.slidebar.$containerElement[s].remove();J.api.hasEvent("sliderDidRemove")&&J.api.triggerEvent("sliderDidRemove");var r=J.slider.$spacingWrapper;r.closest(".ls-popup").length&&(r=r.closest(".ls-popup")).prev(".ls-popup-overlay").remove(),r.remove(),J.slider.$silentWrapper&&J.slider.$silentWrapper.remove()}J.functions.clearEvents(),window._layerSlider.removeSlider(W)}else J.functions.setStates(J.slider,{shouldBeDestroyed:!0,sholudBeRemoved:t||!1});J.slider.positionToViewport="under",J.device.scroll.enable()}},eventData:function(){return{data:J,userData:J.o,uid:W,target:L,slider:$,state:J.slider.state,isBusy:J.slider.isBusy(),event:{target:L},api:function(e,t,i,a){$.layerSlider(e,t,i,a)},navigation:{direction:J.navigation.direction},slides:{first:{index:J.slides.first.index,deeplink:J.slides.get.deeplink(J.slides.first.index),data:J.slides.first.data},prev:{index:J.slides.prev.index,deeplink:J.slides.get.deeplink(J.slides.prev.index),data:J.slides.prev.data},current:{index:J.slides.current.index||J.slides.first.index,deeplink:J.slides.get.deeplink(J.slides.current.index),layersIn:J.layers.get("current,in"),layersOut:J.layers.get("current,out"),timeline:J.transitions._slideTimeline,data:J.slides.current.data},next:{index:J.slides.next.index,deeplink:J.slides.get.deeplink(J.slides.next.index),layersIn:J.layers.get("next,in"),layersOut:J.layers.get("next,out"),data:J.slides.next.data},count:J.slides.count},slideChangeTimeline:J.transitions._slideTransition,slideshow:{state:J.slideshow.state,sequence:J.slideshow.sequence,direction:J.slideshow.direction,isPaused:J.slideshow.isPaused()},cycles:{max:J.o.cycles,current:J.slideshow.curCycle}}},triggerEvent:function(t,e){var i,a;try{i=e?(a=$.triggerHandler(t+".layerSlider",e),$.triggerHandler(t+".$",e)):(a=$.triggerHandler(t+".layerSlider"),$.triggerHandler(t+".$"))}catch(e){console.error('LayerSlider: Error while calling event "'+t+'":\n\r\n\r',e)}return null!=a?a:"undefinded"!=typeof i&&null!==i?i:void 0}},J.browser={isSafari:!!navigator.userAgent.match(/(iPhone|iPod|iPad|Safari)/i)&&!navigator.userAgent.match(/(Opera|Chrome|Edge)/i),isChrome:function(){var e=window.chrome,t=window.navigator,i=t.vendor,a=void 0!==window.opr,s=-1<t.userAgent.indexOf("Edge"),s=!!t.userAgent.match("CriOS")||null!=e&&"Google Inc."===i&&!1==a&&!1==s;return s},usesFileProtocol:-1!==document.location.href.indexOf("file://"),supports3D:function(){for(var e=ie("<div>"),t=!1,i=!1,a=["perspective","OPerspective","msPerspective","MozPerspective","WebkitPerspective"],s=["transformStyle","OTransformStyle","msTransformStyle","MozTransformStyle","WebkitTransformStyle"],r=a.length-1;0<=r;r--)t=t||void 0!==e[0].style[a[r]];for(var o=s.length-1;0<=o;o--)e.css("transform-style","preserve-3d"),i=i||"preserve-3d"==e[0].style[s[o]];return t&&void 0!==e[0].style[a[4]]&&(e.attr("id","ls-test3d").appendTo($),t=3===e[0].offsetHeight&&9===e[0].offsetLeft,e.remove()),t&&i},isOld:-1!==navigator.userAgent.indexOf("rident/5")},J.initializedPlugins={},J.timeouts={},J.intervals={},J.debug={options:{}},J.plugin={version:"7.15.0",release:"stable",releaseDate:"2025. 06. 13."},J.slider.load()}}(jQuery);