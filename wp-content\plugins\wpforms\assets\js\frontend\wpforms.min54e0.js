var wpforms=window.wpforms||((s,d,p)=>{let m={cache:{},isUpdatingToken:!1,init(){p(m.ready),p(d).on("load",function(){"function"==typeof p.ready.then?p.ready.then(m.load):m.load()}),m.bindUIActions(),m.bindOptinMonster()},ready(){m.clearUrlQuery(),m.setUserIdentifier(),m.loadValidation(),m.loadHoneypot(),m.loadDatePicker(),m.loadTimePicker(),m.loadInputMask(),m.loadPayments(),m.loadMailcheck(),m.loadChoicesJS(),m.initTokenUpdater(),m.restoreSubmitButtonOnEventPersisted(),m.bindChoicesJS(),p(".wpforms-randomize").each(function(){for(var e=p(this),t=e.children();t.length;)e.append(t.splice(Math.floor(Math.random()*t.length),1)[0])}),p(".wpforms-page-button").prop("disabled",!1),m.initFormsStartTime(),p(s).trigger("wpformsReady")},load(){},clearUrlQuery(){var e=d.location;let t=e.search;-1!==t.indexOf("wpforms_form_id=")&&(t=t.replace(/([&?]wpforms_form_id=[0-9]*$|wpforms_form_id=[0-9]*&|[?&]wpforms_form_id=[0-9]*(?=#))/,""),history.replaceState({},null,e.origin+e.pathname+t))},loadHoneypot(){p(".wpforms-form").each(function(){let e=p(this),t=e.data("formid"),r=[],a=[];var o,i,s,n;void 0!==wpforms_settings.hn_data[t]&&(p(`#wpforms-form-${t} .wpforms-field`).each(function(){var e=p(this);r.push(e.data("field-id")),a.push(e.find(".wpforms-field-label").text())}),o=m.getHoneypotRandomLabel(a.join(" ").split(" ")),i=m.getHoneypotFieldId(r),n=r[Math.floor(Math.random()*r.length)],s=`wpforms-${t}-field_`+i,p(`#wpforms-${t}-field_${n}-container`,e).before(`
						<div id="${s}-container" class="wpforms-field wpforms-field-text" data-field-type="text" data-field-id="${i}" style="position: absolute !important; overflow: hidden !important; display: inline !important; height: 1px !important; width: 1px !important; z-index: -1000 !important; padding: 0 !important;">
							<label class="wpforms-field-label" for="${s}" aria-hidden="true" style="counter-increment: none;"></label>
							<input type="text" id="${s}" class="wpforms-field-medium" name="wpforms[fields][${i}]" aria-hidden="true" style="visibility: hidden;" tabindex="-1">
						</div>`),(n=p(`#wpforms-${t}-field_${wpforms_settings.hn_data[t]}-container`,e)).find("input").attr({tabindex:"-1","aria-hidden":"true"}),n.find("label").text(o).attr("aria-hidden","true"))})},getHoneypotRandomLabel(t){let r="";for(let e=0;e<3;e++)r+=t[Math.floor(Math.random()*t.length)]+" ";return r.trim()},getHoneypotFieldId(t){var r=Math.max(...t);let a=0;for(let e=1;e<r;e++)if(!t.includes(e)){a=e;break}return a=a||r+1},loadValidation(){void 0===p.fn.validate?d.location.hash&&"#wpformsdebug"===d.location.hash&&console.log("jQuery Validation library not found."):(p(".wpforms-input-temp-name").each(function(e,t){var r=Math.floor(9999*Math.random())+1;p(this).attr("name","wpf-temp-"+r)}),p(s).on("change",".wpforms-validate input[type=url]",function(){var e=p(this).val();if(!e)return!1;"http://"!==e.substr(0,7)&&"https://"!==e.substr(0,8)&&p(this).val("https://"+e)}),p.validator.messages.required=wpforms_settings.val_required,p.validator.messages.url=wpforms_settings.val_url,p.validator.messages.email=wpforms_settings.val_email,p.validator.messages.number=wpforms_settings.val_number,void 0!==p.fn.payment&&p.validator.addMethod("creditcard",function(e,t){e=p.payment.validateCardNumber(e);return this.optional(t)||e},wpforms_settings.val_creditcard),p.validator.addMethod("extension",function(e,t,r){return r="string"==typeof r?r.replace(/,/g,"|"):"png|jpe?g|gif",this.optional(t)||e.match(new RegExp("\\.("+r+")$","i"))},wpforms_settings.val_fileextension),p.validator.addMethod("maxsize",function(e,t,r){var a=r,r=this.optional(t);let o,i;if(r)return r;if(t.files&&t.files.length)for(o=0,i=t.files.length;o<i;o++)if(t.files[o].size>a)return!1;return!0},wpforms_settings.val_filesize),p.validator.addMethod("step",function(e,t,r){i=r;let a=Math.floor(i)!==i&&i.toString().split(".")[1].length||0;function o(e){return Math.round(e*Math.pow(10,a))}var i=o(p(t).attr("min"));return e=o(e)-i,this.optional(t)||o(e)%o(r)==0}),p.validator.methods.email=function(e,t){return this.optional(t)||(e=>{if("string"!=typeof e)return!1;var t=e.indexOf("@",1);if(e.length<6||254<e.length||-1===t)return!1;if(-1!==e.indexOf("@",t+1))return!1;var[t,e]=e.split("@");if(!t||!e)return!1;if(!/^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~.-]+$/.test(t)||63<t.length)return!1;if(/\.{2,}/.test(e)||e.trim(" \t\n\r\0\v.")!==e)return!1;if((t=e.split(".")).length<2)return!1;var r,a=/^[a-z0-9-]+$/i;for(r of t)if(63<r.length||r.trim(" \t\n\r\0\v-")!==r||!a.test(r))return!1;return!0})(e)},p.validator.addMethod("restricted-email",function(e,t){var r=p(t);return!r.val().length||(r=r.closest(".wpforms-form").data("formid"),Object.prototype.hasOwnProperty.call(m.cache,r)&&Object.prototype.hasOwnProperty.call(m.cache[r],"restrictedEmailValidation")&&Object.prototype.hasOwnProperty.call(m.cache[r].restrictedEmailValidation,e)?m.cache[r].restrictedEmailValidation[e]:(m.restrictedEmailRequest(t,e),"pending"))},wpforms_settings.val_email_restricted),p.validator.addMethod("confirm",function(e,t,r){t=p(t).closest(".wpforms-field");return p(t.find("input")[0]).val()===p(t.find("input")[1]).val()},wpforms_settings.val_confirm),p.validator.addMethod("required-payment",function(e,t){return 0<m.amountSanitize(e)},wpforms_settings.val_requiredpayment),p.validator.addMethod("time12h",function(e,t){return this.optional(t)||/^((0?[1-9]|1[012])(:[0-5]\d){1,2}(\ ?[AP]M))$/i.test(e)},wpforms_settings.val_time12h),p.validator.addMethod("time24h",function(e,t){return this.optional(t)||/^(([0-1]?[0-9])|([2][0-3])):([0-5]?[0-9])(\ ?[AP]M)?$/i.test(e)},wpforms_settings.val_time24h),p.validator.addMethod("turnstile",function(e){return e},wpforms_settings.val_turnstile_fail_msg),p.validator.addMethod("time-limit",function(e,t){var t=p(t),r=t.data("min-time");return void 0===r||!(t.prop("required")||!m.empty(e))||(t=t.data("max-time"),m.compareTimesGreaterThan(t,r)?m.compareTimesGreaterThan(e,r)&&m.compareTimesGreaterThan(t,e):m.compareTimesGreaterThan(e,r)&&m.compareTimesGreaterThan(e,t)||m.compareTimesGreaterThan(r,e)&&m.compareTimesGreaterThan(t,e))},function(e,t){t=p(t);let r=t.data("min-time"),a=t.data("max-time");return r=r.replace(/^00:([0-9]{2})pm$/,"12:$1pm"),a=a.replace(/^00:([0-9]{2})pm$/,"12:$1pm"),r=r.replace(/(am|pm)/g," $1").toUpperCase(),a=a.replace(/(am|pm)/g," $1").toUpperCase(),wpforms_settings.val_time_limit.replace("{minTime}",r).replace("{maxTime}",a)}),p.validator.addMethod("check-limit",function(e,t){var t=p(t).closest("ul"),r=parseInt(t.attr("data-choice-limit")||0,10);return 0===r||t.find('input[type="checkbox"]:checked').length<=r},function(e,t){t=parseInt(p(t).closest("ul").attr("data-choice-limit")||0,10);return wpforms_settings.val_checklimit.replace("{#}",t)}),p.validator.addMethod("inputmask-incomplete",function(e,t){return 0===e.length||void 0===p.fn.inputmask||p(t).inputmask("isComplete")},wpforms_settings.val_inputmask_incomplete),p.validator.addMethod("required-positive-number",function(e,t){return 0<m.amountSanitize(e)},wpforms_settings.val_number_positive),p.validator.addMethod("required-minimum-price",function(e,t,r){t=p(t);return""===e&&!t.hasClass("wpforms-field-required")||Number(m.amountSanitize(m.amountFormat(r)))<=Number(m.amountSanitize(e))},wpforms_settings.val_minimum_price),p.validator.addMethod("password-strength",function(e,t){var r=p(t),t=WPFormsPasswordField.passwordStrength(e,t);return""===e&&!r.hasClass("wpforms-field-required")||t>=Number(r.data("password-strength-level"))},wpforms_settings.val_password_strength),p(".wpforms-validate").each(function(){var e=p(this),t=e.data("formid");let r;r=void 0!==d["wpforms_"+t]&&d["wpforms_"+t].hasOwnProperty("validate")?d["wpforms_"+t].validate:"undefined"!=typeof wpforms_validate?wpforms_validate:{errorElement:m.isModernMarkupEnabled()?"em":"label",errorClass:"wpforms-error",validClass:"wpforms-valid",ignore:":hidden:not(textarea.wp-editor-area), .wpforms-conditional-hide textarea.wp-editor-area",ignoreTitle:!0,errorPlacement(e,t){m.isLikertScaleField(t)?(t.closest("table").hasClass("single-row")?t.closest(".wpforms-field"):t.closest("tr").find("th")).append(e):m.isWrappedField(t)?t.closest(".wpforms-field").append(e):m.isDateTimeField(t)?m.dateTimeErrorPlacement(t,e):m.isFieldInColumn(t)||m.isFieldHasHint(t)?t.parent().append(e):m.isLeadFormsSelect(t)?t.parent().parent().append(e):t.hasClass("wp-editor-area")?t.parent().parent().parent().append(e):e.insertAfter(t),m.isModernMarkupEnabled()&&e.attr({role:"alert","aria-label":wpforms_settings.errorMessagePrefix,for:""})},highlight(e,t,r){var a=p(e),o=a.closest(".wpforms-field"),i=a.attr("name");("radio"===a.attr("type")||"checkbox"===a.attr("type")?o.find('input[name="'+i+'"]'):a).addClass(t).removeClass(r),"password"===a.attr("type")&&""===a.val().trim()&&d.WPFormsPasswordField&&a.data("rule-password-strength")&&a.hasClass("wpforms-field-required")&&WPFormsPasswordField.passwordStrength("",e),o.addClass("wpforms-has-error")},unhighlight(e,t,r){var e=p(e),a=e.closest(".wpforms-field"),o=e.attr("name");("radio"===e.attr("type")||"checkbox"===e.attr("type")?a.find('input[name="'+o+'"]'):e).addClass(r).removeClass(t),a.find(":input.wpforms-error,[data-dz-errormessage]:not(:empty)").length||a.removeClass("wpforms-has-error"),m.isModernMarkupEnabled()&&e.parent().find("em.wpforms-error").remove()},submitHandler(s){function n(e,t){let r="label",a="";m.isModernMarkupEnabled()&&(r="em",a='role="alert"');var o=`<${r} id="wpforms-field_recaptcha-error" class="wpforms-error" ${a}> ${wpforms_settings.val_recaptcha_fail_msg}</${r}>`;e.find(".wpforms-recaptcha-container").append(o),m.restoreSubmitButton(e,t)}function l(e){var t=e.find(".wpforms-submit");t.prop("disabled",!0),WPFormsUtils.triggerEvent(e,"wpformsFormSubmitButtonDisable",[e,t])}function e(){let e=p(s),t=e.closest(".wpforms-container"),r=e.find(".wpforms-submit"),a=r.data("captchaInvalid"),o=r.data("alt-text"),i=r.get(0).recaptchaID;return e.data("token")&&0===p(".wpforms-token",e).length&&p('<input type="hidden" class="wpforms-token" name="wpforms[token]" />').val(e.data("token")).appendTo(e),e.find("#wpforms-field_recaptcha-error").remove(),l(e),o&&r.text(o),a?n(e,t):m.empty(i)&&0!==i?(p(".wpforms-input-temp-name").removeAttr("name"),void m.formSubmit(e)):(grecaptcha.execute(i).then(null,function(){grecaptcha.getResponse()||n(e,t)}),!1)}return"function"==typeof wpformsRecaptchaV3Execute?(l(p(s)),wpformsRecaptchaV3Execute(e)):e()},invalidHandler(e,t){void 0!==t.errorList[0]&&m.scrollToError(p(t.errorList[0].element))},onkeyup:WPFormsUtils.debounce(function(e,t){p(e).hasClass("wpforms-novalidate-onkeyup")||9===t.which&&""===this.elementValue(e)||-1!==p.inArray(t.keyCode,[16,17,18,20,35,36,37,38,39,40,45,144,225])||(e.name in this.submitted||e.name in this.invalid)&&this.element(e)},1e3),onfocusout:function(e){let t=!1;p(e).hasClass("wpforms-novalidate-onkeyup")&&!e.value&&(t=!0),this.checkable(e)||!(e.name in this.submitted)&&this.optional(e)||(t=!0),(t=p(e).data("server-error")?!1:t)&&this.element(e)},onclick(e){let t=!1;var r=(e||{}).type;let a=p(e);-1<["checkbox","radio"].indexOf(r)&&((a=a.hasClass("wpforms-likert-scale-option")?a.closest("tr"):a.closest(".wpforms-field")).find("label.wpforms-error, em.wpforms-error").remove(),t=!0),t&&this.element(e)}},e.validate(r),m.loadValidationGroups(e)}))},restrictedEmailRequest(r,a){var e=p(r),t=e.closest("form");let o=t.data("validator"),i=t.data("formid");t=e.closest(".wpforms-field").data("field-id");m.cache[i]=m.cache[i]||{},o.startRequest(r),p.post({url:wpforms_settings.ajaxurl,type:"post",data:{action:"wpforms_restricted_email",form_id:i,field_id:t,email:a},dataType:"json",success(e){var t={},e=e.success&&e.data;e||(t[r.name]=wpforms_settings.val_email_restricted,o.showErrors(t)),m.cache[i].restrictedEmailValidation=m.cache[i].restrictedEmailValidation||[],Object.prototype.hasOwnProperty.call(m.cache[i].restrictedEmailValidation,a)||(m.cache[i].restrictedEmailValidation[a]=e),o.stopRequest(r,e)}})},isFieldInColumn(e){return e.parent().hasClass("wpforms-one-half")||e.parent().hasClass("wpforms-two-fifths")||e.parent().hasClass("wpforms-one-fifth")},isFieldHasHint(e){return 0<e.nextAll(".wpforms-field-sublabel, .wpforms-field-description, .wpforms-field-limit-text, .wpforms-pass-strength-result").length},isDateTimeField(e){return e.hasClass("wpforms-timepicker")||e.hasClass("wpforms-datepicker")||e.is("select")&&e.attr("class").match(/date-month|date-day|date-year/)},isWrappedField(e){return"checkbox"===e.attr("type")||"radio"===e.attr("type")||"range"===e.attr("type")||"select"===e.is("select")||1===e.data("is-wrapped-field")||e.parent().hasClass("iti")||e.hasClass("wpforms-validation-group-member")||e.hasClass("choicesjs-select")||e.hasClass("wpforms-net-promoter-score-option")||e.hasClass("wpforms-field-payment-coupon-input")},isLikertScaleField(e){return e.hasClass("wpforms-likert-scale-option")},isLeadFormsSelect(e){return e.parent().hasClass("wpforms-lead-forms-select")},isCoupon(e){return console.warn('WARNING! Function "wpforms.isCoupon( element )" has been deprecated'),e.closest(".wpforms-field").hasClass("wpforms-field-payment-coupon")},dateTimeErrorPlacement(e,t){var r=e.closest(".wpforms-field-row-block, .wpforms-field-date-time");r.length?r.find("label.wpforms-error, em.wpforms-error").length||r.append(t):e.closest(".wpforms-field").append(t)},loadDatePicker(e){void 0!==p.fn.flatpickr&&(e=e?.length?e:p(s)).find(".wpforms-datepicker-wrap").each(function(){let a=p(this),e=a.find("input"),t=a.closest(".wpforms-form"),r=t.data("formid"),o=a.closest(".wpforms-field").data("field-id"),i;var s;!(i=void 0!==d["wpforms_"+r+"_"+o]&&d["wpforms_"+r+"_"+o].hasOwnProperty("datepicker")?d["wpforms_"+r+"_"+o].datepicker:void 0!==d["wpforms_"+r]&&d["wpforms_"+r].hasOwnProperty("datepicker")?d["wpforms_"+r].datepicker:"undefined"!=typeof wpforms_datepicker?wpforms_datepicker:{disableMobile:!0}).hasOwnProperty("locale")&&"undefined"!=typeof wpforms_settings&&wpforms_settings.hasOwnProperty("locale")&&(i.locale=wpforms_settings.locale),i.wrap=!0,i.dateFormat=e.data("date-format"),1===e.data("disable-past-dates")&&(i.minDate="today",1===e.data("disable-todays-date"))&&(s=new Date,i.minDate=s.setDate(s.getDate()+1));let n=e.data("limit-days"),l=["sun","mon","tue","wed","thu","fri","sat"];n&&""!==n&&(n=n.split(","),i.disable=[function(e){for(var t in n)if(l.indexOf(n[t])===e.getDay())return!1;return!0}]),i.onChange=function(e,t,r){a.find(".wpforms-datepicker-clear").css("display",""===t?"none":"block")},a.flatpickr(i)})},loadTimePicker(e){void 0!==p.fn.timepicker&&(e=e?.length?e:p(s)).find(".wpforms-timepicker").each(function(){var e=p(this),t=e.closest(".wpforms-form").data("formid"),r=e.closest(".wpforms-field").data("field-id");let a;a=void 0!==d["wpforms_"+t+"_"+r]&&d["wpforms_"+t+"_"+r].hasOwnProperty("timepicker")?d["wpforms_"+t+"_"+r].timepicker:void 0!==d["wpforms_"+t]&&d["wpforms_"+t].hasOwnProperty("timepicker")?d["wpforms_"+t].timepicker:"undefined"!=typeof wpforms_timepicker?wpforms_timepicker:{scrollDefault:"now",forceRoundTime:!0};r=e.val();e.timepicker(a),r&&(e.val(r),e.trigger("changeTime"))})},loadInputMask(e){void 0!==p.fn.inputmask&&(e=e?.length?e:p(s)).find(".wpforms-masked-input").inputmask({rightAlign:!1})},fixPhoneFieldSnippets(t){console.warn("WARNING! Obsolete function called. Function wpforms.fixPhoneFieldSnippets( $field ) has been deprecated, please use the wpforms.repairSmartPhoneHiddenField( $field ) function instead!"),t.siblings('input[type="hidden"]').each(function(){var e;p(this).attr("name").includes("function")&&(e=(e=t.data("plugin_intlTelInput")).d||e.options)&&((d.intlTelInput?.getInstance(t[0]))?.destroy(),e.initialCountry=e.initialCountry.toLowerCase(),e.onlyCountries=e.onlyCountries.map(e=>e.toLowerCase()),e.preferredCountries=e.preferredCountries.map(e=>e.toLowerCase()),d.intlTelInput(t[0],e),t.siblings('input[type="hidden"]').each(function(){var e=p(this);e.attr("name",e.attr("name").replace("wpf-temp-",""))}))})},repairSmartPhoneHiddenField(e){console.warn('WARNING! Function "wpforms.repairSmartPhoneHiddenField()" has been deprecated, please use the new "WPFormsPhoneField.repairSmartHiddenField()" function instead!'),WPFormsPhoneField?.repairSmartHiddenField?.(e)},getDefaultSmartPhoneFieldOptions(){return console.warn('WARNING! Function "wpforms.getDefaultSmartPhoneFieldOptions()" has been deprecated, please use the new "WPFormsPhoneField.getDefaultSmartFieldOptions()" function instead!'),WPFormsPhoneField?.getDefaultSmartFieldOptions?.()},loadSmartPhoneField(e){console.warn('WARNING! Function "wpforms.loadSmartPhoneField()" has been deprecated, please use the new "WPFormsPhoneField.loadSmartField()" function instead!'),WPFormsPhoneField?.loadSmartField?.(e)},loadJqueryIntlTelInput(){console.warn('WARNING! Function "wpforms.loadJqueryIntlTelInput()" has been deprecated, please use the new "WPFormsPhoneField.loadJqueryIntlTelInput()" function instead!'),WPFormsPhoneField?.loadJqueryIntlTelInput?.()},initSmartPhoneField(e,t){console.warn('WARNING! Function "wpforms.initSmartPhoneField()" has been deprecated, please use the new "WPFormsPhoneField.initSmartField()" function instead!'),WPFormsPhoneField?.initSmartField?.(e,t)},bindSmartPhoneField(){console.warn('WARNING! Function "wpforms.bindSmartPhoneField()" has been deprecated, please use the new "WPFormsPhoneField.bindSmartField()" function instead!'),WPFormsPhoneField?.bindSmartField?.()},loadPayments(){p(".wpforms-payment-total").each(function(e,t){m.amountTotal(this)}),void 0!==p.fn.payment&&(p(".wpforms-field-credit-card-cardnumber").payment("formatCardNumber"),p(".wpforms-field-credit-card-cardcvc").payment("formatCardCVC"))},loadMailcheck(){wpforms_settings.mailcheck_enabled&&void 0!==p.fn.mailcheck&&(0<wpforms_settings.mailcheck_domains.length&&(Mailcheck.defaultDomains=Mailcheck.defaultDomains.concat(wpforms_settings.mailcheck_domains)),0<wpforms_settings.mailcheck_toplevel_domains.length&&(Mailcheck.defaultTopLevelDomains=Mailcheck.defaultTopLevelDomains.concat(wpforms_settings.mailcheck_toplevel_domains)),p(s).on("blur",".wpforms-field-email input",function(){let e=p(this),o=e.attr("id");e.mailcheck({suggested(e,t){t.full=t.full.replace(/%(?![0-9][0-9a-fA-F]+)/g,"%25"),t.address=t.address.replace(/%(?![0-9][0-9a-fA-F]+)/g,"%25"),t.domain=t.domain.replace(/%(?![0-9][0-9a-fA-F]+)/g,"%25"),t.address.match(/^xn--/)&&(t.full=punycode.toUnicode(decodeURI(t.full)),r=t.full.split("@"),t.address=r[0],t.domain=r[1]),t.domain.match(/^xn--/)&&(t.domain=punycode.toUnicode(decodeURI(t.domain)));var r=decodeURI(t.address).replaceAll(/[<>'"()/\\|:;=@%&\s]/gi,"").substr(0,64),a=decodeURI(t.domain).replaceAll(/[<>'"()/\\|:;=@%&+_\s]/gi,"");t='<a href="#" class="mailcheck-suggestion" data-id="'+o+'" title="'+wpforms_settings.val_email_suggestion_title+'">'+r+"@"+a+"</a>",t=wpforms_settings.val_email_suggestion.replace("{suggestion}",t),e.closest(".wpforms-field").find("#"+o+"_suggestion").remove(),e.parent().append('<label class="wpforms-error mailcheck-error" id="'+o+'_suggestion">'+t+"</label>")},empty(){p("#"+o+"_suggestion").remove()}})}),p(s).on("click",".wpforms-field-email .mailcheck-suggestion",function(e){var t=p(this),r=t.closest(".wpforms-field"),a=t.data("id");e.preventDefault(),r.find("#"+a).val(t.text()),t.parent().remove()}))},loadChoicesJS(i){"function"==typeof d.Choices&&(i=i?.length?i:p(s)).find(".wpforms-field-select-style-modern .choicesjs-select, .wpforms-field-payment-select .choicesjs-select").each(function(e,t){var r,a,o;p(t).data("choicesjs")||WPFormsUtils.triggerEvent(i,"wpformsBeforeLoadElementChoices",[t]).isDefaultPrevented()||(r=d.wpforms_choicesjs_config||{},a=p(t).data("search-enabled"),o=p(t).data("remove-items-enabled"),r.searchEnabled=void 0===a||a,r.removeItems=void 0===o||o,r.removeItemButton=r.removeItems,r.searchEnabled=void 0===a||a,r.allowHTML=!0,r.callbackOnInit=function(){let t=this,r=p(t.passedElement.element),a=p(t.input.element),e=r.data("size-class");r.removeAttr("hidden").addClass(t.config.classNames.input+"--hidden"),e&&p(t.containerOuter.element).addClass(e),r.prop("multiple")&&(a.data("placeholder",a.attr("placeholder")).css("width","auto"),t.getValue(!0).length&&a.removeAttr("placeholder"),a.css("width","1ch")),r.on("change",function(){r.prop("multiple")&&(t.getValue(!0).length?a.removeAttr("placeholder"):a.attr("placeholder",a.data("placeholder")).css("width","auto"));var e=r.closest("form").data("validator");e&&e.element(r)})},r.callbackOnCreateTemplates=function(){let r=p(this.passedElement.element);return{option(e){var t=Choices.defaults.templates.option.call(this,e);return void 0!==e.placeholder&&!0===e.placeholder&&t.classList.add("placeholder"),r.hasClass("wpforms-payment-price")&&null!=e.customProperties&&(t.dataset.amount=e.customProperties),t}}},p(t).data("choicesjs",new Choices(t,r)))})},bindChoicesJS(){p(s).on("click",".choices",function(e){var t=p(this),r=t.find("select").data("choicesjs");r&&t.hasClass("is-open")&&(e.target.classList.contains("choices__inner")||e.target.classList.contains("choices__arrow"))&&r.hideDropdown()})},bindUIActions(){var e=p(s);e.on("click",".wpforms-page-button",function(e){e.preventDefault(),m.pagebreakNav(this)});let t;e.on("change input",".wpforms-payment-price",function(){clearTimeout(t),t=setTimeout(()=>{m.amountTotal(this,!0)},0)});let r;e.on("change input","select.wpforms-payment-quantity",function(){clearTimeout(r),r=setTimeout(()=>{m.amountTotal(this,!0),m.updateOrderSummaryItemQuantity(p(this))},0)}),e.on("input",".wpforms-payment-user-input",function(){var e=p(this),t=e.val();e.val(t.replace(/[^0-9.,]/g,""))}),e.on("focusout",".wpforms-payment-user-input",function(){var e=p(this),t=e.val();if(!t)return t;t=m.amountSanitize(t),t=m.amountFormat(t);e.val(t)});let a;e.on("wpformsProcessConditionals",function(e,t){clearTimeout(a),a=setTimeout(()=>{m.amountTotal(t,!0)},0)}),e.on("mouseenter",".wpforms-field-rating-item",function(){p(this).parent().find(".wpforms-field-rating-item").removeClass("selected hover"),p(this).prevAll().addBack().addClass("hover")}).on("mouseleave",".wpforms-field-rating-item",function(){p(this).parent().find(".wpforms-field-rating-item").removeClass("selected hover"),p(this).parent().find("input:checked").parent().prevAll().addBack().addClass("selected")}),p(s).on("change",".wpforms-field-rating-item input",function(){var e=p(this),t=e.closest(".wpforms-field-rating-items").find(".wpforms-field-rating-item");e.focus(),t.removeClass("hover selected"),e.parent().prevAll().addBack().addClass("selected")}),p(function(){p(".wpforms-field-rating-item input:checked").trigger("change")}),e.on("keydown",".wpforms-image-choices-item label",function(e){var t=p(this);if(t.closest(".wpforms-field").hasClass("wpforms-conditional-hide"))return e.preventDefault(),!1;32===e.keyCode&&(t.find("input").trigger("click"),e.preventDefault())}),d.document.documentMode&&e.on("click",".wpforms-image-choices-item img",function(){p(this).closest("label").find("input").trigger("click")}),e.on("change",".wpforms-field-checkbox input, .wpforms-field-radio input, .wpforms-field-payment-multiple input, .wpforms-field-payment-checkbox input, .wpforms-field-gdpr-checkbox input",function(e){var t=p(this);if(t.closest(".wpforms-field").hasClass("wpforms-conditional-hide"))return e.preventDefault(),!1;switch(t.attr("type")){case"radio":t.closest("ul").find("li").removeClass("wpforms-selected").find("input[type=radio]").removeProp("checked"),t.prop("checked",!0).closest("li").addClass("wpforms-selected");break;case"checkbox":t.is(":checked")?(t.closest("li").addClass("wpforms-selected"),t.prop("checked",!0)):(t.closest("li").removeClass("wpforms-selected"),t.prop("checked",!1))}}),e.on("input",".wpforms-field-file-upload",function(){var e=p(this),t=e.closest("form.wpforms-form").find('.wpforms-field-file-upload input:not(".dropzone-input")');let a=0,r=Number(wpforms_settings.post_max_size),o='<div class="wpforms-error-container-post_max_size">'+wpforms_settings.val_post_max_size+"</div>";e=e.closest("form.wpforms-form").find(".wpforms-submit-container");let i=e.find("button.wpforms-submit"),s=e.prev();var n=i.closest("form"),l=n.find(".wpforms-page-next:visible");0!==n.find(".wpforms-page-indicator").length&&0!==l.length&&(i=l),t.each(function(){var e=p(this);let t=0;for(var r=e[0].files.length;t<r;t++)a+=e[0].files[t].size}),a<r?(s.find(".wpforms-error-container-post_max_size").remove(),i.prop("disabled",!1),WPFormsUtils.triggerEvent(n,"wpformsFormSubmitButtonRestore",[n,i]),WPFormsUtils.triggerEvent(n,"wpformsCombinedUploadsSizeOk",[n,s])):(a=Number((a/1048576).toFixed(3)),r=Number((r/1048576).toFixed(3)),o=o.replace(/{totalSize}/,a).replace(/{maxSize}/,r),s.hasClass("wpforms-error-container")?(s.find(".wpforms-error-container-post_max_size").remove(),s.append(o)):(e.before('<div class="wpforms-error-container">{errorMsg}</div>'.replace(/{errorMsg}/,o)),s=e.prev()),i.prop("disabled",!0),WPFormsUtils.triggerEvent(n,"wpformsFormSubmitButtonDisable",[n,i]),WPFormsUtils.triggerEvent(n,"wpformsCombinedUploadsSizeError",[n,s]))}),e.on("change input",".wpforms-field-number-slider input[type=range]",function(e){var t=p(e.target).siblings(".wpforms-field-number-slider-hint");t.html(t.data("hint").replaceAll("{value}","<b>"+e.target.value+"</b>"))}),e.on("keydown",".wpforms-form input",function(e){var t,r;13!==e.keyCode||0===(r=(t=p(this)).closest(".wpforms-page")).length||["text","tel","number","email","url","radio","checkbox"].indexOf(t.attr("type"))<0||(t.hasClass("wpforms-datepicker")&&t.flatpickr("close"),e.preventDefault(),(r.hasClass("last")?r.closest(".wpforms-form").find(".wpforms-submit"):r.find(".wpforms-page-next")).trigger("click"))}),e.on("keypress",".wpforms-field-number input",function(e){return/^[-0-9.]+$/.test(String.fromCharCode(e.keyCode||e.which))}),e.one("input",".wpforms-field input, .wpforms-field textarea, .wpforms-field select",m.formChanged).one("change",".wpforms-field-select-style-modern, .wpforms-timepicker",m.formChanged).one("focus",".dropzone-input",m.formChanged).one("click touchstart",".wpforms-signature-canvas",m.formChanged).one("wpformsRichTextContentChange",m.richTextContentChanged),p("form.wpforms-form").on("wpformsBeforePageChange",m.skipEmptyPages)},skipEmptyPages(e,t,r,a){var o=m.findNonEmptyPage(t,r,a);o!==t&&(e.preventDefault(),1===o&&"prev"===a?(e=r.find(".wpforms-page-2"),a=r.find(".wpforms-page-"+t),t=(e.find(".wpforms-page-prev").length?e:a).find(".wpforms-page-prev"),wpforms.navigateToPage(t,"prev",2,r,e)):(t=r.find(".wpforms-page-"+(a=o-1)),wpforms.navigateToPage(t.find(".wpforms-page-next"),"next",a,r,t)))},findNonEmptyPage(e,t,r){let a=e;for(;m.isEmptyPage(t,a);)"prev"===r?a--:a++;return a},isEmptyPage(e,t){return 1!==t&&!(e=e.find(".wpforms-page-"+t)).hasClass("last")&&(t=e.find(".wpforms-field:not(.wpforms-field-pagebreak):not(.wpforms-field-hidden)"),e.find(".wpforms-conditional-hide").length===t.length)},formChanged(e){var t=p(this).closest(".wpforms-form");m.maybeSetStartTime(t)},richTextContentChanged(e,t,r){r=r.getContainer(),r=p(r).closest(".wpforms-form");m.maybeSetStartTime(r)},initFormsStartTime(){p(".wpforms-form").each(function(){m.maybeSetStartTime(p(this))})},maybeSetStartTime(e){m.getStartTimestampData(e)||e.data("start_timestamp",m.getTimestampSec())},getStartTimestampData(e){return e.hasClass("wpforms-form")&&0<(e=parseInt(e.data("start_timestamp"),10)||0)?e:0},entryPreviewFieldPageChange(e,t,r){console.warn("WARNING! Obsolete function called. Function wpforms.entryPreviewFieldPageChange has been deprecated, please use the WPFormsEntryPreview.pageChange function instead!"),WPFormsEntryPreview.pageChange(e,t,r)},entryPreviewFieldUpdate(e,t){console.warn("WARNING! Obsolete function called. Function wpforms.entryPreviewFieldUpdate has been deprecated, please use the WPFormsEntryPreview.update function instead!"),WPFormsEntryPreview.update(e,t)},scrollToError(e){if(0!==e.length){let t=e.find(".wpforms-field.wpforms-has-error");0!==(t=0===t.length?e.closest(".wpforms-field"):t).length&&void 0!==(e=t.offset())&&m.animateScrollTop(e.top-75,750).done(function(){var e=t.find(".wpforms-error").first();"function"==typeof e.focus&&e.trigger("focus")})}},pagebreakNav(e){let t=p(e),r=t.data("action"),a=t.data("page"),o=t.closest(".wpforms-form"),i=o.find(".wpforms-page-"+a);m.saveTinyMCE(),"next"===r&&void 0!==p.fn.validate?m.checkForInvalidFields(o,i,function(){m.navigateToPage(t,r,a,o,i)}):"prev"!==r&&"next"!==r||m.navigateToPage(t,r,a,o,i)},checkForInvalidFields(e,t,r){var o=e.data("validator");if(o){let a=!0;t.find(":input").each(function(e,t){var r=p(t);!r.attr("name")||r.hasClass("wpforms-field-skip-validation")||p(t).valid()||(a=!1)}),0<o.pendingRequest?setTimeout(function(){m.checkForInvalidFields(e,t,r)},800):a?r():m.scrollToError(t)}},navigateToPage(t,r,a,o,e){if(!t.hasClass("wpforms-disabled")){let e=a;"next"===r?e+=1:"prev"===r&&--e,WPFormsUtils.triggerEvent(t,"wpformsBeforePageChange",[e,o,r]).isDefaultPrevented()||(o.find(".wpforms-page").hide(),(a=o.find(".wpforms-page-"+e)).show(),m.toggleReCaptchaAndSubmitDisplay(o,r,a),m.checkTurnstileVisibility(o),(a=m.getPageScroll(o))&&m.animateScrollTop(o.offset().top-a,750,null),t.trigger("wpformsPageChange",[e,o,r]),m.manipulateIndicator(e,o))}},toggleReCaptchaAndSubmitDisplay(e,t,r){var a=e.find(".wpforms-submit-container"),e=e.find(".wpforms-recaptcha-container");"next"===t&&r.hasClass("last")?(e.show(),a.show()):"prev"===t&&(e.hide(),a.hide())},checkTurnstileVisibility(e){var t,e=e.find(".wpforms-recaptcha-container");e.hasClass("wpforms-is-turnstile")&&(t=e.find(".g-recaptcha").height(),0===parseInt(t,10)?e.addClass("wpforms-is-turnstile-invisible"):e.removeClass("wpforms-is-turnstile-invisible"))},getPageScroll(e){return!1!==d.wpforms_pageScroll&&(m.empty(d.wpform_pageScroll)?0!==e.find(".wpforms-page-indicator").data("scroll")&&75:d.wpform_pageScroll)},manipulateIndicator(e,t){var r,a=t.find(".wpforms-page-indicator");a&&("connector"===(r=a.data("indicator"))||"circles"===r?m.manipulateConnectorAndCirclesIndicator(a,r,e):"progress"===r&&m.manipulateProgressIndicator(a,t,e))},manipulateConnectorAndCirclesIndicator(e,t,r){var a=e.data("indicator-color");e.find(".wpforms-page-indicator-page").removeClass("active"),e.find(".wpforms-page-indicator-page-"+r).addClass("active"),e.find(".wpforms-page-indicator-page-number").removeAttr("style"),e.find(".active .wpforms-page-indicator-page-number").css("background-color",a),"connector"===t&&(e.find(".wpforms-page-indicator-page-triangle").removeAttr("style"),e.find(".active .wpforms-page-indicator-page-triangle").css("border-top-color",a))},manipulateProgressIndicator(e,t,r){var a=e.find(".wpforms-page-indicator-page-title"),o=e.find(".wpforms-page-indicator-page-title-sep"),t=r/t.find(".wpforms-page").length*100;e.find(".wpforms-page-indicator-page-progress").css("width",t+"%"),e.find(".wpforms-page-indicator-steps-current").text(r),a.data("page-"+r+"-title")?(a.css("display","inline").text(a.data("page-"+r+"-title")),o.css("display","inline")):(a.css("display","none"),o.css("display","none"))},bindOptinMonster(){s.addEventListener("om.Campaign.load",function(e){m.ready(),m.optinMonsterRecaptchaReset(e.detail.Campaign.data.id)}),s.addEventListener("om.Campaign.afterShow",function(e){"undefined"!=typeof WPFormsRepeaterField&&WPFormsRepeaterField.ready()}),p(s).on("OptinMonsterOnShow",function(e,t,r){m.ready(),m.optinMonsterRecaptchaReset(t.optin),"undefined"!=typeof WPFormsRepeaterField&&WPFormsRepeaterField.ready()})},optinMonsterRecaptchaReset(e){var e=p("#om-"+e).find(".wpforms-form"),a=e.find(".wpforms-recaptcha-container"),o=e.find(".g-recaptcha");if(e.length&&o.length){let e=o.attr("data-sitekey"),t="recaptcha-"+Date.now(),r=a.hasClass("wpforms-is-hcaptcha")?hcaptcha:grecaptcha;o.remove(),a.prepend('<div class="g-recaptcha" id="'+t+'" data-sitekey="'+e+'"></div>'),r.render(t,{sitekey:e,callback(){wpformsRecaptchaCallback(p("#"+t))}})}},amountTotal(e,t){t=t||!1;let r=p(e),a=r.closest(".wpforms-form"),o=m.amountTotalCalc(a);if(m.allowAmountTotalCalc(a,r,o)){let e=m.amountFormatSymbol(o);a.find(".wpforms-payment-total").each(function(){"hidden"===p(this).attr("type")||"text"===p(this).attr("type")?(p(this).val(e),"text"===p(this).attr("type")&&t&&a.data("validator")&&p(this).valid()):p(this).text(e)}),m.updateOrderSummaryItems(a,r,e)}},allowAmountTotalCalc(e,t,r){var e=e.data("formid");return m.getCache(e,"amountTotal")!==r?(m.updateCache(e,"amountTotal",r),!0):"radio"===(e=t.prop("type"))||"select-one"===e||"checkbox"===e},updateOrderSummaryItems(t,e,r){t.find(".wpforms-order-summary-preview").each(function(){let e=p(this);""!==r&&e.find(".wpforms-order-summary-preview-total .wpforms-order-summary-item-price").text(r),t.find(".wpforms-payment-price").each(function(){m.updateOrderSummaryItem(p(this),e)})})},updateCache(e,t,r){m.cache[e]=m.cache[e]||{},m.cache[e][t]=r},getCache(e,t){return!(!Object.prototype.hasOwnProperty.call(m.cache,e)||!Object.prototype.hasOwnProperty.call(m.cache[e],t))&&m.cache[e][t]},updateOrderSummaryItem(o,i){if(o.hasClass("wpforms-payment-price")){let t=o.closest(".wpforms-field"),e=t.data("field-id"),r=o.prop("type"),a="block"===t.css("display");var s,n;"checkbox"===r||"radio"===r||"select-one"===r?i.find(`tr[data-field="${e}"]`).each(function(){var e=p(this).data("choice"),e="select-one"===r?e===parseInt(t.find("select").val(),10):t.find(`input[value="${e}"]`).is(":checked");p(this).toggle(a&&e)}):(s=i.find(`tr[data-field="${e}"]`),n=o.val(),s.find(".wpforms-order-summary-item-price").text(m.amountFormatSymbol(m.amountSanitize(n))),s.toggle(a)),t.hasClass("wpforms-payment-quantities-enabled")?m.updateOrderSummaryItemQuantity(o):(m.updateSummaryPriceWidth(i),m.toggleSummaryPlaceholder(i))}},updateOrderSummaryItemQuantity(e){let t=e.closest(".wpforms-field"),a=t.find("input.wpforms-payment-price, select.wpforms-payment-price"),r=e.closest(".wpforms-form"),o=t.data("field-id"),i=m.getPaymentFieldQuantity(a),s=m.getPaymentFieldAmount(a),n=a.prop("type");r.find(".wpforms-order-summary-preview").each(function(){var e,t=p(this);let r;(r="checkbox"===n||"radio"===n||"select-one"===n?(e=a.val(),t.find(`tr[data-field="${o}"][data-choice="${e}"]`)):t.find(`tr[data-field="${o}"]`)).toggle(0<i),r.find(".wpforms-order-summary-item-quantity").text(i),r.find(".wpforms-order-summary-item-price").text(m.amountFormatSymbol(s*i)),m.updateSummaryPriceWidth(t),m.toggleSummaryPlaceholder(t)})},updateSummaryPriceWidth(e){var t=Math.max(e.find(".wpforms-order-summary-preview-coupon-total .wpforms-order-summary-item-price").text().length,e.find(".wpforms-order-summary-preview-total .wpforms-order-summary-item-price").text().length+3);e.find(".wpforms-order-summary-item-price").css("width",t+"ch")},toggleSummaryPlaceholder(e){var t=e.find(".wpforms-order-summary-placeholder");let r=!0;e.find(".wpforms-order-summary-field").each(function(){if("none"!==p(this).css("display"))return r=!1}),t.toggle(r)},amountTotalCalc(e){let r=0;p(".wpforms-payment-price",e).each(function(){var e,t=p(this);t.closest(".wpforms-field-payment-single").hasClass("wpforms-conditional-hide")||(e=m.getPaymentFieldAmount(t))&&(r=Number(r)+e*m.getPaymentFieldQuantity(t))});var t=p(s),a=WPFormsUtils.triggerEvent(t,"wpformsAmountTotalCalculate",[e,r]);return r=void 0!==a.result&&0<=a.result?a.result:r,WPFormsUtils.triggerEvent(t,"wpformsAmountTotalCalculated",[e,r]),r},getPaymentFieldAmount(e){var t=e.attr("type");return"text"===t||"hidden"===t?Number(m.amountSanitize(e.val())):"radio"!==t&&"checkbox"!==t||!e.is(":checked")?e.is("select")&&0<e.find("option:selected").length&&e.find("option:selected").data("amount")?Number(m.amountSanitize(e.find("option:selected").data("amount"))):0:Number(m.amountSanitize(e.data("amount")))},getPaymentFieldQuantity(e){e=e.attr("id"),e=p(`#${e}-quantity`);return e.length?Number(e.val()):1},amountSanitize(e){var t=m.getCurrency();return e=e.toString().replace(t.symbol,"").replace(/[^0-9.,]/g,""),","===t.decimal_sep?("."===t.thousands_sep&&-1!==e.indexOf(t.thousands_sep)?e=e.replace(new RegExp("\\"+t.thousands_sep,"g"),""):""===t.thousands_sep&&-1!==e.indexOf(".")&&(e=e.replace(/\./g,"")),e=e.replace(t.decimal_sep,".")):","===t.thousands_sep&&-1!==e.indexOf(t.thousands_sep)&&(e=e.replace(new RegExp("\\"+t.thousands_sep,"g"),"")),m.numberFormat(e,t.decimals,".","")},amountFormat(e){var t,r=m.getCurrency();return e=String(e),","===r.decimal_sep&&-1!==e.indexOf(r.decimal_sep)&&(t=e.indexOf(r.decimal_sep),e=e.substr(0,t)+"."+e.substr(t+1,e.length-1)),","===r.thousands_sep&&-1!==e.indexOf(r.thousands_sep)&&(e=e.replace(/,/g,"")),m.empty(e)&&(e=0),m.numberFormat(e,r.decimals,r.decimal_sep,r.thousands_sep)},amountFormatSymbol(e){var t=m.getCurrency(),e=m.amountFormat(e);return"left"===t.symbol_pos?t.symbol+e:e+" "+t.symbol},getCurrency(){var e={code:"USD",thousands_sep:",",decimals:2,decimal_sep:".",symbol:"$",symbol_pos:"left"};return void 0!==wpforms_settings.currency_code&&(e.code=wpforms_settings.currency_code),void 0!==wpforms_settings.currency_thousands&&(e.thousands_sep=wpforms_settings.currency_thousands),void 0!==wpforms_settings.currency_decimals&&(e.decimals=wpforms_settings.currency_decimals),void 0!==wpforms_settings.currency_decimal&&(e.decimal_sep=wpforms_settings.currency_decimal),void 0!==wpforms_settings.currency_symbol&&(e.symbol=wpforms_settings.currency_symbol),void 0!==wpforms_settings.currency_symbol_pos&&(e.symbol_pos=wpforms_settings.currency_symbol_pos),e},numberFormat(e,t,r,a){e=(e+"").replace(/[^0-9+\-Ee.]/g,"");var o,i,e=isFinite(+e)?+e:0,t=isFinite(+t)?Math.abs(t):0,a=void 0===a?",":a,r=void 0===r?".":r,s=(t?(s=e,o=t,i=Math.pow(10,o),""+(Math.round(s*i)/i).toFixed(o)):""+Math.round(e)).split(".");return 3<s[0].length&&(s[0]=s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g,a)),(s[1]||"").length<t&&(s[1]=s[1]||"",s[1]+=new Array(t-s[1].length+1).join("0")),s.join(r)},empty(e){var t;let r,a;var o=[void 0,null,!1,0,"","0"];for(a=0,t=o.length;a<t;a++)if(e===o[a])return!0;if("object"!=typeof e)return!1;for(r in e)if(e.hasOwnProperty(r))return!1;return!0},setUserIdentifier(){if((!d.hasRequiredConsent&&"undefined"!=typeof wpforms_settings&&wpforms_settings.uuid_cookie||d.hasRequiredConsent&&d.hasRequiredConsent())&&!m.getCookie("_wpfuuid")){var t=new Array(36),r="0123456789abcdef";for(let e=0;e<36;e++)t[e]=r.substr(Math.floor(16*Math.random()),1);t[14]="4",t[19]=r.substr(3&t[19]|8,1),t[8]=t[13]=t[18]=t[23]="-";var e=t.join("");m.createCookie("_wpfuuid",e,3999)}},createCookie(e,t,r){let a="",o="";var i;wpforms_settings.is_ssl&&(o=";secure"),a=r?-1===r?"":((i=new Date).setTime(i.getTime()+24*r*60*60*1e3),";expires="+i.toGMTString()):";expires=Thu, 01 Jan 1970 00:00:01 GMT",s.cookie=e+"="+t+a+";path=/;samesite=strict"+o},getCookie(e){var r=e+"=",a=s.cookie.split(";");for(let t=0;t<a.length;t++){let e=a[t];for(;" "===e.charAt(0);)e=e.substring(1,e.length);if(0===e.indexOf(r))return e.substring(r.length,e.length)}return null},removeCookie(e){m.createCookie(e,"",-1)},getFirstBrowserLanguage(){return console.warn('WARNING! Function "wpforms.getFirstBrowserLanguage()" has been deprecated, please use the new "WPFormsPhoneField.getFirstBrowserLanguage()" function instead!'),WPFormsPhoneField?.getFirstBrowserLanguage?.()},mapLanguageToIso(e){return console.warn('WARNING! Function "wpforms.mapLanguageToIso()" has been deprecated, please use the new "WPFormsPhoneField.mapLanguageToIso()" function instead!'),WPFormsPhoneField?.mapLanguageToIso?.(e)},currentIpToCountry(e){console.warn('WARNING! Function "wpforms.currentIpToCountry()" has been deprecated, please use the new "WPFormsPhoneField.currentIpToCountry()" function instead!'),WPFormsPhoneField?.currentIpToCountry?.(e)},formSubmit(e){e instanceof jQuery||(e=p(e)),m.saveTinyMCE(),WPFormsUtils.triggerEvent(e,"wpformsBeforeFormSubmit",[e]).isDefaultPrevented()?m.restoreSubmitButton(e,e.closest(".wpforms-container")):e.hasClass("wpforms-ajax-form")&&"undefined"!=typeof FormData?m.formSubmitAjax(e):m.formSubmitNormal(e)},restoreSubmitButton(e,t){var r=e.find(".wpforms-submit"),a=r.data("submit-text");a&&r.text(a),r.prop("disabled",!1),WPFormsUtils.triggerEvent(e,"wpformsFormSubmitButtonRestore",[e,r]),t.css("opacity",""),e.find(".wpforms-submit-spinner").hide()},formSubmitNormal(e){var t,r;e.length&&(r=(t=e.find(".wpforms-submit")).get(0).recaptchaID,m.empty(r)&&0!==r||(t.get(0).recaptchaID=!1),e.append('<input type="hidden" name="start_timestamp" value="'+m.getStartTimestampData(e)+'">'),e.append('<input type="hidden" name="end_timestamp" value="'+m.getTimestampSec()+'">'),e.get(0).submit())},formHasCaptcha(e){return!(!e||!e.length||"undefined"==typeof hcaptcha&&"undefined"==typeof grecaptcha&&"undefined"==typeof turnstile)&&(e=e.find(".wpforms-recaptcha-container"),Boolean(e.length))},resetFormRecaptcha(r){if(m.formHasCaptcha(r)){var a=r.find(".wpforms-recaptcha-container");let e,t;e=a.hasClass("wpforms-is-hcaptcha")?hcaptcha:a.hasClass("wpforms-is-turnstile")?turnstile:grecaptcha,t=r.find(".wpforms-submit").get(0).recaptchaID,m.empty(t)&&0!==t&&(t=r.find(".g-recaptcha").data("recaptcha-id")),m.empty(t)&&0!==t||e.reset(t)}},consoleLogAjaxError(e){e?console.error("WPForms AJAX submit error:\n%s",e):console.error("WPForms AJAX submit error")},displayFormAjaxErrors(e,t){"string"==typeof t?m.displayFormAjaxGeneralErrors(e,t):(t=t&&"errors"in t?t.errors:null,m.empty(t)||m.empty(t.general)&&m.empty(t.field)?m.consoleLogAjaxError():(m.empty(t.general)||m.displayFormAjaxGeneralErrors(e,t.general),m.empty(t.field)||m.displayFormAjaxFieldErrors(e,t.field)))},displayFormAjaxGeneralErrors(e,t){var r,a;e&&e.length&&(m.empty(t)||(m.isModernMarkupEnabled()&&e.attr({"aria-invalid":"true","aria-errormessage":""}),"string"==typeof t?(a=m.isModernMarkupEnabled()?' role="alert"':"",r=m.isModernMarkupEnabled()?`<span class="wpforms-hidden">${wpforms_settings.formErrorMessagePrefix}</span>`:"",e.find(".wpforms-submit-container").before(`<div class="wpforms-error-container"${a}>${r}${t}</div>`),m.setCurrentPage(e,{})):(a=e.data("formid"),m.printGeneralErrors(e,t,a))))},printGeneralErrors(o,e,i){p.each(e,function(e,t){switch(e){case"header":case"header_styled":o.prepend(t);break;case"footer":case"footer_styled":r=t,0===o.find(".wpforms-page-indicator").length?o.find(".wpforms-submit-container").before(r):o.find(".wpforms-page-1").append(r);break;case"recaptcha":r=t,o.find(".wpforms-recaptcha-container").append(r)}var r,a;m.isModernMarkupEnabled()&&(a=o.attr("aria-errormessage")||"",o.attr("aria-errormessage",a+` wpforms-${i}-${e}-error`))}),o.find(".wpforms-error-container").length&&m.animateScrollTop(o.find(".wpforms-error-container").first().offset().top-100)},clearFormAjaxGeneralErrors(e){e.find(".wpforms-error-container").remove(),e.find("#wpforms-field_recaptcha-error").remove(),m.isModernMarkupEnabled()&&e.attr({"aria-invalid":"false","aria-errormessage":""})},displayFormAjaxFieldErrors(a,o){var e;a&&a.length&&(m.empty(o)||(e=a.data("validator"))&&(o=m.splitFieldErrors(o),p.each(o,function(e,t){var r=p('[name="'+e+'"]',a);r.length?r.attr("data-server-error",t):delete o[e]}),e.showErrors(o),m.formHasCaptcha(a)||e.focusInvalid()))},splitFieldErrors:i=>(p.each(i,function(o,e){"string"!=typeof e&&p.each(e,function(e,t){var r=o.split("[").pop().replace("]",""),a=o.replace("["+r+"]","");r===e?i[o]=t:"string"==typeof e&&isNaN(e)&&(i[a+"["+e+"]"]=t)})}),i),formSubmitAjax:a=>{if(!a.length)return p.Deferred().reject();let r=a.closest(".wpforms-container"),e=a.find(".wpforms-submit-spinner"),o;r.css("opacity",.6),e.show(),m.clearFormAjaxGeneralErrors(a);var t=new FormData(a.get(0)),t=(t.append("action","wpforms_submit"),t.append("start_timestamp",m.getStartTimestampData(a)),t.append("end_timestamp",m.getTimestampSec()),{type:"post",dataType:"json",url:wpforms_settings.ajaxurl,data:t,cache:!1,contentType:!1,processData:!1});return t.success=function(e){var t;if(e)if(e.data&&e.data.action_required)a.trigger("wpformsAjaxSubmitActionRequired",e);else if(e.success){if(a.trigger("wpformsAjaxSubmitSuccess",e),e.data)return e.data.redirect_url?(t=e.data.new_tab||!1,a.trigger("wpformsAjaxSubmitBeforeRedirect",e),t?(d.open(e.data.redirect_url,"_blank"),void location.reload()):void(d.location=e.data.redirect_url)):void(e.data.confirmation&&(r.html(e.data.confirmation),o=r.find("div.wpforms-confirmation-scroll"),r.trigger("wpformsAjaxSubmitSuccessConfirmation",e),o.length)&&m.animateScrollTop(o.offset().top-100))}else m.resetFormRecaptcha(a),m.displayFormAjaxErrors(a,e.data),a.trigger("wpformsAjaxSubmitFailed",e),m.setCurrentPage(a,e.data);else m.consoleLogAjaxError()},t.error=function(e,t,r){m.consoleLogAjaxError(r),a.trigger("wpformsAjaxSubmitError",[e,t,r])},t.complete=function(e,t){e.responseJSON&&e.responseJSON.data&&(e.responseJSON.data.action_required||"success"===t&&e.responseJSON.data.redirect_url)||(m.restoreSubmitButton(a,r),a.trigger("wpformsAjaxSubmitCompleted",[e,t]))},WPFormsUtils.triggerEvent(a,"wpformsAjaxBeforeSubmit",[a]).isDefaultPrevented()?(m.restoreSubmitButton(a,r),p.Deferred().reject()):p.ajax(t)},setCurrentPage(a,o){if(0!==a.find(".wpforms-page-indicator").length){let r=[];if(a.find(".wpforms-page").each(function(e,t){if(1<=p(t).find(".wpforms-has-error").length)return r.push(p(t))}),0!==r.length||void 0===o.errors||void 0===o.errors.general||void 0!==o.errors.general.footer||void 0===o.errors.general.recaptcha){var i=0<r.length?r[0]:a.find(".wpforms-page-1");let e,t="prev";1===i.data("page")||void 0!==o.errors&&void 0!==o.errors.general.footer?e=a.find(".wpforms-page-1").next():(e=0!==i.next().length?i.next():i.prev(),t=0!==i.next().length?"prev":"next");o=e.find(".wpforms-page-next"),i=e.data("page");m.navigateToPage(o,t,i,a,p(".wpforms-page-"+i))}}},animateScrollTop(e,t,r){return t=t||1e3,r="function"==typeof r?r:function(){},p("html, body").animate({scrollTop:parseInt(e,10)},{duration:t,complete:r}).promise()},saveTinyMCE(){"undefined"!=typeof tinyMCE&&tinyMCE.triggerSave()},isFunction(e){return!!(e&&e.constructor&&e.call&&e.apply)},compareTimesGreaterThan(e,t){e=e.replace(/(am|pm)/g," $1").toUpperCase(),t=t.replace(/(am|pm)/g," $1").toUpperCase();e=Date.parse("01 Jan 2021 "+e);return Date.parse("01 Jan 2021 "+t)<=e},isModernMarkupEnabled(){return!!wpforms_settings.isModernMarkupEnabled},initTokenUpdater(){p(".wpforms-form").on("focusin",function(e){var t=p(e.target.closest("form")),r=Date.now();this.needsTokenUpdate(r,t)&&this.updateToken(r,t,e)}.bind(this))},needsTokenUpdate(e,t){return e-1e3*(t.attr("data-token-time")||0)>=1e3*wpforms_settings.token_cache_lifetime&&!this.isUpdatingToken},updateToken(t,r,a){var e=r.data("formid");let o=r.find(".wpforms-submit");this.isUpdatingToken=!0,o.prop("disabled",!0),p.post(wpforms_settings.ajaxurl,{action:"wpforms_get_token",formId:e}).done(function(e){e.success?(r.attr("data-token-time",t),r.attr("data-token",e.data.token),o.prop("disabled",!1),a.target===o[0]&&o.trigger("click")):console.error("Failed to update token: ",e)}).fail(function(e,t,r){console.error("AJAX request failed: ",t,r)}).always(function(){this.isUpdatingToken=!1,o.prop("disabled",!1)}.bind(this))},restoreSubmitButtonOnEventPersisted(){d.onpageshow=function(e){e.persisted&&p(".wpforms-form").each(function(){var e=p(this);m.restoreSubmitButton(e,e.closest(".wpforms-container"))})}},loadValidationGroups(e){var t=e.closest(".wpforms-form").data("validator");t&&p.extend(t.groups,m.getDateTimeValidationGroups(e))},getDateTimeValidationGroups(e){let a={};return e.find(".wpforms-field.wpforms-field-date-time").each(function(){var e=p(this);if(e.find(".wpforms-field-date-dropdown-wrap").length){let r=e.attr("id").replace("-container","");p.each(["month","day","year"],function(e,t){t=p(`#${r}-`+t).attr("name");a[t]=r})}}),a},getTimestampSec(){return Math.floor(Date.now()/1e3)}};return m})(document,window,jQuery);wpforms.init();