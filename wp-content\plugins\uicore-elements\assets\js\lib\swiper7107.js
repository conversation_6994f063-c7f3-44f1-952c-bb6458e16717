"use strict";function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var a=0;a<t.length;a++){var r=t[a];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_toPropertyKey(r.key),r)}}function _createClass(e,t,a){return t&&_defineProperties(e.prototype,t),a&&_defineProperties(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function ownKeys(t,e){var a,r=Object.keys(t);return Object.getOwnPropertySymbols&&(a=Object.getOwnPropertySymbols(t),e&&(a=a.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,a)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(a),!0).forEach(function(e){_defineProperty(t,e,a[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):ownKeys(Object(a)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))})}return t}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var r,i,s,n,o=[],l=!0,d=!1;try{if(s=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(r=s.call(a)).done)&&(o.push(r.value),o.length!==t);l=!0);}catch(e){d=!0,i=e}finally{try{if(!l&&null!=a.return&&(n=a.return(),Object(n)!==n))return}finally{if(d)throw i}}return o}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var a;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(a="Object"===(a={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}function _defineProperty(e,t,a){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var a=e[Symbol.toPrimitive];if(void 0===a)return("string"===t?String:Number)(e);a=a.call(e,t||"default");if("object"!=_typeof(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}var UiCoreSwiper=function(){function y(e){return null!==e&&"object"==_typeof(e)&&"constructor"in e&&e.constructor===Object}function r(t,a){void 0===t&&(t={}),void 0===a&&(a={}),Object.keys(a).forEach(function(e){void 0===t[e]?t[e]=a[e]:y(a[e])&&y(t[e])&&0<Object.keys(a[e]).length&&r(t[e],a[e])})}var t={body:{},addEventListener:function(){},removeEventListener:function(){},activeElement:{blur:function(){},nodeName:""},querySelector:function(){return null},querySelectorAll:function(){return[]},getElementById:function(){return null},createEvent:function(){return{initEvent:function(){}}},createElement:function(){return{children:[],childNodes:[],style:{},setAttribute:function(){},getElementsByTagName:function(){return[]}}},createElementNS:function(){return{}},importNode:function(){return null},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function I(){var e="undefined"!=typeof document?document:{};return r(e,t),e}var a,p,h,w={document:t,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState:function(){},pushState:function(){},go:function(){},back:function(){}},CustomEvent:function(){return this},addEventListener:function(){},removeEventListener:function(){},getComputedStyle:function(){return{getPropertyValue:function(){return""}}},Image:function(){},Date:function(){},screen:{},setTimeout:function(){},clearTimeout:function(){},matchMedia:function(){return{}},requestAnimationFrame:function(e){return"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0)},cancelAnimationFrame:function(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function G(){var e="undefined"!=typeof window?window:{};return r(e,w),e}function T(e){return(e=void 0===e?"":e).trim().split(" ").filter(function(e){return!!e.trim()})}function M(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function b(){return Date.now()}function H(e,t){void 0===t&&(t="x");var a,r,i,s,n=G(),o=(e=e,o=G(),s=(s=!(s=o.getComputedStyle?o.getComputedStyle(e,null):s)&&e.currentStyle?e.currentStyle:s)||e.style);return n.WebKitCSSMatrix?(6<(r=o.transform||o.webkitTransform).split(",").length&&(r=r.split(", ").map(function(e){return e.replace(",",".")}).join(", ")),i=new n.WebKitCSSMatrix("none"===r?"":r)):a=(i=o.MozTransform||o.OTransform||o.MsTransform||o.msTransform||o.transform||o.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(r=n.WebKitCSSMatrix?i.m41:16===a.length?parseFloat(a[12]):parseFloat(a[4])),(r="y"===t?n.WebKitCSSMatrix?i.m42:16===a.length?parseFloat(a[13]):parseFloat(a[5]):r)||0}function m(e){return"object"==_typeof(e)&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function v(e){for(var t,a=Object(arguments.length<=0?void 0:e),r=["__proto__","constructor","prototype"],i=1;i<arguments.length;i+=1){var s=i<0||arguments.length<=i?void 0:arguments[i];if(null!=s&&(t=s,!("undefined"!=typeof window&&void 0!==window.HTMLElement?t instanceof HTMLElement:t&&(1===t.nodeType||11===t.nodeType))))for(var n=Object.keys(Object(s)).filter(function(e){return r.indexOf(e)<0}),o=0,l=n.length;o<l;o+=1){var d=n[o],c=Object.getOwnPropertyDescriptor(s,d);void 0!==c&&c.enumerable&&(m(a[d])&&m(s[d])?s[d].__swiper__?a[d]=s[d]:v(a[d],s[d]):m(a[d])||!m(s[d])||(a[d]={},s[d].__swiper__)?a[d]=s[d]:v(a[d],s[d]))}}return a}function $(e,t,a){e.style.setProperty(t,a)}function z(e){function a(e,t){return"next"===u&&t<=e||"prev"===u&&e<=t}function r(){i=(new Date).getTime(),null===c&&(c=i);var e=Math.max(Math.min((i-c)/p,1),0),e=.5-Math.cos(e*Math.PI)/2,t=d+e*(n-d);a(t,n)&&(t=n),s.wrapperEl.scrollTo(_defineProperty({},o,t)),a(t,n)?(s.wrapperEl.style.overflow="hidden",s.wrapperEl.style.scrollSnapType="",setTimeout(function(){s.wrapperEl.style.overflow="",s.wrapperEl.scrollTo(_defineProperty({},o,t))}),l.cancelAnimationFrame(s.cssModeFrameID)):s.cssModeFrameID=l.requestAnimationFrame(r)}var i,s=e.swiper,n=e.targetPosition,o=e.side,l=G(),d=-s.translate,c=null,p=s.params.speed,u=(s.wrapperEl.style.scrollSnapType="none",l.cancelAnimationFrame(s.cssModeFrameID),d<n?"next":"prev");r()}function i(e){return e.querySelector(".swiper-slide-transform")||e.shadowRoot&&e.shadowRoot.querySelector(".swiper-slide-transform")||e}function Q(e,t){void 0===t&&(t="");var a=G(),r=_toConsumableArray(e.children);return a.HTMLSlotElement&&e instanceof HTMLSlotElement&&r.push.apply(r,_toConsumableArray(e.assignedElements())),t?r.filter(function(e){return e.matches(t)}):r}function O(e){try{console.warn(e)}catch(e){}}function C(e,t){void 0===t&&(t=[]);var a,e=document.createElement(e);return(a=e.classList).add.apply(a,_toConsumableArray(Array.isArray(t)?t:T(t))),e}function B(e){var t=G(),a=I(),r=e.getBoundingClientRect(),a=a.body,i=e.clientTop||a.clientTop||0,a=e.clientLeft||a.clientLeft||0,s=e===t?t.scrollY:e.scrollTop,t=e===t?t.scrollX:e.scrollLeft;return{top:r.top+s-i,left:r.left+t-a}}function J(e,t){return G().getComputedStyle(e,null).getPropertyValue(t)}function P(e){var t,a=e;if(a){for(t=0;null!==(a=a.previousSibling);)1===a.nodeType&&(t+=1);return t}}function X(e,t){for(var a=[],r=e.parentElement;r;)t&&!r.matches(t)||a.push(r),r=r.parentElement;return a}function g(a,r){r&&a.addEventListener("transitionend",function e(t){t.target===a&&(r.call(a,t),a.removeEventListener("transitionend",e))})}function ee(e,t,a){var r=G();return a?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(r.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function A(e){return(Array.isArray(e)?e:[e]).filter(function(e){return!!e})}function L(t){return function(e){return 0<Math.abs(e)&&t.browser&&t.browser.need3dFix&&Math.abs(e)%90==0?e+.001:e}}function E(){return a||(e=G(),t=I(),a={smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}),a;var e,t}function _(e){return void 0===e&&(e={}),p||(e=(void 0===e?{}:e).userAgent,t=E(),a=G(),r=a.navigator.platform,e=e||a.navigator.userAgent,i={ios:!1,android:!1},s=a.screen.width,a=a.screen.height,n=e.match(/(Android);?[\s\/]+([\d.]+)?/),o=e.match(/(iPad).*OS\s([\d_]+)/),l=e.match(/(iPod)(.*OS\s([\d_]+))?/),d=!o&&e.match(/(iPhone\sOS|iOS)\s([\d_]+)/),c="Win32"===r,r="MacIntel"===r,!o&&r&&t.touch&&0<=["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf("".concat(s,"x").concat(a))&&(o=(o=e.match(/(Version)\/([\d.]+)/))||[0,1,"13_0_0"]),n&&!c&&(i.os="android",i.android=!0),(o||d||l)&&(i.os="ios",i.ios=!0),p=i),p;var t,a,r,i,s,n,o,l,d,c}function D(e,t,a){t&&!e.classList.contains(a)?e.classList.add(a):!t&&e.classList.contains(a)&&e.classList.remove(a)}function u(e,t,a){t&&!e.classList.contains(a)?e.classList.add(a):!t&&e.classList.contains(a)&&e.classList.remove(a)}function s(e,t){var a,r;e&&!e.destroyed&&e.params&&(a=t.closest(e.isElement?"swiper-slide":".".concat(e.params.slideClass)))&&(!(r=a.querySelector(".".concat(e.params.lazyPreloaderClass)))&&e.isElement&&(a.shadowRoot?r=a.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)):requestAnimationFrame(function(){a.shadowRoot&&(r=a.shadowRoot.querySelector(".".concat(e.params.lazyPreloaderClass)))&&r.remove()})),r)&&r.remove()}function f(e,t){e.slides[t]&&(e=e.slides[t].querySelector('[loading="lazy"]'))&&e.removeAttribute("loading")}function c(a){if(a&&!a.destroyed&&a.params){var e=a.params.lazyPreloadPrevNext,t=a.slides.length;if(t&&e&&!(e<0)){var r,i,e=Math.min(e,t),s="auto"===a.params.slidesPerView?a.slidesPerViewDynamic():Math.ceil(a.params.slidesPerView),n=a.activeIndex;if(a.params.grid&&1<a.params.grid.rows)(i=[(r=n)-e]).push.apply(i,_toConsumableArray(Array.from({length:e}).map(function(e,t){return r+s+t}))),a.slides.forEach(function(e,t){i.includes(e.column)&&f(a,t)});else{var o=n+s-1;if(a.params.rewind||a.params.loop)for(var l=n-e;l<=o+e;l+=1){var d=(l%t+t)%t;(d<n||o<d)&&f(a,d)}else for(var c=Math.max(n-e,0);c<=Math.min(o+e,t-1);c+=1)c!==n&&(o<c||c<n)&&f(a,c)}}}}function Y(e){var t=e.swiper,a=e.runCallbacks,r=e.direction,e=e.step,i=t.activeIndex,s=t.previousIndex,r=(r=r)||(s<i?"next":i<s?"prev":"reset");if(t.emit("transition".concat(e)),a&&i!==s){if("reset"===r)return t.emit("slideResetTransition".concat(e));t.emit("slideChangeTransition".concat(e)),"next"===r?t.emit("slideNextTransition".concat(e)):t.emit("slidePrevTransition".concat(e))}}function N(e,t,a){var r=G(),e=e.params,i=e.edgeSwipeDetection,e=e.edgeSwipeThreshold;return!i||!(a<=e||a>=r.innerWidth-e)||"prevent"===i&&(t.preventDefault(),!0)}function R(){var e,t,a,r,i=this,s=i.params,n=i.el;n&&0===n.offsetWidth||(s.breakpoints&&i.setBreakpoint(),n=i.allowSlideNext,e=i.allowSlidePrev,t=i.snapGrid,a=i.virtual&&i.params.virtual.enabled,i.allowSlideNext=!0,i.allowSlidePrev=!0,i.updateSize(),i.updateSlides(),i.updateSlidesClasses(),r=a&&s.loop,!("auto"===s.slidesPerView||1<s.slidesPerView)||!i.isEnd||i.isBeginning||i.params.centeredSlides||r?i.params.loop&&!a?i.slideToLoop(i.realIndex,0,!1,!0):i.slideTo(i.activeIndex,0,!1,!0):i.slideTo(i.slides.length-1,0,!1,!0),i.autoplay&&i.autoplay.running&&i.autoplay.paused&&(clearTimeout(i.autoplay.resizeTimeout),i.autoplay.resizeTimeout=setTimeout(function(){i.autoplay&&i.autoplay.running&&i.autoplay.paused&&i.autoplay.resume()},500)),i.allowSlidePrev=e,i.allowSlideNext=n,i.params.watchOverflow&&t!==i.snapGrid&&i.checkOverflow())}function j(e,t){var a=I(),r=e.params,i=e.el,s=e.wrapperEl,n=e.device,o=!!r.nested,l="on"===t?"addEventListener":"removeEventListener";i&&"string"!=typeof i&&(a[l]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:o}),i[l]("touchstart",e.onTouchStart,{passive:!1}),i[l]("pointerdown",e.onTouchStart,{passive:!1}),a[l]("touchmove",e.onTouchMove,{passive:!1,capture:o}),a[l]("pointermove",e.onTouchMove,{passive:!1,capture:o}),a[l]("touchend",e.onTouchEnd,{passive:!0}),a[l]("pointerup",e.onTouchEnd,{passive:!0}),a[l]("pointercancel",e.onTouchEnd,{passive:!0}),a[l]("touchcancel",e.onTouchEnd,{passive:!0}),a[l]("pointerout",e.onTouchEnd,{passive:!0}),a[l]("pointerleave",e.onTouchEnd,{passive:!0}),a[l]("contextmenu",e.onTouchEnd,{passive:!0}),(r.preventClicks||r.preventClicksPropagation)&&i[l]("click",e.onClick,!0),r.cssMode&&s[l]("scroll",e.onScroll),r.updateOnWindowResize?e[t](n.ios||n.android?"resize orientationchange observerUpdate":"resize observerUpdate",R,!0):e[t]("observerUpdate",R,!0),i[l]("load",e.onLoad,{capture:!0}))}function q(e,t){return e.grid&&t.grid&&1<t.grid.rows}var V={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};var n={eventsEmitter:{on:function(e,t,a){var r,i=this;return i.eventsListeners&&!i.destroyed&&"function"==typeof t&&(r=a?"unshift":"push",e.split(" ").forEach(function(e){i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)})),i},once:function(r,i,e){var s=this;return!s.eventsListeners||s.destroyed||"function"!=typeof i?s:(n.__emitterProxy=i,s.on(r,n,e));function n(){s.off(r,n),n.__emitterProxy&&delete n.__emitterProxy;for(var e=arguments.length,t=new Array(e),a=0;a<e;a++)t[a]=arguments[a];i.apply(s,t)}},onAny:function(e,t){var a=this;return a.eventsListeners&&!a.destroyed&&"function"==typeof e&&(t=t?"unshift":"push",a.eventsAnyListeners.indexOf(e)<0)&&a.eventsAnyListeners[t](e),a},offAny:function(e){var t=this;return t.eventsListeners&&!t.destroyed&&t.eventsAnyListeners&&0<=(e=t.eventsAnyListeners.indexOf(e))&&t.eventsAnyListeners.splice(e,1),t},off:function(e,r){var i=this;return!i.eventsListeners||i.destroyed||i.eventsListeners&&e.split(" ").forEach(function(a){void 0===r?i.eventsListeners[a]=[]:i.eventsListeners[a]&&i.eventsListeners[a].forEach(function(e,t){(e===r||e.__emitterProxy&&e.__emitterProxy===r)&&i.eventsListeners[a].splice(t,1)})}),i},emit:function(){var e=this;if(e.eventsListeners&&!e.destroyed&&e.eventsListeners){for(var t,a,r,i=arguments.length,s=new Array(i),n=0;n<i;n++)s[n]=arguments[n];r="string"==typeof s[0]||Array.isArray(s[0])?(t=s[0],a=s.slice(1,s.length),e):(t=s[0].events,a=s[0].data,s[0].context||e),a.unshift(r),(Array.isArray(t)?t:t.split(" ")).forEach(function(t){e.eventsAnyListeners&&e.eventsAnyListeners.length&&e.eventsAnyListeners.forEach(function(e){e.apply(r,[t].concat(_toConsumableArray(a)))}),e.eventsListeners&&e.eventsListeners[t]&&e.eventsListeners[t].forEach(function(e){e.apply(r,a)})})}return e}},update:{updateSize:function(){var e=this,t=e.el,a=void 0!==e.params.width&&null!==e.params.width?e.params.width:t.clientWidth,r=void 0!==e.params.height&&null!==e.params.height?e.params.height:t.clientHeight;0===a&&e.isHorizontal()||0===r&&e.isVertical()||(a=a-parseInt(J(t,"padding-left")||0,10)-parseInt(J(t,"padding-right")||0,10),r=r-parseInt(J(t,"padding-top")||0,10)-parseInt(J(t,"padding-bottom")||0,10),Number.isNaN(a)&&(a=0),Number.isNaN(r)&&(r=0),Object.assign(e,{width:a,height:r,size:e.isHorizontal()?a:r}))},updateSlides:function(){var a=this;function e(e,t){return parseFloat(e.getPropertyValue(a.getDirectionLabel(t))||0)}var r=a.params,t=a.wrapperEl,G=a.slidesEl,i=a.size,s=a.rtlTranslate,n=a.wrongRTL,o=a.virtual&&r.virtual.enabled,H=(o?a.virtual:a).slides.length,l=Q(G,".".concat(a.params.slideClass,", swiper-slide")),d=(o?a.virtual.slides:l).length,c=[],p=[],u=[],m=r.slidesOffsetBefore,f=("function"==typeof m&&(m=r.slidesOffsetBefore.call(a)),r.slidesOffsetAfter),G=("function"==typeof f&&(f=r.slidesOffsetAfter.call(a)),a.snapGrid.length),B=a.slidesGrid.length,h=r.spaceBetween,v=-m,g=0,y=0;if(void 0!==i){"string"==typeof h&&0<=h.indexOf("%")?h=parseFloat(h.replace("%",""))/100*i:"string"==typeof h&&(h=parseFloat(h)),a.virtualSize=-h,l.forEach(function(e){s?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),r.centeredSlides&&r.cssMode&&($(t,"--swiper-centered-offset-before",""),$(t,"--swiper-centered-offset-after",""));for(var X,b,w,E,x,Y,N,S=r.grid&&1<r.grid.rows&&a.grid,R=(S?a.grid.initSlides(l):a.grid&&a.grid.unsetSlides(),"auto"===r.slidesPerView&&r.breakpoints&&0<Object.keys(r.breakpoints).filter(function(e){return void 0!==r.breakpoints[e].slidesPerView}).length),T=0;T<d;T+=1){var M,C,P,j,q,A,L,k,V,I,z=void 0;I=0,l[T]&&(z=l[T]),S&&a.grid.updateSlide(T,z,l),l[T]&&"none"===J(z,"display")||("auto"===r.slidesPerView?(R&&(l[T].style[a.getDirectionLabel("width")]=""),k=getComputedStyle(z),M=z.style.transform,C=z.style.webkitTransform,M&&(z.style.transform="none"),C&&(z.style.webkitTransform="none"),I=r.roundLengths?a.isHorizontal()?ee(z,"width",!0):ee(z,"height",!0):(P=e(k,"width"),j=e(k,"padding-left"),q=e(k,"padding-right"),A=e(k,"margin-left"),L=e(k,"margin-right"),(k=k.getPropertyValue("box-sizing"))&&"border-box"===k?P+A+L:(V=(k=z).clientWidth,P+j+q+A+L+(k.offsetWidth-V))),M&&(z.style.transform=M),C&&(z.style.webkitTransform=C),r.roundLengths&&(I=Math.floor(I))):(I=(i-(r.slidesPerView-1)*h)/r.slidesPerView,r.roundLengths&&(I=Math.floor(I)),l[T]&&(l[T].style[a.getDirectionLabel("width")]="".concat(I,"px"))),l[T]&&(l[T].swiperSlideSize=I),u.push(I),r.centeredSlides?(v=v+I/2+g/2+h,0===g&&0!==T&&(v=v-i/2-h),0===T&&(v=v-i/2-h),Math.abs(v)<.001&&(v=0),r.roundLengths&&(v=Math.floor(v)),y%r.slidesPerGroup==0&&c.push(v),p.push(v)):(r.roundLengths&&(v=Math.floor(v)),(y-Math.min(a.params.slidesPerGroupSkip,y))%a.params.slidesPerGroup==0&&c.push(v),p.push(v),v=v+I+h),a.virtualSize+=I+h,g=I,y+=1)}if(a.virtualSize=Math.max(a.virtualSize,i)+f,s&&n&&("slide"===r.effect||"coverflow"===r.effect)&&(t.style.width="".concat(a.virtualSize+h,"px")),r.setWrapperSize&&(t.style[a.getDirectionLabel("width")]="".concat(a.virtualSize+h,"px")),S&&a.grid.updateWrapperSize(I,c),!r.centeredSlides){for(var F=[],O=0;O<c.length;O+=1){var _=c[O];r.roundLengths&&(_=Math.floor(_)),c[O]<=a.virtualSize-i&&F.push(_)}c=F,1<Math.floor(a.virtualSize-i)-Math.floor(c[c.length-1])&&c.push(a.virtualSize-i)}if(o&&r.loop){var D=u[0]+h;if(1<r.slidesPerGroup)for(var W=Math.ceil((a.virtual.slidesBefore+a.virtual.slidesAfter)/r.slidesPerGroup),U=D*r.slidesPerGroup,K=0;K<W;K+=1)c.push(c[c.length-1]+U);for(var Z=0;Z<a.virtual.slidesBefore+a.virtual.slidesAfter;Z+=1)1===r.slidesPerGroup&&c.push(c[c.length-1]+D),p.push(p[p.length-1]+D),a.virtualSize+=D}0===c.length&&(c=[0]),0!==h&&(X=a.isHorizontal()&&s?"marginLeft":a.getDirectionLabel("marginRight"),l.filter(function(e,t){return!(r.cssMode&&!r.loop)||t!==l.length-1}).forEach(function(e){e.style[X]="".concat(h,"px")})),r.centeredSlides&&r.centeredSlidesBounds&&(b=0,u.forEach(function(e){b+=e+(h||0)}),w=i<(b-=h)?b-i:0,c=c.map(function(e){return e<=0?-m:w<e?w+f:e})),r.centerInsufficientSlides&&(E=0,u.forEach(function(e){E+=e+(h||0)}),E-=h,n=(r.slidesOffsetBefore||0)+(r.slidesOffsetAfter||0),E+n<i)&&(x=(i-E-n)/2,c.forEach(function(e,t){c[t]=e-x}),p.forEach(function(e,t){p[t]=e+x})),Object.assign(a,{slides:l,snapGrid:c,slidesGrid:p,slidesSizesGrid:u}),r.centeredSlides&&r.cssMode&&!r.centeredSlidesBounds&&($(t,"--swiper-centered-offset-before",-c[0]+"px"),$(t,"--swiper-centered-offset-after",a.size/2-u[u.length-1]/2+"px"),Y=-a.snapGrid[0],N=-a.slidesGrid[0],a.snapGrid=a.snapGrid.map(function(e){return e+Y}),a.slidesGrid=a.slidesGrid.map(function(e){return e+N})),d!==H&&a.emit("slidesLengthChange"),c.length!==G&&(a.params.watchOverflow&&a.checkOverflow(),a.emit("snapGridLengthChange")),p.length!==B&&a.emit("slidesGridLengthChange"),r.watchSlidesProgress&&a.updateSlidesOffset(),a.emit("slidesUpdated"),o||r.cssMode||"slide"!==r.effect&&"fade"!==r.effect||(n="".concat(r.containerModifierClass,"backface-hidden"),t=a.el.classList.contains(n),d<=r.maxBackfaceHiddenSlides?t||a.el.classList.add(n):t&&a.el.classList.remove(n))}},updateAutoHeight:function(e){function t(e){return n?i.slides[i.getSlideIndexByData(e)]:i.slides[e]}var a,r,i=this,s=[],n=i.virtual&&i.params.virtual.enabled,o=0;"number"==typeof e?i.setTransition(e):!0===e&&i.setTransition(i.params.speed);if("auto"!==i.params.slidesPerView&&1<i.params.slidesPerView)if(i.params.centeredSlides)(i.visibleSlides||[]).forEach(function(e){s.push(e)});else for(a=0;a<Math.ceil(i.params.slidesPerView);a+=1){var l=i.activeIndex+a;if(l>i.slides.length&&!n)break;s.push(t(l))}else s.push(t(i.activeIndex));for(a=0;a<s.length;a+=1)void 0!==s[a]&&(o=o<(r=s[a].offsetHeight)?r:o);!o&&0!==o||(i.wrapperEl.style.height="".concat(o,"px"))},updateSlidesOffset:function(){for(var e=this,t=e.slides,a=e.isElement?e.isHorizontal()?e.wrapperEl.offsetLeft:e.wrapperEl.offsetTop:0,r=0;r<t.length;r+=1)t[r].swiperSlideOffset=(e.isHorizontal()?t[r].offsetLeft:t[r].offsetTop)-a-e.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);var t=this,a=t.params,r=t.slides,i=t.rtlTranslate,s=t.snapGrid;if(0!==r.length){void 0===r[0].swiperSlideOffset&&t.updateSlidesOffset();var n=i?e:-e,o=(t.visibleSlidesIndexes=[],t.visibleSlides=[],a.spaceBetween);"string"==typeof o&&0<=o.indexOf("%")?o=parseFloat(o.replace("%",""))/100*t.size:"string"==typeof o&&(o=parseFloat(o));for(var l=0;l<r.length;l+=1){var d=r[l],c=d.swiperSlideOffset,p=(a.cssMode&&a.centeredSlides&&(c-=r[0].swiperSlideOffset),(n+(a.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o)),u=(n-s[0]+(a.centeredSlides?t.minTranslate():0)-c)/(d.swiperSlideSize+o),c=-(n-c),m=c+t.slidesSizesGrid[l],f=0<=c&&c<=t.size-t.slidesSizesGrid[l],c=0<=c&&c<t.size-1||1<m&&m<=t.size||c<=0&&m>=t.size;c&&(t.visibleSlides.push(d),t.visibleSlidesIndexes.push(l)),D(d,c,a.slideVisibleClass),D(d,f,a.slideFullyVisibleClass),d.progress=i?-p:p,d.originalProgress=i?-u:u}}},updateProgress:function(e){var t,a,r,i=this,s=(void 0===e&&(s=i.rtlTranslate?-1:1,e=i&&i.translate&&i.translate*s||0),i.params),n=i.maxTranslate()-i.minTranslate(),o=i.progress,l=i.isBeginning,d=i.isEnd,c=i.progressLoop,p=l,u=d;0==n?d=l=!(o=0):(o=(e-i.minTranslate())/n,l=(n=Math.abs(e-i.minTranslate())<1)||o<=0,d=(t=Math.abs(e-i.maxTranslate())<1)||1<=o,n&&(o=0),t&&(o=1)),s.loop&&(n=i.getSlideIndexByData(0),t=i.getSlideIndexByData(i.slides.length-1),n=i.slidesGrid[n],t=i.slidesGrid[t],a=i.slidesGrid[i.slidesGrid.length-1],1<(c=n<=(r=Math.abs(e))?(r-n)/a:(r+a-t)/a))&&--c,Object.assign(i,{progress:o,progressLoop:c,isBeginning:l,isEnd:d}),(s.watchSlidesProgress||s.centeredSlides&&s.autoHeight)&&i.updateSlidesProgress(e),l&&!p&&i.emit("reachBeginning toEdge"),d&&!u&&i.emit("reachEnd toEdge"),(p&&!l||u&&!d)&&i.emit("fromEdge"),i.emit("progress",o)},updateSlidesClasses:function(){function e(e){return Q(o,".".concat(n.slideClass).concat(e,", swiper-slide").concat(e))[0]}var t,a,r,i=this,s=i.slides,n=i.params,o=i.slidesEl,l=i.activeIndex,d=i.virtual&&n.virtual.enabled,c=i.grid&&n.grid&&1<n.grid.rows;d?r=n.loop?((d=(d=l-i.virtual.slidesBefore)<0?i.virtual.slides.length+d:d)>=i.virtual.slides.length&&(d-=i.virtual.slides.length),e('[data-swiper-slide-index="'.concat(d,'"]'))):e('[data-swiper-slide-index="'.concat(l,'"]')):c?(r=s.find(function(e){return e.column===l}),a=s.find(function(e){return e.column===l+1}),t=s.find(function(e){return e.column===l-1})):r=s[l],r&&!c&&(a=function(e,t){for(var a=[];e.nextElementSibling;){var r=e.nextElementSibling;t&&!r.matches(t)||a.push(r),e=r}return a}(r,".".concat(n.slideClass,", swiper-slide"))[0],n.loop&&!a&&(a=s[0]),t=function(e,t){for(var a=[];e.previousElementSibling;){var r=e.previousElementSibling;t&&!r.matches(t)||a.push(r),e=r}return a}(r,".".concat(n.slideClass,", swiper-slide"))[0],n.loop)&&0===!t&&(t=s[s.length-1]),s.forEach(function(e){u(e,e===r,n.slideActiveClass),u(e,e===a,n.slideNextClass),u(e,e===t,n.slidePrevClass)}),i.emitSlidesClasses()},updateActiveIndex:function(e){function t(e){return(e=(e-=s.virtual.slidesBefore)<0?s.virtual.slides.length+e:e)>=s.virtual.slides.length&&(e-=s.virtual.slides.length),e}var s=this,a=s.rtlTranslate?s.translate:-s.translate,r=s.snapGrid,i=s.params,n=s.activeIndex,o=s.realIndex,l=s.snapIndex,d=e;void 0===d&&(d=function(){for(var e,t=s.slidesGrid,a=s.params,r=s.rtlTranslate?s.translate:-s.translate,i=0;i<t.length;i+=1)void 0!==t[i+1]?r>=t[i]&&r<t[i+1]-(t[i+1]-t[i])/2?e=i:r>=t[i]&&r<t[i+1]&&(e=i+1):r>=t[i]&&(e=i);return e=a.normalizeSlideIndex&&(e<0||void 0===e)?0:e}()),(a=0<=r.indexOf(a)?r.indexOf(a):(e=Math.min(i.slidesPerGroupSkip,d))+Math.floor((d-e)/i.slidesPerGroup))>=r.length&&(a=r.length-1),d!==n||s.params.loop?d===n&&s.params.loop&&s.virtual&&s.params.virtual.enabled?s.realIndex=t(d):(e=s.grid&&i.grid&&1<i.grid.rows,e=s.virtual&&i.virtual.enabled&&i.loop?t(d):e?(r=s.slides.find(function(e){return e.column===d}),e=parseInt(r.getAttribute("data-swiper-slide-index"),10),Number.isNaN(e)&&(e=Math.max(s.slides.indexOf(r),0)),Math.floor(e/i.grid.rows)):s.slides[d]&&(r=s.slides[d].getAttribute("data-swiper-slide-index"))?parseInt(r,10):d,Object.assign(s,{previousSnapIndex:l,snapIndex:a,previousRealIndex:o,realIndex:e,previousIndex:n,activeIndex:d}),s.initialized&&c(s),s.emit("activeIndexChange"),s.emit("snapIndexChange"),(s.initialized||s.params.runCallbacksOnInit)&&(o!==e&&s.emit("realIndexChange"),s.emit("slideChange"))):a!==l&&(s.snapIndex=a,s.emit("snapIndexChange"))},updateClickedSlide:function(e,t){var a=this,r=a.params,i=e.closest(".".concat(r.slideClass,", swiper-slide"));!i&&a.isElement&&t&&1<t.length&&t.includes(e)&&_toConsumableArray(t.slice(t.indexOf(e)+1,t.length)).forEach(function(e){!i&&e.matches&&e.matches(".".concat(r.slideClass,", swiper-slide"))&&(i=e)});var s,n=!1;if(i)for(var o=0;o<a.slides.length;o+=1)if(a.slides[o]===i){n=!0,s=o;break}i&&n?(a.clickedSlide=i,a.virtual&&a.params.virtual.enabled?a.clickedIndex=parseInt(i.getAttribute("data-swiper-slide-index"),10):a.clickedIndex=s,r.slideToClickedSlide&&void 0!==a.clickedIndex&&a.clickedIndex!==a.activeIndex&&a.slideToClickedSlide()):(a.clickedSlide=void 0,a.clickedIndex=void 0)}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");var t=this.params,a=this.rtlTranslate,r=this.translate,i=this.wrapperEl;return t.virtualTranslate?a?-r:r:t.cssMode?r:(t=H(i,e),t+=this.cssOverflowAdjustment(),(t=a?-t:t)||0)},setTranslate:function(e,t){var a=this,r=a.rtlTranslate,i=a.params,s=a.wrapperEl,n=a.progress,o=0,l=0,r=(a.isHorizontal()?o=r?-e:e:l=e,i.roundLengths&&(o=Math.floor(o),l=Math.floor(l)),a.previousTranslate=a.translate,a.translate=a.isHorizontal()?o:l,i.cssMode?s[a.isHorizontal()?"scrollLeft":"scrollTop"]=a.isHorizontal()?-o:-l:i.virtualTranslate||(a.isHorizontal()?o-=a.cssOverflowAdjustment():l-=a.cssOverflowAdjustment(),s.style.transform="translate3d(".concat(o,"px, ").concat(l,"px, 0px)")),a.maxTranslate()-a.minTranslate());(0==r?0:(e-a.minTranslate())/r)!==n&&a.updateProgress(e),a.emit("setTranslate",a.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,a,r,i){void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===a&&(a=!0),void 0===r&&(r=!0);var s=this,n=s.params,o=s.wrapperEl;if(s.animating&&n.preventInteractionOnTransition)return!1;var l=s.minTranslate(),d=s.maxTranslate(),l=r&&l<e?l:r&&e<d?d:e;if(s.updateProgress(l),n.cssMode){r=s.isHorizontal();if(0===t)o[r?"scrollLeft":"scrollTop"]=-l;else{if(!s.support.smoothScroll)return z({swiper:s,targetPosition:-l,side:r?"left":"top"}),!0;o.scrollTo(_defineProperty(_defineProperty({},r?"left":"top",-l),"behavior","smooth"))}}else 0===t?(s.setTransition(0),s.setTranslate(l),a&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionEnd"))):(s.setTransition(t),s.setTranslate(l),a&&(s.emit("beforeTransitionStart",t,i),s.emit("transitionStart")),s.animating||(s.animating=!0,s.onTranslateToWrapperTransitionEnd||(s.onTranslateToWrapperTransitionEnd=function(e){s&&!s.destroyed&&e.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onTranslateToWrapperTransitionEnd),s.onTranslateToWrapperTransitionEnd=null,delete s.onTranslateToWrapperTransitionEnd,s.animating=!1,a)&&s.emit("transitionEnd")}),s.wrapperEl.addEventListener("transitionend",s.onTranslateToWrapperTransitionEnd)));return!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration="".concat(e,"ms"),this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);var a=this.params;a.cssMode||(a.autoHeight&&this.updateAutoHeight(),Y({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);var a=this.params;this.animating=!1,a.cssMode||(this.setTransition(0),Y({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,a,r,i){void 0===a&&(a=!0),"string"==typeof(e=void 0===e?0:e)&&(e=parseInt(e,10));var s=this,n=e,e=(n<0&&(n=0),s.params),o=s.snapGrid,l=s.slidesGrid,d=s.previousIndex,c=s.activeIndex,p=s.rtlTranslate,u=s.wrapperEl;if(!s.enabled&&!r&&!i||s.destroyed||s.animating&&e.preventInteractionOnTransition)return!1;void 0===t&&(t=s.params.speed);var m=Math.min(s.params.slidesPerGroupSkip,n),m=m+Math.floor((n-m)/s.params.slidesPerGroup),f=-o[m=m>=o.length?o.length-1:m];if(e.normalizeSlideIndex)for(var h=0;h<l.length;h+=1){var v=-Math.floor(100*f),g=Math.floor(100*l[h]),y=Math.floor(100*l[h+1]);void 0!==l[h+1]?g<=v&&v<y-(y-g)/2?n=h:g<=v&&v<y&&(n=h+1):g<=v&&(n=h)}if(s.initialized&&n!==c){if(!s.allowSlideNext&&(p?f>s.translate&&f>s.minTranslate():f<s.translate&&f<s.minTranslate()))return!1;if(!s.allowSlidePrev&&f>s.translate&&f>s.maxTranslate()&&(c||0)!==n)return!1}n!==(d||0)&&a&&s.emit("beforeSlideChangeStart"),s.updateProgress(f);var b=c<n?"next":n<c?"prev":"reset",o=s.virtual&&s.params.virtual.enabled;if((!o||!i)&&(p&&-f===s.translate||!p&&f===s.translate))return s.updateActiveIndex(n),e.autoHeight&&s.updateAutoHeight(),s.updateSlidesClasses(),"slide"!==e.effect&&s.setTranslate(f),"reset"!=b&&(s.transitionStart(a,b),s.transitionEnd(a,b)),!1;if(e.cssMode){var w=s.isHorizontal(),E=p?f:-f;if(0===t)o&&(s.wrapperEl.style.scrollSnapType="none",s._immediateVirtual=!0),o&&!s._cssModeVirtualInitialSet&&0<s.params.initialSlide?(s._cssModeVirtualInitialSet=!0,requestAnimationFrame(function(){u[w?"scrollLeft":"scrollTop"]=E})):u[w?"scrollLeft":"scrollTop"]=E,o&&requestAnimationFrame(function(){s.wrapperEl.style.scrollSnapType="",s._immediateVirtual=!1});else{if(!s.support.smoothScroll)return z({swiper:s,targetPosition:E,side:w?"left":"top"}),!0;u.scrollTo(_defineProperty(_defineProperty({},w?"left":"top",E),"behavior","smooth"))}}else s.setTransition(t),s.setTranslate(f),s.updateActiveIndex(n),s.updateSlidesClasses(),s.emit("beforeTransitionStart",t,r),s.transitionStart(a,b),0===t?s.transitionEnd(a,b):s.animating||(s.animating=!0,s.onSlideToWrapperTransitionEnd||(s.onSlideToWrapperTransitionEnd=function(e){s&&!s.destroyed&&e.target===this&&(s.wrapperEl.removeEventListener("transitionend",s.onSlideToWrapperTransitionEnd),s.onSlideToWrapperTransitionEnd=null,delete s.onSlideToWrapperTransitionEnd,s.transitionEnd(a,b))}),s.wrapperEl.addEventListener("transitionend",s.onSlideToWrapperTransitionEnd));return!0},slideToLoop:function(e,t,a,r){void 0===a&&(a=!0),"string"==typeof(e=void 0===e?0:e)&&(e=parseInt(e,10));var i,s,n,o,l,d,c,p,u=this;if(!u.destroyed)return void 0===t&&(t=u.params.speed),i=u.grid&&u.params.grid&&1<u.params.grid.rows,p=e,u.params.loop&&(u.virtual&&u.params.virtual.enabled?p+=u.virtual.slidesBefore:(e=i?(s=p*u.params.grid.rows,u.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")==s}).column):u.getSlideIndexByData(p),n=i?Math.ceil(u.slides.length/u.params.grid.rows):u.slides.length,o=u.params.centeredSlides,"auto"===(d=u.params.slidesPerView)?d=u.slidesPerViewDynamic():(d=Math.ceil(parseFloat(u.params.slidesPerView,10)),o&&d%2==0&&(d+=1)),l=n-e<d,o&&(l=l||e<Math.ceil(d/2)),(l=!(r&&o&&"auto"!==u.params.slidesPerView&&!i)&&l)&&(d=o?e<u.activeIndex?"prev":"next":e-u.activeIndex-1<u.params.slidesPerView?"next":"prev",u.loopFix({direction:d,slideTo:!0,activeSlideIndex:"next"==d?e+1:e-n+1,slideRealIndex:"next"==d?u.realIndex:void 0})),p=i?(c=p*u.params.grid.rows,u.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")==c}).column):u.getSlideIndexByData(p))),requestAnimationFrame(function(){u.slideTo(p,t,a,r)}),u},slideNext:function(e,t,a){void 0===t&&(t=!0);var r=this,i=r.enabled,s=r.params,n=r.animating;if(!i||r.destroyed)return r;void 0===e&&(e=r.params.speed);var i=s.slidesPerGroup,o=("auto"===s.slidesPerView&&1===s.slidesPerGroup&&s.slidesPerGroupAuto&&(i=Math.max(r.slidesPerViewDynamic("current",!0),1)),r.activeIndex<s.slidesPerGroupSkip?1:i),i=r.virtual&&s.virtual.enabled;if(s.loop){if(n&&!i&&s.loopPreventsSliding)return!1;if(r.loopFix({direction:"next"}),r._clientLeft=r.wrapperEl.clientLeft,r.activeIndex===r.slides.length-1&&s.cssMode)return requestAnimationFrame(function(){r.slideTo(r.activeIndex+o,e,t,a)}),!0}return s.rewind&&r.isEnd?r.slideTo(0,e,t,a):r.slideTo(r.activeIndex+o,e,t,a)},slidePrev:function(e,t,a){void 0===t&&(t=!0);var r=this,i=r.params,s=r.snapGrid,n=r.slidesGrid,o=r.rtlTranslate,l=r.enabled,d=r.animating;if(!l||r.destroyed)return r;void 0===e&&(e=r.params.speed);l=r.virtual&&i.virtual.enabled;if(i.loop){if(d&&!l&&i.loopPreventsSliding)return!1;r.loopFix({direction:"prev"}),r._clientLeft=r.wrapperEl.clientLeft}function c(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}var p,u=c(o?r.translate:-r.translate),d=s.map(c),l=s[d.indexOf(u)-1],m=(void 0===l&&i.cssMode&&(s.forEach(function(e,t){e<=u&&(p=t)}),void 0!==p)&&(l=s[0<p?p-1:p]),0);return void 0!==l&&((m=n.indexOf(l))<0&&(m=r.activeIndex-1),"auto"===i.slidesPerView)&&1===i.slidesPerGroup&&i.slidesPerGroupAuto&&(m=m-r.slidesPerViewDynamic("previous",!0)+1,m=Math.max(m,0)),i.rewind&&r.isBeginning?(o=r.params.virtual&&r.params.virtual.enabled&&r.virtual?r.virtual.slides.length-1:r.slides.length-1,r.slideTo(o,e,t,a)):i.loop&&0===r.activeIndex&&i.cssMode?(requestAnimationFrame(function(){r.slideTo(m,e,t,a)}),!0):r.slideTo(m,e,t,a)},slideReset:function(e,t,a){void 0===t&&(t=!0);if(!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,a)},slideToClosest:function(e,t,a,r){void 0===t&&(t=!0),void 0===r&&(r=.5);var i,s,n,o,l=this;if(!l.destroyed)return void 0===e&&(e=l.params.speed),i=l.activeIndex,s=(s=Math.min(l.params.slidesPerGroupSkip,i))+Math.floor((i-s)/l.params.slidesPerGroup),(n=l.rtlTranslate?l.translate:-l.translate)>=l.snapGrid[s]?n-(o=l.snapGrid[s])>(l.snapGrid[s+1]-o)*r&&(i+=l.params.slidesPerGroup):n-(o=l.snapGrid[s-1])<=(l.snapGrid[s]-o)*r&&(i-=l.params.slidesPerGroup),i=Math.max(i,0),i=Math.min(i,l.slidesGrid.length-1),l.slideTo(i,e,t,a)},slideToClickedSlide:function(){var e,t,a,r,i,s,n=this;n.destroyed||(e=n.params,t=n.slidesEl,a="auto"===e.slidesPerView?n.slidesPerViewDynamic():e.slidesPerView,i=n.clickedIndex,s=n.isElement?"swiper-slide":".".concat(e.slideClass),e.loop?n.animating||(r=parseInt(n.clickedSlide.getAttribute("data-swiper-slide-index"),10),e.centeredSlides?i<n.loopedSlides-a/2||i>n.slides.length-n.loopedSlides+a/2?(n.loopFix(),i=n.getSlideIndex(Q(t,"".concat(s,'[data-swiper-slide-index="').concat(r,'"]'))[0]),M(function(){n.slideTo(i)})):n.slideTo(i):i>n.slides.length-a?(n.loopFix(),i=n.getSlideIndex(Q(t,"".concat(s,'[data-swiper-slide-index="').concat(r,'"]'))[0]),M(function(){n.slideTo(i)})):n.slideTo(i)):n.slideTo(i))}},loop:{loopCreate:function(e){var t,a,r,i,s,n=this,o=n.params,l=n.slidesEl;!o.loop||n.virtual&&n.params.virtual.enabled||(t=function(){Q(l,".".concat(o.slideClass,", swiper-slide")).forEach(function(e,t){e.setAttribute("data-swiper-slide-index",t)})},i=n.grid&&o.grid&&1<o.grid.rows,a=o.slidesPerGroup*(i?o.grid.rows:1),r=n.slides.length%a!=0,i=i&&n.slides.length%o.grid.rows!=0,s=function(e){for(var t=0;t<e;t+=1){var a=n.isElement?C("swiper-slide",[o.slideBlankClass]):C("div",[o.slideClass,o.slideBlankClass]);n.slidesEl.append(a)}},r?o.loopAddBlankSlides?(s(a-n.slides.length%a),n.recalcSlides(),n.updateSlides()):O("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):i&&(o.loopAddBlankSlides?(s(o.grid.rows-n.slides.length%o.grid.rows),n.recalcSlides(),n.updateSlides()):O("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),t(),n.loopFix({slideRealIndex:e,direction:o.centeredSlides?void 0:"next"}))},loopFix:function(e){var e=void 0===e?{}:e,t=e.slideRealIndex,a=e.slideTo,r=void 0===a||a,a=e.direction,i=e.setTranslate,s=e.activeSlideIndex,n=e.byController,e=e.byMousewheel,o=this;if(o.params.loop){o.emit("beforeLoopFix");var l=o.slides,d=o.allowSlidePrev,c=o.allowSlideNext,p=o.slidesEl,u=o.params,m=u.centeredSlides;if(o.allowSlidePrev=!0,o.allowSlideNext=!0,o.virtual&&u.virtual.enabled)r&&(u.centeredSlides||0!==o.snapIndex?u.centeredSlides&&o.snapIndex<u.slidesPerView?o.slideTo(o.virtual.slides.length+o.snapIndex,0,!1,!0):o.snapIndex===o.snapGrid.length-1&&o.slideTo(o.virtual.slidesBefore,0,!1,!0):o.slideTo(o.virtual.slides.length,0,!1,!0)),o.allowSlidePrev=d,o.allowSlideNext=c;else{var f,h=u.slidesPerView,v=("auto"===h?h=o.slidesPerViewDynamic():(h=Math.ceil(parseFloat(u.slidesPerView,10)),m&&h%2==0&&(h+=1)),u.slidesPerGroupAuto?h:u.slidesPerGroup),g=v,y=(g%v!=0&&(g+=v-g%v),g+=u.loopAdditionalSlides,o.loopedSlides=g,o.grid&&u.grid&&1<u.grid.rows),b=(l.length<h+g?O("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled and not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):y&&"row"===u.grid.fill&&O("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`"),[]),w=[],E=o.activeIndex,x=(void 0===s?s=o.getSlideIndex(l.find(function(e){return e.classList.contains(u.slideActiveClass)})):E=s,"next"===a||!a),S="prev"===a||!a,T=0,M=0,C=y?Math.ceil(l.length/u.grid.rows):l.length,P=(y?l[s].column:s)+(m&&void 0===i?-h/2+.5:0);if(P<g)for(var T=Math.max(g-P,v),A=0;A<g-P;A+=1){var L=A-Math.floor(A/C)*C;if(y)for(var k=C-L-1,I=l.length-1;0<=I;--I)l[I].column===k&&b.push(I);else b.push(C-L-1)}else if(C-g<P+h)for(var M=Math.max(P-(C-2*g),v),z=0;z<M;z+=1)!function(){var a=z-Math.floor(z/C)*C;y?l.forEach(function(e,t){e.column===a&&w.push(t)}):w.push(a)}();o.__preventObserver__=!0,requestAnimationFrame(function(){o.__preventObserver__=!1}),S&&b.forEach(function(e){l[e].swiperLoopMoveDOM=!0,p.prepend(l[e]),l[e].swiperLoopMoveDOM=!1}),x&&w.forEach(function(e){l[e].swiperLoopMoveDOM=!0,p.append(l[e]),l[e].swiperLoopMoveDOM=!1}),o.recalcSlides(),"auto"===u.slidesPerView?o.updateSlides():y&&(0<b.length&&S||0<w.length&&x)&&o.slides.forEach(function(e,t){o.grid.updateSlide(t,e,o.slides)}),u.watchSlidesProgress&&o.updateSlidesOffset(),r&&(0<b.length&&S?void 0===t?(m=o.slidesGrid[E],h=o.slidesGrid[E+T]-m,e?o.setTranslate(o.translate-h):(o.slideTo(E+Math.ceil(T),0,!1,!0),i&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-h,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-h))):i&&(v=y?b.length/u.grid.rows:b.length,o.slideTo(o.activeIndex+v,0,!1,!0),o.touchEventsData.currentTranslate=o.translate):0<w.length&&x&&(void 0===t?(S=o.slidesGrid[E],m=o.slidesGrid[E-M]-S,e?o.setTranslate(o.translate-m):(o.slideTo(E-M,0,!1,!0),i&&(o.touchEventsData.startTranslate=o.touchEventsData.startTranslate-m,o.touchEventsData.currentTranslate=o.touchEventsData.currentTranslate-m))):(T=y?w.length/u.grid.rows:w.length,o.slideTo(o.activeIndex-T,0,!1,!0)))),o.allowSlidePrev=d,o.allowSlideNext=c,o.controller&&o.controller.control&&!n&&(f={slideRealIndex:t,direction:a,setTranslate:i,activeSlideIndex:s,byController:!0},Array.isArray(o.controller.control)?o.controller.control.forEach(function(e){!e.destroyed&&e.params.loop&&e.loopFix(_objectSpread(_objectSpread({},f),{},{slideTo:e.params.slidesPerView===u.slidesPerView&&r}))}):o.controller.control instanceof o.constructor&&o.controller.control.params.loop&&o.controller.control.loopFix(_objectSpread(_objectSpread({},f),{},{slideTo:o.controller.control.params.slidesPerView===u.slidesPerView&&r})))}o.emit("loopFix")}},loopDestroy:function(){var a,e=this,t=e.params,r=e.slidesEl;!t.loop||e.virtual&&e.params.virtual.enabled||(e.recalcSlides(),a=[],e.slides.forEach(function(e){var t=void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex;a[t]=e}),e.slides.forEach(function(e){e.removeAttribute("data-swiper-slide-index")}),a.forEach(function(e){r.append(e)}),e.recalcSlides(),e.slideTo(e.realIndex,0))}},grabCursor:{setGrabCursor:function(e){var t,a=this;!a.params.simulateTouch||a.params.watchOverflow&&a.isLocked||a.params.cssMode||(t="container"===a.params.touchEventsTarget?a.el:a.wrapperEl,a.isElement&&(a.__preventObserver__=!0),t.style.cursor="move",t.style.cursor=e?"grabbing":"grab",a.isElement&&requestAnimationFrame(function(){a.__preventObserver__=!1}))},unsetGrabCursor:function(){var e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(function(){e.__preventObserver__=!1}))}},events:{attachEvents:function(){var e=this,t=e.params;e.onTouchStart=function(e){var t,a,i,r,s,n,o,l,d,c=this,p=I(),u=(e.originalEvent&&(e=e.originalEvent),c.touchEventsData);if("pointerdown"===e.type){if(null!==u.pointerId&&u.pointerId!==e.pointerId)return;u.pointerId=e.pointerId}else"touchstart"===e.type&&1===e.targetTouches.length&&(u.touchId=e.targetTouches[0].identifier);"touchstart"===e.type?N(c,e,e.targetTouches[0].pageX):(t=c.params,d=c.touches,!c.enabled||!t.simulateTouch&&"mouse"===e.pointerType||c.animating&&t.preventInteractionOnTransition||(!c.animating&&t.cssMode&&t.loop&&c.loopFix(),a=e.target,!("wrapper"!==t.touchEventsTarget||(s=a,i=c.wrapperEl,r=G(),n=!(n=i.contains(s))&&r.HTMLSlotElement&&i instanceof HTMLSlotElement?(n=_toConsumableArray(i.assignedElements()).includes(s))||function(e){for(var t=[i];0<t.length;){var a,r=t.shift();if(e===r)return!0;t.push.apply(t,_toConsumableArray(r.children).concat(_toConsumableArray((null==(a=r.shadowRoot)?void 0:a.children)||[]),_toConsumableArray((null==(a=r.assignedElements)?void 0:a.call(r))||[])))}}(s):n)))||"which"in e&&3===e.which||"button"in e&&0<e.button||u.isTouched&&u.isMoved||(r=!!t.noSwipingClass&&""!==t.noSwipingClass,s=e.composedPath?e.composedPath():e.path,r&&e.target&&e.target.shadowRoot&&s&&(a=s[0]),n=t.noSwipingSelector||".".concat(t.noSwipingClass),l=!(!e.target||!e.target.shadowRoot),t.noSwiping&&(l?function(r){return function e(t){var a;return t&&t!==I()&&t!==G()&&((a=(t=t.assignedSlot?t.assignedSlot:t).closest(r))||t.getRootNode)?a||e(t.getRootNode().host):null}(void 0===a?this:a)}(n):a.closest(n))?c.allowClick=!0:t.swipeHandler&&!a.closest(t.swipeHandler)||(d.currentX=e.pageX,d.currentY=e.pageY,l=d.currentX,o=d.currentY,N(c,e,l)&&(Object.assign(u,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),d.startX=l,d.startY=o,u.touchStartTime=b(),c.allowClick=!0,c.updateSize(),c.swipeDirection=void 0,0<t.threshold&&(u.allowThresholdMove=!1),l=!0,a.matches(u.focusableElements)&&(l=!1,"SELECT"===a.nodeName)&&(u.isTouched=!1),p.activeElement&&p.activeElement.matches(u.focusableElements)&&p.activeElement!==a&&("mouse"===e.pointerType||"mouse"!==e.pointerType&&!a.matches(u.focusableElements))&&p.activeElement.blur(),d=l&&c.allowTouchMove&&t.touchStartPreventDefault,!t.touchStartForcePreventDefault&&!d||a.isContentEditable||e.preventDefault(),t.freeMode&&t.freeMode.enabled&&c.freeMode&&c.animating&&!t.cssMode&&c.freeMode.onTouchStart(),c.emit("touchStart",e)))))}.bind(e),e.onTouchMove=function(e){var t=I(),a=this,r=a.touchEventsData,i=a.params,s=a.touches,n=a.rtlTranslate,o=a.enabled;if(o&&(i.simulateTouch||"mouse"!==e.pointerType)){o=e;if("pointermove"===(o=o.originalEvent?o.originalEvent:o).type){if(null!==r.touchId)return;if(o.pointerId!==r.pointerId)return}if("touchmove"===o.type){if(!(l=_toConsumableArray(o.changedTouches).find(function(e){return e.identifier===r.touchId}))||l.identifier!==r.touchId)return}else l=o;if(r.isTouched){var e=l.pageX,l=l.pageY;if(o.preventedByNestedSwiper)s.startX=e,s.startY=l;else if(a.allowTouchMove){if(i.touchReleaseOnEdges&&!i.loop)if(a.isVertical()){if(l<s.startY&&a.translate<=a.maxTranslate()||l>s.startY&&a.translate>=a.minTranslate())return r.isTouched=!1,void(r.isMoved=!1)}else if(e<s.startX&&a.translate<=a.maxTranslate()||e>s.startX&&a.translate>=a.minTranslate())return;if(t.activeElement&&t.activeElement.matches(r.focusableElements)&&t.activeElement!==o.target&&"mouse"!==o.pointerType&&t.activeElement.blur(),t.activeElement&&o.target===t.activeElement&&o.target.matches(r.focusableElements))r.isMoved=!0,a.allowClick=!1;else{r.allowTouchCallbacks&&a.emit("touchMove",o),s.previousX=s.currentX,s.previousY=s.currentY,s.currentX=e,s.currentY=l;var t=s.currentX-s.startX,d=s.currentY-s.startY;if(!(a.params.threshold&&Math.sqrt(Math.pow(t,2)+Math.pow(d,2))<a.params.threshold))if(void 0===r.isScrolling&&(a.isHorizontal()&&s.currentY===s.startY||a.isVertical()&&s.currentX===s.startX?r.isScrolling=!1:25<=t*t+d*d&&(c=180*Math.atan2(Math.abs(d),Math.abs(t))/Math.PI,r.isScrolling=a.isHorizontal()?c>i.touchAngle:90-c>i.touchAngle)),r.isScrolling&&a.emit("touchMoveOpposite",o),void 0!==r.startMoving||s.currentX===s.startX&&s.currentY===s.startY||(r.startMoving=!0),r.isScrolling||"touchmove"===o.type&&r.preventTouchMoveFromPointerMove)r.isTouched=!1;else if(r.startMoving){a.allowClick=!1,!i.cssMode&&o.cancelable&&o.preventDefault(),i.touchMoveStopPropagation&&!i.nested&&o.stopPropagation();var c=a.isHorizontal()?t:d,t=a.isHorizontal()?s.currentX-s.previousX:s.currentY-s.previousY,d=(i.oneWayMovement&&(c=Math.abs(c)*(n?1:-1),t=Math.abs(t)*(n?1:-1)),s.diff=c,c*=i.touchRatio,n&&(c=-c,t=-t),a.touchesDirection),n=(a.swipeDirection=0<c?"prev":"next",a.touchesDirection=0<t?"prev":"next",a.params.loop&&!i.cssMode),t="next"===a.touchesDirection&&a.allowSlideNext||"prev"===a.touchesDirection&&a.allowSlidePrev;if(r.isMoved||(n&&t&&a.loopFix({direction:a.swipeDirection}),r.startTranslate=a.getTranslate(),a.setTransition(0),a.animating&&(p=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}}),a.wrapperEl.dispatchEvent(p)),r.allowMomentumBounce=!1,!i.grabCursor||!0!==a.allowSlideNext&&!0!==a.allowSlidePrev||a.setGrabCursor(!0),a.emit("sliderFirstMove",o)),(new Date).getTime(),r.isMoved&&r.allowThresholdMove&&d!==a.touchesDirection&&n&&t&&1<=Math.abs(c))Object.assign(s,{startX:e,startY:l,currentX:e,currentY:l,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;else{a.emit("sliderMove",o),r.isMoved=!0,r.currentTranslate=c+r.startTranslate;var p=!0,d=i.resistanceRatio;if(i.touchReleaseOnEdges&&(d=0),0<c?(n&&t&&r.allowThresholdMove&&r.currentTranslate>(i.centeredSlides?a.minTranslate()-a.slidesSizesGrid[a.activeIndex+1]-("auto"!==i.slidesPerView&&2<=a.slides.length-i.slidesPerView?a.slidesSizesGrid[a.activeIndex+1]+a.params.spaceBetween:0)-a.params.spaceBetween:a.minTranslate())&&a.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>a.minTranslate()&&(p=!1,i.resistance)&&(r.currentTranslate=a.minTranslate()-1+Math.pow(-a.minTranslate()+r.startTranslate+c,d))):c<0&&(n&&t&&r.allowThresholdMove&&r.currentTranslate<(i.centeredSlides?a.maxTranslate()+a.slidesSizesGrid[a.slidesSizesGrid.length-1]+a.params.spaceBetween+("auto"!==i.slidesPerView&&2<=a.slides.length-i.slidesPerView?a.slidesSizesGrid[a.slidesSizesGrid.length-1]+a.params.spaceBetween:0):a.maxTranslate())&&a.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:a.slides.length-("auto"===i.slidesPerView?a.slidesPerViewDynamic():Math.ceil(parseFloat(i.slidesPerView,10)))}),r.currentTranslate<a.maxTranslate())&&(p=!1,i.resistance)&&(r.currentTranslate=a.maxTranslate()+1-Math.pow(a.maxTranslate()-r.startTranslate-c,d)),p&&(o.preventedByNestedSwiper=!0),!a.allowSlideNext&&"next"===a.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!a.allowSlidePrev&&"prev"===a.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),a.allowSlidePrev||a.allowSlideNext||(r.currentTranslate=r.startTranslate),0<i.threshold){if(!(Math.abs(c)>i.threshold||r.allowThresholdMove))return void(r.currentTranslate=r.startTranslate);if(!r.allowThresholdMove)return r.allowThresholdMove=!0,s.startX=s.currentX,s.startY=s.currentY,r.currentTranslate=r.startTranslate,void(s.diff=a.isHorizontal()?s.currentX-s.startX:s.currentY-s.startY)}i.followFinger&&!i.cssMode&&((i.freeMode&&i.freeMode.enabled&&a.freeMode||i.watchSlidesProgress)&&(a.updateActiveIndex(),a.updateSlidesClasses()),i.freeMode&&i.freeMode.enabled&&a.freeMode&&a.freeMode.onTouchMove(),a.updateProgress(r.currentTranslate),a.setTranslate(r.currentTranslate))}}}}else o.target.matches(r.focusableElements)||(a.allowClick=!1),r.isTouched&&(Object.assign(s,{startX:e,startY:l,currentX:e,currentY:l}),r.touchStartTime=b())}else r.startMoving&&r.isScrolling&&a.emit("touchMoveOpposite",o)}}.bind(e),e.onTouchEnd=function(e){var t=this,a=t.touchEventsData;if("touchend"===(e=e.originalEvent?e.originalEvent:e).type||"touchcancel"===e.type){if(!(i=_toConsumableArray(e.changedTouches).find(function(e){return e.identifier===a.touchId}))||i.identifier!==a.touchId)return}else{if(null!==a.touchId)return;if(e.pointerId!==a.pointerId)return;i=e}if(!["pointercancel","pointerout","pointerleave","contextmenu"].includes(e.type)||["pointercancel","contextmenu"].includes(e.type)&&(t.browser.isSafari||t.browser.isWebView)){a.pointerId=null,a.touchId=null;var r=t.params,i=t.touches,s=t.rtlTranslate,n=t.slidesGrid,o=t.enabled;if(o&&(r.simulateTouch||"mouse"!==e.pointerType))if(a.allowTouchCallbacks&&t.emit("touchEnd",e),a.allowTouchCallbacks=!1,a.isTouched){r.grabCursor&&a.isMoved&&a.isTouched&&(!0===t.allowSlideNext||!0===t.allowSlidePrev)&&t.setGrabCursor(!1);var l,o=b(),d=o-a.touchStartTime;if(t.allowClick&&(h=e.path||e.composedPath&&e.composedPath(),t.updateClickedSlide(h&&h[0]||e.target,h),t.emit("tap click",e),d<300)&&o-a.lastClickTime<300&&t.emit("doubleTap doubleClick",e),a.lastClickTime=b(),M(function(){t.destroyed||(t.allowClick=!0)}),a.isTouched&&a.isMoved&&t.swipeDirection&&(0!==i.diff||a.loopSwapReset)&&(a.currentTranslate!==a.startTranslate||a.loopSwapReset)){if(a.isTouched=!1,a.isMoved=!1,a.startMoving=!1,l=r.followFinger?s?t.translate:-t.translate:-a.currentTranslate,!r.cssMode)if(r.freeMode&&r.freeMode.enabled)t.freeMode.onTouchEnd({currentPos:l});else{for(var c=l>=-t.maxTranslate()&&!t.params.loop,p=0,u=t.slidesSizesGrid[0],m=0;m<n.length;m+=m<r.slidesPerGroupSkip?1:r.slidesPerGroup){var f=m<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;void 0!==n[m+f]?(c||l>=n[m]&&l<n[m+f])&&(u=n[(p=m)+f]-n[m]):(c||l>=n[m])&&(p=m,u=n[n.length-1]-n[n.length-2])}var h=null,o=null,i=(r.rewind&&(t.isBeginning?o=r.virtual&&r.virtual.enabled&&t.virtual?t.virtual.slides.length-1:t.slides.length-1:t.isEnd&&(h=0)),(l-n[p])/u),s=p<r.slidesPerGroupSkip-1?1:r.slidesPerGroup;d>r.longSwipesMs?r.longSwipes?("next"===t.swipeDirection&&(i>=r.longSwipesRatio?t.slideTo(r.rewind&&t.isEnd?h:p+s):t.slideTo(p)),"prev"===t.swipeDirection&&(i>1-r.longSwipesRatio?t.slideTo(p+s):null!==o&&i<0&&Math.abs(i)>r.longSwipesRatio?t.slideTo(o):t.slideTo(p))):t.slideTo(t.activeIndex):r.shortSwipes?!t.navigation||e.target!==t.navigation.nextEl&&e.target!==t.navigation.prevEl?("next"===t.swipeDirection&&t.slideTo(null!==h?h:p+s),"prev"===t.swipeDirection&&t.slideTo(null!==o?o:p)):e.target===t.navigation.nextEl?t.slideTo(p+s):t.slideTo(p):t.slideTo(t.activeIndex)}}else a.isTouched=!1,a.isMoved=!1,a.startMoving=!1}else a.isMoved&&r.grabCursor&&t.setGrabCursor(!1),a.isMoved=!1,a.startMoving=!1}}.bind(e),e.onDocumentTouchStart=function(){this.documentTouchHandlerProceeded||(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}.bind(e),t.cssMode&&(e.onScroll=function(){var e=this,t=e.wrapperEl,a=e.rtlTranslate;e.enabled&&(e.previousTranslate=e.translate,e.isHorizontal()?e.translate=-t.scrollLeft:e.translate=-t.scrollTop,0===e.translate&&(e.translate=0),e.updateActiveIndex(),e.updateSlidesClasses(),(0==(t=e.maxTranslate()-e.minTranslate())?0:(e.translate-e.minTranslate())/t)!==e.progress&&e.updateProgress(a?-e.translate:e.translate),e.emit("setTranslate",e.translate,!1))}.bind(e)),e.onClick=function(e){var t=this;t.enabled&&!t.allowClick&&(t.params.preventClicks&&e.preventDefault(),t.params.preventClicksPropagation)&&t.animating&&(e.stopPropagation(),e.stopImmediatePropagation())}.bind(e),e.onLoad=function(e){var t=this;s(t,e.target),t.params.cssMode||"auto"!==t.params.slidesPerView&&!t.params.autoHeight||t.update()}.bind(e),j(e,"on")},detachEvents:function(){j(this,"off")}},breakpoints:{setBreakpoint:function(){var e,r,t,a,i,s,n=this,o=n.realIndex,l=n.initialized,d=n.params,c=n.el,p=d.breakpoints;p&&0!==Object.keys(p).length&&(a=I(),e="window"!==d.breakpointsBase&&d.breakpointsBase?"container":d.breakpointsBase,a=["window","container"].includes(d.breakpointsBase)||!d.breakpointsBase?n.el:a.querySelector(d.breakpointsBase),e=n.getBreakpoint(p,e,a))&&n.currentBreakpoint!==e&&(r=(e in p?p[e]:void 0)||n.originalParams,a=q(n,d),p=q(n,r),i=n.params.grabCursor,s=r.grabCursor,t=d.enabled,a&&!p?(c.classList.remove("".concat(d.containerModifierClass,"grid"),"".concat(d.containerModifierClass,"grid-column")),n.emitContainerClasses()):!a&&p&&(c.classList.add("".concat(d.containerModifierClass,"grid")),(r.grid.fill&&"column"===r.grid.fill||!r.grid.fill&&"column"===d.grid.fill)&&c.classList.add("".concat(d.containerModifierClass,"grid-column")),n.emitContainerClasses()),i&&!s?n.unsetGrabCursor():!i&&s&&n.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(function(e){var t,a;void 0!==r[e]&&(t=d[e]&&d[e].enabled,a=r[e]&&r[e].enabled,t&&!a&&n[e].disable(),!t)&&a&&n[e].enable()}),a=r.direction&&r.direction!==d.direction,p=d.loop&&(r.slidesPerView!==d.slidesPerView||a),c=d.loop,a&&l&&n.changeDirection(),v(n.params,r),i=n.params.enabled,s=n.params.loop,Object.assign(n,{allowTouchMove:n.params.allowTouchMove,allowSlideNext:n.params.allowSlideNext,allowSlidePrev:n.params.allowSlidePrev}),t&&!i?n.disable():!t&&i&&n.enable(),n.currentBreakpoint=e,n.emit("_beforeBreakpoint",r),l&&(p?(n.loopDestroy(),n.loopCreate(o),n.updateSlides()):!c&&s?(n.loopCreate(o),n.updateSlides()):c&&!s&&n.loopDestroy()),n.emit("breakpoint",r))},getBreakpoint:function(e,t,a){if(void 0===t&&(t="window"),e&&("container"!==t||a)){var r=!1,i=G(),s="window"===t?i.innerHeight:a.clientHeight,n=Object.keys(e).map(function(e){var t;return"string"==typeof e&&0===e.indexOf("@")?(t=parseFloat(e.substr(1)),{value:s*t,point:e}):{value:e,point:e}});n.sort(function(e,t){return parseInt(e.value,10)-parseInt(t.value,10)});for(var o=0;o<n.length;o+=1){var l=n[o],d=l.point,l=l.value;"window"===t?i.matchMedia("(min-width: ".concat(l,"px)")).matches&&(r=d):l<=a.clientWidth&&(r=d)}return r||"max"}}},checkOverflow:{checkOverflow:function(){var e,t=this,a=t.isLocked,r=t.params,i=r.slidesOffsetBefore;i?(e=t.slides.length-1,e=t.slidesGrid[e]+t.slidesSizesGrid[e]+2*i,t.isLocked=t.size>e):t.isLocked=1===t.snapGrid.length,!0===r.allowSlideNext&&(t.allowSlideNext=!t.isLocked),!0===r.allowSlidePrev&&(t.allowSlidePrev=!t.isLocked),a&&a!==t.isLocked&&(t.isEnd=!1),a!==t.isLocked&&t.emit(t.isLocked?"lock":"unlock")}},classes:{addClasses:function(){var a,r,e=this,t=e.classNames,i=e.params,s=e.rtl,n=e.el,o=e.device,o=(s=["initialized",i.direction,{"free-mode":e.params.freeMode&&i.freeMode.enabled},{autoheight:i.autoHeight},{rtl:s},{grid:i.grid&&1<i.grid.rows},{"grid-column":i.grid&&1<i.grid.rows&&"column"===i.grid.fill},{android:o.android},{ios:o.ios},{"css-mode":i.cssMode},{centered:i.cssMode&&i.centeredSlides},{"watch-progress":i.watchSlidesProgress}],a=i.containerModifierClass,r=[],s.forEach(function(t){"object"==_typeof(t)?Object.keys(t).forEach(function(e){t[e]&&r.push(a+e)}):"string"==typeof t&&r.push(a+t)}),r);t.push.apply(t,_toConsumableArray(o)),(i=n.classList).add.apply(i,_toConsumableArray(t)),e.emitContainerClasses()},removeClasses:function(){var e=this.el,t=this.classNames;e&&"string"!=typeof e&&((e=e.classList).remove.apply(e,_toConsumableArray(t)),this.emitContainerClasses())}}},F={},o=function(){function f(){var e,t;_classCallCheck(this,f);for(var a=arguments.length,r=new Array(a),i=0;i<a;i++)r[i]=arguments[i];t=1===r.length&&r[0].constructor&&"Object"===Object.prototype.toString.call(r[0]).slice(8,-1)?r[0]:(e=(n=_slicedToArray(r,2))[0],n[1]),t=v({},t=t||{}),e&&!t.el&&(t.el=e);var s,n=I();if(t.el&&"string"==typeof t.el&&1<n.querySelectorAll(t.el).length)return s=[],n.querySelectorAll(t.el).forEach(function(e){e=v({},t,{el:e});s.push(new f(e))}),s;var o,l,d,c=this;function p(){var e=o.navigator.userAgent.toLowerCase();return 0<=e.indexOf("safari")&&e.indexOf("chrome")<0&&e.indexOf("android")<0}c.__swiper__=!0,c.support=E(),c.device=_({userAgent:t.userAgent}),c.browser=(h||(o=G(),n=_(),l=!1,p()&&(m=String(o.navigator.userAgent)).includes("Version/")&&(d=(m=_slicedToArray(m.split("Version/")[1].split(" ")[0].split(".").map(function(e){return Number(e)}),2))[0],m=m[1],l=d<16||16===d&&m<2),d=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(o.navigator.userAgent),m=p(),h={isSafari:l||m,needPerspectiveFix:l,need3dFix:m||d&&n.ios,isWebView:d}),h),c.eventsListeners={},c.eventsAnyListeners=[],c.modules=_toConsumableArray(c.__modules__),t.modules&&Array.isArray(t.modules)&&(l=c.modules).push.apply(l,_toConsumableArray(t.modules));var u={},m=(c.modules.forEach(function(e){var r,i;e({params:t,swiper:c,extendParams:(r=t,i=u,function(e){void 0===e&&(e={});var t=Object.keys(e)[0],a=e[t];"object"==_typeof(a)&&null!==a&&(!0===r[t]&&(r[t]={enabled:!0}),"navigation"===t&&r[t]&&r[t].enabled&&!r[t].prevEl&&!r[t].nextEl&&(r[t].auto=!0),0<=["pagination","scrollbar"].indexOf(t)&&r[t]&&r[t].enabled&&!r[t].el&&(r[t].auto=!0),t in r)&&"enabled"in a&&("object"!=_typeof(r[t])||"enabled"in r[t]||(r[t].enabled=!0),r[t]||(r[t]={enabled:!1})),v(i,e)}),on:c.on.bind(c),once:c.once.bind(c),off:c.off.bind(c),emit:c.emit.bind(c)})}),v({},V,u));return c.params=v({},m,F,t),c.originalParams=v({},c.params),c.passedParams=v({},t),c.params&&c.params.on&&Object.keys(c.params.on).forEach(function(e){c.on(e,c.params.on[e])}),c.params&&c.params.onAny&&c.onAny(c.params.onAny),Object.assign(c,{enabled:c.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:function(){return"horizontal"===c.params.direction},isVertical:function(){return"vertical"===c.params.direction},activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment:function(){return Math.trunc(this.translate/Math.pow(2,23))*Math.pow(2,23)},allowSlideNext:c.params.allowSlideNext,allowSlidePrev:c.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:c.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:c.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),c.emit("_swiper"),c.params.init&&c.init(),c}return _createClass(f,[{key:"getDirectionLabel",value:function(e){return this.isHorizontal()?e:{width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"}[e]}},{key:"getSlideIndex",value:function(e){var t=this.slidesEl,a=this.params,t=P(Q(t,".".concat(a.slideClass,", swiper-slide"))[0]);return P(e)-t}},{key:"getSlideIndexByData",value:function(t){return this.getSlideIndex(this.slides.find(function(e){return+e.getAttribute("data-swiper-slide-index")===t}))}},{key:"recalcSlides",value:function(){var e=this.slidesEl,t=this.params;this.slides=Q(e,".".concat(t.slideClass,", swiper-slide"))}},{key:"enable",value:function(){var e=this;e.enabled||(e.enabled=!0,e.params.grabCursor&&e.setGrabCursor(),e.emit("enable"))}},{key:"disable",value:function(){var e=this;e.enabled&&(e.enabled=!1,e.params.grabCursor&&e.unsetGrabCursor(),e.emit("disable"))}},{key:"setProgress",value:function(e,t){var a=this,r=(e=Math.min(Math.max(e,0),1),a.minTranslate()),e=(a.maxTranslate()-r)*e+r;a.translateTo(e,void 0===t?0:t),a.updateActiveIndex(),a.updateSlidesClasses()}},{key:"emitContainerClasses",value:function(){var e,t=this;t.params._emitClasses&&t.el&&(e=t.el.className.split(" ").filter(function(e){return 0===e.indexOf("swiper")||0===e.indexOf(t.params.containerModifierClass)}),t.emit("_containerClasses",e.join(" ")))}},{key:"getSlideClasses",value:function(e){var t=this;return t.destroyed?"":e.className.split(" ").filter(function(e){return 0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)}).join(" ")}},{key:"emitSlidesClasses",value:function(){var a,r=this;r.params._emitClasses&&r.el&&(a=[],r.slides.forEach(function(e){var t=r.getSlideClasses(e);a.push({slideEl:e,classNames:t}),r.emit("_slideClass",e,t)}),r.emit("_slideClasses",a))}},{key:"slidesPerViewDynamic",value:function(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);var a=this.params,r=this.slides,i=this.slidesGrid,s=this.slidesSizesGrid,n=this.size,o=this.activeIndex,l=1;if("number"==typeof a.slidesPerView)return a.slidesPerView;if(a.centeredSlides){for(var d,c=r[o]?Math.ceil(r[o].swiperSlideSize):0,p=o+1;p<r.length;p+=1)r[p]&&!d&&(l+=1,n<(c+=Math.ceil(r[p].swiperSlideSize)))&&(d=!0);for(var u=o-1;0<=u;--u)r[u]&&!d&&(l+=1,n<(c+=r[u].swiperSlideSize))&&(d=!0)}else if("current"===e)for(var m=o+1;m<r.length;m+=1)(t?i[m]+s[m]-i[o]<n:i[m]-i[o]<n)&&(l+=1);else for(var f=o-1;0<=f;--f)i[o]-i[f]<n&&(l+=1);return l}},{key:"update",value:function(){var e,t,a,r=this;function i(){var e=r.rtlTranslate?-1*r.translate:r.translate,e=Math.min(Math.max(e,r.maxTranslate()),r.minTranslate());r.setTranslate(e),r.updateActiveIndex(),r.updateSlidesClasses()}r&&!r.destroyed&&(e=r.snapGrid,(t=r.params).breakpoints&&r.setBreakpoint(),_toConsumableArray(r.el.querySelectorAll('[loading="lazy"]')).forEach(function(e){e.complete&&s(r,e)}),r.updateSize(),r.updateSlides(),r.updateProgress(),r.updateSlidesClasses(),t.freeMode&&t.freeMode.enabled&&!t.cssMode?(i(),t.autoHeight&&r.updateAutoHeight()):(("auto"===t.slidesPerView||1<t.slidesPerView)&&r.isEnd&&!t.centeredSlides?(a=(r.virtual&&t.virtual.enabled?r.virtual:r).slides,r.slideTo(a.length-1,0,!1,!0)):r.slideTo(r.activeIndex,0,!1,!0))||i(),t.watchOverflow&&e!==r.snapGrid&&r.checkOverflow(),r.emit("update"))}},{key:"changeDirection",value:function(t,e){void 0===e&&(e=!0);var a=this,r=a.params.direction;return(t=t||("horizontal"===r?"vertical":"horizontal"))===r||"horizontal"!==t&&"vertical"!==t||(a.el.classList.remove("".concat(a.params.containerModifierClass).concat(r)),a.el.classList.add("".concat(a.params.containerModifierClass).concat(t)),a.emitContainerClasses(),a.params.direction=t,a.slides.forEach(function(e){"vertical"===t?e.style.width="":e.style.height=""}),a.emit("changeDirection"),e&&a.update()),a}},{key:"changeLanguageDirection",value:function(e){var t=this;t.rtl&&"rtl"===e||!t.rtl&&"ltr"===e||(t.rtl="rtl"===e,t.rtlTranslate="horizontal"===t.params.direction&&t.rtl,t.rtl?(t.el.classList.add("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="rtl"):(t.el.classList.remove("".concat(t.params.containerModifierClass,"rtl")),t.el.dir="ltr"),t.update())}},{key:"mount",value:function(e){var t=this;if(!t.mounted){e=e||t.params.el;if(!(e="string"==typeof e?document.querySelector(e):e))return!1;e.swiper=t,e.parentNode&&e.parentNode.host&&e.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);var a=function(){return".".concat((t.params.wrapperClass||"").trim().split(" ").join("."))},r=e&&e.shadowRoot&&e.shadowRoot.querySelector?e.shadowRoot.querySelector(a()):Q(e,a())[0];!r&&t.params.createElements&&(r=C("div",t.params.wrapperClass),e.append(r),Q(e,".".concat(t.params.slideClass)).forEach(function(e){r.append(e)})),Object.assign(t,{el:e,wrapperEl:r,slidesEl:t.isElement&&!e.parentNode.host.slideSlots?e.parentNode.host:r,hostEl:t.isElement?e.parentNode.host:e,mounted:!0,rtl:"rtl"===e.dir.toLowerCase()||"rtl"===J(e,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===e.dir.toLowerCase()||"rtl"===J(e,"direction")),wrongRTL:"-webkit-box"===J(r,"display")})}return!0}},{key:"init",value:function(e){var t=this;return t.initialized||!1!==t.mount(e)&&(t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(),t.attachEvents(),e=_toConsumableArray(t.el.querySelectorAll('[loading="lazy"]')),t.isElement&&e.push.apply(e,_toConsumableArray(t.hostEl.querySelectorAll('[loading="lazy"]'))),e.forEach(function(e){e.complete?s(t,e):e.addEventListener("load",function(e){s(t,e.target)})}),c(t),t.initialized=!0,c(t),t.emit("init"),t.emit("afterInit")),t}},{key:"destroy",value:function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);var a,r=this,i=r.params,s=r.el,n=r.wrapperEl,o=r.slides;return void 0===r.params||r.destroyed||(r.emit("beforeDestroy"),r.initialized=!1,r.detachEvents(),i.loop&&r.loopDestroy(),t&&(r.removeClasses(),s&&"string"!=typeof s&&s.removeAttribute("style"),n&&n.removeAttribute("style"),o)&&o.length&&o.forEach(function(e){e.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")}),r.emit("destroy"),Object.keys(r.eventsListeners).forEach(function(e){r.off(e)}),!1!==e&&(r.el&&"string"!=typeof r.el&&(r.el.swiper=null),a=r,Object.keys(a).forEach(function(e){try{a[e]=null}catch(e){}try{delete a[e]}catch(e){}})),r.destroyed=!0),null}}],[{key:"extendDefaults",value:function(e){v(F,e)}},{key:"extendedDefaults",get:function(){return F}},{key:"defaults",get:function(){return V}},{key:"installModule",value:function(e){var t=f.prototype.__modules__=f.prototype.__modules__?f.prototype.__modules__:[];"function"==typeof e&&t.indexOf(e)<0&&t.push(e)}},{key:"use",value:function(e){return Array.isArray(e)?e.forEach(function(e){return f.installModule(e)}):f.installModule(e),f}}])}();function W(a,r,i,s){return a.params.createElements&&Object.keys(s).forEach(function(e){var t;i[e]||!0!==i.auto||((t=Q(a.el,".".concat(s[e]))[0])||((t=C("div",s[e])).className=s[e],a.el.append(t)),i[e]=t,r[e]=t)}),i}function k(e){return".".concat((e=void 0===e?"":e).trim().replace(/([\.:!+\/])/g,"\\$1").replace(/ /g,"."))}function U(e){function t(e){var t;"string"==typeof e?((t=document.createElement("div")).innerHTML=e,i.append(t.children[0]),t.innerHTML=""):i.append(e)}var a=this,r=a.params,i=a.slidesEl;r.loop&&a.loopDestroy();if("object"==_typeof(e)&&"length"in e)for(var s=0;s<e.length;s+=1)e[s]&&t(e[s]);else t(e);a.recalcSlides(),r.loop&&a.loopCreate(),r.observer&&!a.isElement||a.update()}function K(e){function t(e){var t;"string"==typeof e?((t=document.createElement("div")).innerHTML=e,s.prepend(t.children[0]),t.innerHTML=""):s.prepend(e)}var a=this,r=a.params,i=a.activeIndex,s=a.slidesEl,n=(r.loop&&a.loopDestroy(),i+1);if("object"==_typeof(e)&&"length"in e){for(var o=0;o<e.length;o+=1)e[o]&&t(e[o]);n=i+e.length}else t(e);a.recalcSlides(),r.loop&&a.loopCreate(),r.observer&&!a.isElement||a.update(),a.slideTo(n,0,!1)}function l(e){var t,a=e.effect,r=e.swiper,i=e.on,s=e.setTranslate,n=e.setTransition,o=e.overwriteParams,l=e.perspective,d=e.recreateShadows,c=e.getEffectParams;i("beforeInit",function(){var e;r.params.effect===a&&(r.classNames.push("".concat(r.params.containerModifierClass).concat(a)),l&&l()&&r.classNames.push("".concat(r.params.containerModifierClass,"3d")),e=o?o():{},Object.assign(r.params,e),Object.assign(r.originalParams,e))}),i("setTranslate",function(){r.params.effect===a&&s()}),i("setTransition",function(e,t){r.params.effect===a&&n(t)}),i("transitionEnd",function(){r.params.effect===a&&d&&c&&c().slideShadows&&(r.slides.forEach(function(e){e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){return e.remove()})}),d())}),i("virtualUpdate",function(){r.params.effect===a&&(r.slides.length||(t=!0),requestAnimationFrame(function(){t&&r.slides&&r.slides.length&&(s(),t=!1)}))})}function x(e,t){var a=i(t);return a!==t&&(a.style.backfaceVisibility="hidden",a.style["-webkit-backface-visibility"]="hidden"),a}function d(e){var t,a=e.swiper,r=e.duration,i=e.transformElements,e=e.allSlides,s=a.activeIndex;a.params.virtualTranslate&&0!==r&&(t=!1,(e?i:i.filter(function(e){var t,e=e.classList.contains("swiper-slide-transform")?(t=e).parentElement||a.slides.find(function(e){return e.shadowRoot&&e.shadowRoot===t.parentNode}):e;return a.getSlideIndex(e)===s})).forEach(function(e){g(e,function(){var e;t||a&&!a.destroyed&&(t=!0,a.animating=!1,e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0}),a.wrapperEl.dispatchEvent(e))})}))}function S(e,t,a){a="swiper-slide-shadow".concat(a?"-".concat(a):"").concat(e?" swiper-slide-shadow-".concat(e):""),e=i(t),t=e.querySelector(".".concat(a.split(" ").join(".")));return t||(t=C("div",a.split(" ")),e.append(t)),t}return Object.keys(n).forEach(function(t){Object.keys(n[t]).forEach(function(e){o.prototype[e]=n[t][e]})}),o.use([function(e){function s(){n&&!n.destroyed&&n.initialized&&(r("beforeResize"),r("resize"))}function t(){n&&!n.destroyed&&n.initialized&&r("orientationchange")}var n=e.swiper,a=e.on,r=e.emit,i=G(),o=null,l=null;a("init",function(){n.params.resizeObserver&&void 0!==i.ResizeObserver?n&&!n.destroyed&&n.initialized&&(o=new ResizeObserver(function(a){l=i.requestAnimationFrame(function(){var e=n.width,t=n.height,r=e,i=t;a.forEach(function(e){var t=e.contentBoxSize,a=e.contentRect,e=e.target;e&&e!==n.el||(r=a?a.width:(t[0]||t).inlineSize,i=a?a.height:(t[0]||t).blockSize)}),r===e&&i===t||s()})})).observe(n.el):(i.addEventListener("resize",s),i.addEventListener("orientationchange",t))}),a("destroy",function(){l&&i.cancelAnimationFrame(l),o&&o.unobserve&&n.el&&(o.unobserve(n.el),o=null),i.removeEventListener("resize",s),i.removeEventListener("orientationchange",t)})},function(e){function a(e,t){void 0===t&&(t={});var a=new(o.MutationObserver||o.WebkitMutationObserver)(function(e){var t;r.__preventObserver__||(1===e.length?s("observerUpdate",e[0]):(t=function(){s("observerUpdate",e[0])},o.requestAnimationFrame?o.requestAnimationFrame(t):o.setTimeout(t,0)))});a.observe(e,{attributes:void 0===t.attributes||t.attributes,childList:r.isElement||(void 0===t.childList||t).childList,characterData:void 0===t.characterData||t.characterData}),n.push(a)}var r=e.swiper,t=e.extendParams,i=e.on,s=e.emit,n=[],o=G();t({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",function(){if(r.params.observer){if(r.params.observeParents)for(var e=X(r.hostEl),t=0;t<e.length;t+=1)a(e[t]);a(r.hostEl,{childList:r.params.observeSlideChildren}),a(r.wrapperEl,{attributes:!1})}}),i("destroy",function(){n.forEach(function(e){e.disconnect()}),n.splice(0,n.length)})}]),o.use([function(e){var t,A=e.swiper,a=e.extendParams,r=e.on,L=e.emit,e=(a({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}}),I()),i=(A.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]},e.createElement("div"));function k(e,t){var a,r=A.params.virtual;return r.cache&&A.virtual.cache[t]?A.virtual.cache[t]:(r.renderSlide?"string"==typeof(a=r.renderSlide.call(A,e,t))&&(i.innerHTML=a,a=i.children[0]):a=A.isElement?C("swiper-slide"):C("div",A.params.slideClass),a.setAttribute("data-swiper-slide-index",t),r.renderSlide||(a.innerHTML=e),r.cache&&(A.virtual.cache[t]=a),a)}function o(e,t){var a=A.params,r=a.slidesPerView,i=a.slidesPerGroup,s=a.centeredSlides,n=a.loop,a=a.initialSlide;if(!(t&&!n&&0<a)){var o,t=A.params.virtual,a=t.addSlidesBefore,t=t.addSlidesAfter,l=A.virtual,d=l.from,c=l.to,p=l.slides,u=l.slidesGrid,l=l.offset,m=(A.params.cssMode||A.updateActiveIndex(),A.activeIndex||0),f=A.rtlTranslate?"right":A.isHorizontal()?"left":"top",t=s?(o=Math.floor(r/2)+i+t,Math.floor(r/2)+i+a):(o=r+(i-1)+t,(n?r:i)+a),h=m-t,v=m+o,g=(n||(h=Math.max(h,0),v=Math.min(v,p.length-1)),(A.slidesGrid[h]||0)-(A.slidesGrid[0]||0));if(n&&t<=m?(h-=t,s||(g+=A.slidesGrid[0])):n&&m<t&&(h=-t,s)&&(g+=A.slidesGrid[0]),Object.assign(A.virtual,{from:h,to:v,offset:g,slidesGrid:A.slidesGrid,slidesBefore:t,slidesAfter:o}),d!==h||c!==v||e)if(A.params.virtual.renderExternal)A.params.virtual.renderExternal.call(A,{offset:g,from:h,to:v,slides:function(){for(var e=[],t=h;t<=v;t+=1)e.push(p[t]);return e}()}),A.params.virtual.renderExternalUpdate?P():L("virtualUpdate");else{var y=[],b=[],w=function(e){var t=e;return e<0?t=p.length+e:t>=p.length&&(t-=p.length),t};if(e)A.slides.filter(function(e){return e.matches(".".concat(A.params.slideClass,", swiper-slide"))}).forEach(function(e){e.remove()});else for(var E=d;E<=c;E+=1)!function(){var t;(E<h||v<E)&&(t=w(E),A.slides.filter(function(e){return e.matches(".".concat(A.params.slideClass,'[data-swiper-slide-index="').concat(t,'"], swiper-slide[data-swiper-slide-index="').concat(t,'"]'))}).forEach(function(e){e.remove()}))}();for(var x,r=n?-p.length:0,S=n?2*p.length:p.length,T=r;T<S;T+=1)h<=T&&T<=v&&(x=w(T),void 0===c||e?b.push(x):(c<T&&b.push(x),T<d&&y.push(x)));if(b.forEach(function(e){A.slidesEl.append(k(p[e],e))}),n)for(var M=y.length-1;0<=M;--M){var C=y[M];A.slidesEl.prepend(k(p[C],C))}else y.sort(function(e,t){return t-e}),y.forEach(function(e){A.slidesEl.prepend(k(p[e],e))});Q(A.slidesEl,".swiper-slide, swiper-slide").forEach(function(e){e.style[f]=g-Math.abs(A.cssOverflowAdjustment())+"px"}),P()}else A.slidesGrid!==u&&g!==l&&A.slides.forEach(function(e){e.style[f]=g-Math.abs(A.cssOverflowAdjustment())+"px"}),A.updateProgress(),L("virtualUpdate")}function P(){A.updateSlides(),A.updateProgress(),A.updateSlidesClasses(),L("virtualUpdate")}}r("beforeInit",function(){var e,t;A.params.virtual.enabled&&(void 0===A.passedParams.virtual.slides&&(t=_toConsumableArray(A.slidesEl.children).filter(function(e){return e.matches(".".concat(A.params.slideClass,", swiper-slide"))}))&&t.length&&(A.virtual.slides=_toConsumableArray(t),e=!0,t.forEach(function(e,t){e.setAttribute("data-swiper-slide-index",t),(A.virtual.cache[t]=e).remove()})),e||(A.virtual.slides=A.params.virtual.slides),A.classNames.push("".concat(A.params.containerModifierClass,"virtual")),A.params.watchSlidesProgress=!0,A.originalParams.watchSlidesProgress=!0,o(!1,!0))}),r("setTranslate",function(){A.params.virtual.enabled&&(A.params.cssMode&&!A._immediateVirtual?(clearTimeout(t),t=setTimeout(function(){o()},100)):o())}),r("init update resize",function(){A.params.virtual.enabled&&A.params.cssMode&&$(A.wrapperEl,"--swiper-virtual-size","".concat(A.virtualSize,"px"))}),Object.assign(A.virtual,{appendSlide:function(e){if("object"==_typeof(e)&&"length"in e)for(var t=0;t<e.length;t+=1)e[t]&&A.virtual.slides.push(e[t]);else A.virtual.slides.push(e);o(!0)},prependSlide:function(e){var r,i,t=A.activeIndex,a=t+1,s=1;if(Array.isArray(e)){for(var n=0;n<e.length;n+=1)e[n]&&A.virtual.slides.unshift(e[n]);a=t+e.length,s=e.length}else A.virtual.slides.unshift(e);A.params.virtual.cache&&(r=A.virtual.cache,i={},Object.keys(r).forEach(function(e){var t=r[e],a=t.getAttribute("data-swiper-slide-index");a&&t.setAttribute("data-swiper-slide-index",parseInt(a,10)+s),i[parseInt(e,10)+s]=t}),A.virtual.cache=i),o(!0),A.slideTo(a,0)},removeSlide:function(t){if(null!=t){var e=A.activeIndex;if(Array.isArray(t))for(var a=t.length-1;0<=a;--a)A.params.virtual.cache&&(delete A.virtual.cache[t[a]],Object.keys(A.virtual.cache).forEach(function(e){t<e&&(A.virtual.cache[e-1]=A.virtual.cache[e],A.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete A.virtual.cache[e])})),A.virtual.slides.splice(t[a],1),t[a]<e&&--e,e=Math.max(e,0);else A.params.virtual.cache&&(delete A.virtual.cache[t],Object.keys(A.virtual.cache).forEach(function(e){t<e&&(A.virtual.cache[e-1]=A.virtual.cache[e],A.virtual.cache[e-1].setAttribute("data-swiper-slide-index",e-1),delete A.virtual.cache[e])})),A.virtual.slides.splice(t,1),t<e&&--e,e=Math.max(e,0);o(!0),A.slideTo(e,0)}},removeAllSlides:function(){A.virtual.slides=[],A.params.virtual.cache&&(A.virtual.cache={}),o(!0),A.slideTo(0,0)},update:o})},function(e){var b=e.swiper,t=e.extendParams,a=e.on,w=e.emit,E=I(),x=G();function r(e){if(b.enabled){var t=b.rtlTranslate,a=(e=e.originalEvent?e.originalEvent:e).keyCode||e.charCode,r=b.params.keyboard.pageUpDown,i=r&&33===a,r=r&&34===a,s=37===a,n=39===a,o=38===a,l=40===a;if(!b.allowSlideNext&&(b.isHorizontal()&&n||b.isVertical()&&l||r))return!1;if(!b.allowSlidePrev&&(b.isHorizontal()&&s||b.isVertical()&&o||i))return!1;if(!(e.shiftKey||e.altKey||e.ctrlKey||e.metaKey||E.activeElement&&E.activeElement.nodeName&&("input"===E.activeElement.nodeName.toLowerCase()||"textarea"===E.activeElement.nodeName.toLowerCase()))){if(b.params.keyboard.onlyInViewport&&(i||r||s||n||o||l)){var d=!1;if(0<X(b.el,".".concat(b.params.slideClass,", swiper-slide")).length&&0===X(b.el,".".concat(b.params.slideActiveClass)).length)return;for(var c=b.el,p=c.clientWidth,u=c.clientHeight,m=x.innerWidth,f=x.innerHeight,h=B(c),v=(t&&(h.left-=c.scrollLeft),[[h.left,h.top],[h.left+p,h.top],[h.left,h.top+u],[h.left+p,h.top+u]]),g=0;g<v.length;g+=1){var y=v[g];0<=y[0]&&y[0]<=m&&0<=y[1]&&y[1]<=f&&(0===y[0]&&0===y[1]||(d=!0))}if(!d)return}b.isHorizontal()?((i||r||s||n)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),((r||n)&&!t||(i||s)&&t)&&b.slideNext(),((i||s)&&!t||(r||n)&&t)&&b.slidePrev()):((i||r||o||l)&&(e.preventDefault?e.preventDefault():e.returnValue=!1),(r||l)&&b.slideNext(),(i||o)&&b.slidePrev()),w("keyPress",a)}}}function i(){b.keyboard.enabled||(E.addEventListener("keydown",r),b.keyboard.enabled=!0)}function s(){b.keyboard.enabled&&(E.removeEventListener("keydown",r),b.keyboard.enabled=!1)}t({keyboard:{enabled:!(b.keyboard={enabled:!1}),onlyInViewport:!0,pageUpDown:!0}}),a("init",function(){b.params.keyboard.enabled&&i()}),a("destroy",function(){b.keyboard.enabled&&s()}),Object.assign(b.keyboard,{enable:i,disable:s})},function(e){var m,f=e.swiper,t=e.extendParams,a=e.on,h=e.emit,r=G();t({mousewheel:{enabled:!1,releaseOnEdges:!1,invert:!1,forceToAxis:!1,sensitivity:1,eventsTarget:"container",thresholdDelta:null,thresholdTime:null,noMousewheelClass:"swiper-no-mousewheel"}}),f.mousewheel={enabled:!1};var v,i=b(),g=[];function s(){f.enabled&&(f.mouseEntered=!0)}function n(){f.enabled&&(f.mouseEntered=!1)}function y(e){f.params.mousewheel.thresholdDelta&&e.delta<f.params.mousewheel.thresholdDelta||f.params.mousewheel.thresholdTime&&b()-i<f.params.mousewheel.thresholdTime||6<=e.delta&&b()-i<60||(e.direction<0?f.isEnd&&!f.params.loop||f.animating||(f.slideNext(),h("scroll",e.raw)):f.isBeginning&&!f.params.loop||f.animating||(f.slidePrev(),h("scroll",e.raw)),i=(new r.Date).getTime())}function o(e){var t=e;if(f.enabled&&!e.target.closest(".".concat(f.params.mousewheel.noMousewheelClass))){var a=f.params.mousewheel,r=(f.params.cssMode&&t.preventDefault(),f.el),r=(r="container"!==f.params.mousewheel.eventsTarget?document.querySelector(f.params.mousewheel.eventsTarget):r)&&r.contains(t.target);if(!f.mouseEntered&&!r&&!a.releaseOnEdges)return!0;t.originalEvent&&(t=t.originalEvent);var r=0,i=f.rtlTranslate?-1:1,s=(p=c=l=n=0,"detail"in(s=t)&&(l=s.detail),"wheelDelta"in s&&(l=-s.wheelDelta/120),"wheelDeltaY"in s&&(l=-s.wheelDeltaY/120),"wheelDeltaX"in s&&(n=-s.wheelDeltaX/120),"axis"in s&&s.axis===s.HORIZONTAL_AXIS&&(n=l,l=0),c=10*n,p=10*l,"deltaY"in s&&(p=s.deltaY),"deltaX"in s&&(c=s.deltaX),s.shiftKey&&!c&&(c=p,p=0),(c||p)&&s.deltaMode&&(1===s.deltaMode?(c*=40,p*=40):(c*=800,p*=800)),{spinX:n=c&&!n?c<1?-1:1:n,spinY:l=p&&!l?p<1?-1:1:l,pixelX:c,pixelY:p});if(a.forceToAxis)if(f.isHorizontal()){if(!(Math.abs(s.pixelX)>Math.abs(s.pixelY)))return!0;r=-s.pixelX*i}else{if(!(Math.abs(s.pixelY)>Math.abs(s.pixelX)))return!0;r=-s.pixelY}else r=Math.abs(s.pixelX)>Math.abs(s.pixelY)?-s.pixelX*i:-s.pixelY;if(0===r)return!0;a.invert&&(r=-r);var n=f.getTranslate()+r*a.sensitivity;if((n=n>=f.minTranslate()?f.minTranslate():n)<=f.maxTranslate()&&(n=f.maxTranslate()),(!!f.params.loop||!(n===f.minTranslate()||n===f.maxTranslate()))&&f.params.nested&&t.stopPropagation(),f.params.freeMode&&f.params.freeMode.enabled){var o={time:b(),delta:Math.abs(r),direction:Math.sign(r)},l=v&&o.time<v.time+500&&o.delta<=v.delta&&o.direction===v.direction;if(!l){v=void 0;var d,c=f.getTranslate()+r*a.sensitivity,p=f.isBeginning,i=f.isEnd;if((c=c>=f.minTranslate()?f.minTranslate():c)<=f.maxTranslate()&&(c=f.maxTranslate()),f.setTransition(0),f.setTranslate(c),f.updateProgress(),f.updateActiveIndex(),f.updateSlidesClasses(),(!p&&f.isBeginning||!i&&f.isEnd)&&f.updateSlidesClasses(),f.params.loop&&f.loopFix({direction:o.direction<0?"next":"prev",byMousewheel:!0}),f.params.freeMode.sticky&&(clearTimeout(m),m=void 0,15<=g.length&&g.shift(),s=g.length?g[g.length-1]:void 0,n=g[0],g.push(o),s&&(o.delta>s.delta||o.direction!==s.direction)?g.splice(0):15<=g.length&&o.time-n.time<500&&1<=n.delta-o.delta&&o.delta<=6&&(d=0<r?.8:.2,v=o,g.splice(0),m=M(function(){!f.destroyed&&f.params&&f.slideToClosest(f.params.speed,!0,void 0,d)},0)),m=m||M(function(){!f.destroyed&&f.params&&(v=o,g.splice(0),f.slideToClosest(f.params.speed,!0,void 0,.5))},500)),l||h("scroll",t),f.params.autoplay&&f.params.autoplay.disableOnInteraction&&f.autoplay.stop(),a.releaseOnEdges&&(c===f.minTranslate()||c===f.maxTranslate()))return!0}}else{var u={time:b(),delta:Math.abs(r),direction:Math.sign(r),raw:e},p=(2<=g.length&&g.shift(),g.length?g[g.length-1]:void 0);if(g.push(u),(!p||u.direction!==p.direction||u.delta>p.delta||u.time>p.time+150)&&y(u),function(){var e=f.params.mousewheel;if(u.direction<0){if(f.isEnd&&!f.params.loop&&e.releaseOnEdges)return 1}else if(f.isBeginning&&!f.params.loop&&e.releaseOnEdges)return 1}())return!0}return t.preventDefault?t.preventDefault():t.returnValue=!1,!1}}function l(e){var t=f.el;(t="container"!==f.params.mousewheel.eventsTarget?document.querySelector(f.params.mousewheel.eventsTarget):t)[e]("mouseenter",s),t[e]("mouseleave",n),t[e]("wheel",o)}function d(){return f.params.cssMode?(f.wrapperEl.removeEventListener("wheel",o),!0):!f.mousewheel.enabled&&(l("addEventListener"),f.mousewheel.enabled=!0)}function c(){return f.params.cssMode?(f.wrapperEl.addEventListener(event,o),!0):!!f.mousewheel.enabled&&(l("removeEventListener"),!(f.mousewheel.enabled=!1))}a("init",function(){!f.params.mousewheel.enabled&&f.params.cssMode&&c(),f.params.mousewheel.enabled&&d()}),a("destroy",function(){f.params.cssMode&&d(),f.mousewheel.enabled&&c()}),Object.assign(f.mousewheel,{enable:d,disable:c})},function(e){var o=e.swiper,t=e.extendParams,a=e.on,l=e.emit;function i(e){var t;return!(e&&"string"==typeof e&&o.isElement&&(t=o.el.querySelector(e)||o.hostEl.querySelector(e)))&&(e&&("string"==typeof e&&(t=_toConsumableArray(document.querySelectorAll(e))),o.params.uniqueNavElements&&"string"==typeof e&&t&&1<t.length&&1===o.el.querySelectorAll(e).length?t=o.el.querySelector(e):t&&1===t.length&&(t=t[0])),e)&&!t?e:t}function r(e,a){var r=o.params.navigation;(e=A(e)).forEach(function(e){var t;e&&((t=e.classList)[a?"add":"remove"].apply(t,_toConsumableArray(r.disabledClass.split(" "))),"BUTTON"===e.tagName&&(e.disabled=a),o.params.watchOverflow)&&o.enabled&&e.classList[o.isLocked?"add":"remove"](r.lockClass)})}function s(){var e=o.navigation,t=e.nextEl,e=e.prevEl;o.params.loop?(r(e,!1),r(t,!1)):(r(e,o.isBeginning&&!o.params.rewind),r(t,o.isEnd&&!o.params.rewind))}function n(e){e.preventDefault(),o.isBeginning&&!o.params.loop&&!o.params.rewind||(o.slidePrev(),l("navigationPrev"))}function d(e){e.preventDefault(),o.isEnd&&!o.params.loop&&!o.params.rewind||(o.slideNext(),l("navigationNext"))}function c(){var e,t,a,r=o.params.navigation;o.params.navigation=W(o,o.originalParams.navigation,o.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),(r.nextEl||r.prevEl)&&(e=i(r.nextEl),t=i(r.prevEl),Object.assign(o.navigation,{nextEl:e,prevEl:t}),e=A(e),t=A(t),a=function(e,t){e&&e.addEventListener("click","next"===t?d:n),!o.enabled&&e&&(t=e.classList).add.apply(t,_toConsumableArray(r.lockClass.split(" ")))},e.forEach(function(e){return a(e,"next")}),t.forEach(function(e){return a(e,"prev")}))}function p(){function t(e,t){e.removeEventListener("click","next"===t?d:n),(t=e.classList).remove.apply(t,_toConsumableArray(o.params.navigation.disabledClass.split(" ")))}var e=o.navigation,a=e.nextEl,e=e.prevEl,a=A(a),e=A(e);a.forEach(function(e){return t(e,"next")}),e.forEach(function(e){return t(e,"prev")})}t({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),o.navigation={nextEl:null,prevEl:null},a("init",function(){(!1===o.params.navigation.enabled?u:(c(),s))()}),a("toEdge fromEdge lock unlock",function(){s()}),a("destroy",function(){p()}),a("enable disable",function(){var e=o.navigation,t=e.nextEl,e=e.prevEl,t=A(t),e=A(e);o.enabled?s():[].concat(_toConsumableArray(t),_toConsumableArray(e)).filter(function(e){return!!e}).forEach(function(e){return e.classList.add(o.params.navigation.lockClass)})}),a("click",function(e,t){var a,r=o.navigation,i=r.nextEl,s=r.prevEl,i=A(i),s=A(s),r=t.target,n=s.includes(r)||i.includes(r);o.isElement&&!n&&(t=t.path||t.composedPath&&t.composedPath())&&(n=t.find(function(e){return i.includes(e)||s.includes(e)})),!o.params.navigation.hideOnClick||n||o.pagination&&o.params.pagination&&o.params.pagination.clickable&&(o.pagination.el===r||o.pagination.el.contains(r))||(i.length?a=i[0].classList.contains(o.params.navigation.hiddenClass):s.length&&(a=s[0].classList.contains(o.params.navigation.hiddenClass)),l(!0===a?"navigationShow":"navigationHide"),[].concat(_toConsumableArray(i),_toConsumableArray(s)).filter(function(e){return!!e}).forEach(function(e){return e.classList.toggle(o.params.navigation.hiddenClass)}))});var u=function(){var e;(e=o.el.classList).add.apply(e,_toConsumableArray(o.params.navigation.navigationDisabledClass.split(" "))),p()};Object.assign(o.navigation,{enable:function(){var e;(e=o.el.classList).remove.apply(e,_toConsumableArray(o.params.navigation.navigationDisabledClass.split(" "))),c(),s()},disable:u,update:s,init:c,destroy:p})},function(e){var h,v=e.swiper,t=e.extendParams,a=e.on,g=e.emit,e="swiper-pagination",y=(t({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:function(e){return e},formatFractionTotal:function(e){return e},bulletClass:"".concat(e,"-bullet"),bulletActiveClass:"".concat(e,"-bullet-active"),modifierClass:"".concat(e,"-"),currentClass:"".concat(e,"-current"),totalClass:"".concat(e,"-total"),hiddenClass:"".concat(e,"-hidden"),progressbarFillClass:"".concat(e,"-progressbar-fill"),progressbarOppositeClass:"".concat(e,"-progressbar-opposite"),clickableClass:"".concat(e,"-clickable"),lockClass:"".concat(e,"-lock"),horizontalClass:"".concat(e,"-horizontal"),verticalClass:"".concat(e,"-vertical"),paginationDisabledClass:"".concat(e,"-disabled")}}),v.pagination={el:null,bullets:[]},0);function b(){return!v.params.pagination.el||!v.pagination.el||Array.isArray(v.pagination.el)&&0===v.pagination.el.length}function w(e,t){var a=v.params.pagination.bulletActiveClass;(e=e&&e[("prev"===t?"previous":"next")+"ElementSibling"])&&(e.classList.add("".concat(a,"-").concat(t)),e=e[("prev"===t?"previous":"next")+"ElementSibling"])&&e.classList.add("".concat(a,"-").concat(t,"-").concat(t))}function r(e){var t,a,r=e.target.closest(k(v.params.pagination.bulletClass));r&&(e.preventDefault(),e=P(r)*v.params.slidesPerGroup,v.params.loop?v.realIndex!==e&&(r=v.realIndex,t=e,"next"===(a=(t%=a=v.slides.length)==1+(r%=a)?"next":t==r-1?"previous":void 0)?v.slideNext():"previous"===a?v.slidePrev():v.slideToLoop(e)):v.slideTo(e))}function i(){var e=v.rtl,n=v.params.pagination;if(!b()){var o,t=A(t=v.pagination.el),a=(v.virtual&&v.params.virtual.enabled?v.virtual:v).slides.length,l=v.params.loop?Math.ceil(a/v.params.slidesPerGroup):v.snapGrid.length;if(v.params.loop?(m=v.previousRealIndex||0,o=1<v.params.slidesPerGroup?Math.floor(v.realIndex/v.params.slidesPerGroup):v.realIndex):void 0!==v.snapIndex?(o=v.snapIndex,m=v.previousSnapIndex):(m=v.previousIndex||0,o=v.activeIndex||0),"bullets"===n.type&&v.pagination.bullets&&0<v.pagination.bullets.length){var r,i,s,d,c,p=v.pagination.bullets;if(n.dynamicBullets&&(h=ee(p[0],v.isHorizontal()?"width":"height",!0),t.forEach(function(e){e.style[v.isHorizontal()?"width":"height"]=h*(n.dynamicMainBullets+4)+"px"}),1<n.dynamicMainBullets&&void 0!==m&&((y+=o-(m||0))>n.dynamicMainBullets-1?y=n.dynamicMainBullets-1:y<0&&(y=0)),r=Math.max(o-y,0),s=((i=r+(Math.min(p.length,n.dynamicMainBullets)-1))+r)/2),p.forEach(function(e){var t=_toConsumableArray(["","-next","-next-next","-prev","-prev-prev","-main"].map(function(e){return"".concat(n.bulletActiveClass).concat(e)})).map(function(e){return"string"==typeof e&&e.includes(" ")?e.split(" "):e}).flat();(e=e.classList).remove.apply(e,_toConsumableArray(t))}),1<t.length)p.forEach(function(e){var t,a=P(e);a===o?(t=e.classList).add.apply(t,_toConsumableArray(n.bulletActiveClass.split(" "))):v.isElement&&e.setAttribute("part","bullet"),n.dynamicBullets&&(r<=a&&a<=i&&(t=e.classList).add.apply(t,_toConsumableArray("".concat(n.bulletActiveClass,"-main").split(" "))),a===r&&w(e,"prev"),a===i)&&w(e,"next")});else{var a=p[o];if(a&&(m=a.classList).add.apply(m,_toConsumableArray(n.bulletActiveClass.split(" "))),v.isElement&&p.forEach(function(e,t){e.setAttribute("part",t===o?"bullet-active":"bullet")}),n.dynamicBullets){for(var u,a=p[r],m=p[i],f=r;f<=i;f+=1)p[f]&&(u=p[f].classList).add.apply(u,_toConsumableArray("".concat(n.bulletActiveClass,"-main").split(" ")));w(a,"prev"),w(m,"next")}}n.dynamicBullets&&(a=Math.min(p.length,n.dynamicMainBullets+4),d=(h*a-h)/2-s*h,c=e?"right":"left",p.forEach(function(e){e.style[v.isHorizontal()?c:"top"]="".concat(d,"px")}))}t.forEach(function(e,t){var a,r,i,s;"fraction"===n.type&&(e.querySelectorAll(k(n.currentClass)).forEach(function(e){e.textContent=n.formatFractionCurrent(o+1)}),e.querySelectorAll(k(n.totalClass)).forEach(function(e){e.textContent=n.formatFractionTotal(l)})),"progressbar"===n.type&&(a=n.progressbarOpposite?v.isHorizontal()?"vertical":"horizontal":v.isHorizontal()?"horizontal":"vertical",r=(o+1)/l,s=i=1,"horizontal"==a?i=r:s=r,e.querySelectorAll(k(n.progressbarFillClass)).forEach(function(e){e.style.transform="translate3d(0,0,0) scaleX(".concat(i,") scaleY(").concat(s,")"),e.style.transitionDuration="".concat(v.params.speed,"ms")})),"custom"===n.type&&n.renderCustom?(e.innerHTML=n.renderCustom(v,o+1,l),0===t&&g("paginationRender",e)):(0===t&&g("paginationRender",e),g("paginationUpdate",e)),v.params.watchOverflow&&v.enabled&&e.classList[v.isLocked?"add":"remove"](n.lockClass)})}}function s(){var a=v.params.pagination;if(!b()){var e=v.virtual&&v.params.virtual.enabled?v.virtual.slides.length:v.grid&&1<v.params.grid.rows?v.slides.length/Math.ceil(v.params.grid.rows):v.slides.length,t=A(t=v.pagination.el),r="";if("bullets"===a.type){var i=v.params.loop?Math.ceil(e/v.params.slidesPerGroup):v.snapGrid.length;v.params.freeMode&&v.params.freeMode.enabled&&e<i&&(i=e);for(var s=0;s<i;s+=1)a.renderBullet?r+=a.renderBullet.call(v,s,a.bulletClass):r+="<".concat(a.bulletElement," ").concat(v.isElement?'part="bullet"':"",' class="').concat(a.bulletClass,'"></').concat(a.bulletElement,">")}"fraction"===a.type&&(r=a.renderFraction?a.renderFraction.call(v,a.currentClass,a.totalClass):'<span class="'.concat(a.currentClass,'"></span> / <span class="').concat(a.totalClass,'"></span>')),"progressbar"===a.type&&(r=a.renderProgressbar?a.renderProgressbar.call(v,a.progressbarFillClass):'<span class="'.concat(a.progressbarFillClass,'"></span>')),v.pagination.bullets=[],t.forEach(function(e){var t;"custom"!==a.type&&(e.innerHTML=r||""),"bullets"===a.type&&(t=v.pagination.bullets).push.apply(t,_toConsumableArray(e.querySelectorAll(k(a.bulletClass))))}),"custom"!==a.type&&g("paginationRender",t[0])}}function n(){v.params.pagination=W(v,v.originalParams.pagination,v.params.pagination,{el:"swiper-pagination"});var e,a=v.params.pagination;a.el&&(e=(e=(e="string"==typeof a.el&&v.isElement?v.el.querySelector(a.el):e)||"string"!=typeof a.el?e:_toConsumableArray(document.querySelectorAll(a.el)))||a.el)&&0!==e.length&&(v.params.uniqueNavElements&&"string"==typeof a.el&&Array.isArray(e)&&1<e.length&&1<(e=_toConsumableArray(v.el.querySelectorAll(a.el))).length&&(e=e.find(function(e){return X(e,".swiper")[0]===v.el})),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(v.pagination,{el:e}),(e=A(e)).forEach(function(e){var t;"bullets"===a.type&&a.clickable&&(t=e.classList).add.apply(t,_toConsumableArray((a.clickableClass||"").split(" "))),e.classList.add(a.modifierClass+a.type),e.classList.add(v.isHorizontal()?a.horizontalClass:a.verticalClass),"bullets"===a.type&&a.dynamicBullets&&(e.classList.add("".concat(a.modifierClass).concat(a.type,"-dynamic")),y=0,a.dynamicMainBullets<1)&&(a.dynamicMainBullets=1),"progressbar"===a.type&&a.progressbarOpposite&&e.classList.add(a.progressbarOppositeClass),a.clickable&&e.addEventListener("click",r),v.enabled||e.classList.add(a.lockClass)}))}function o(){var e,a=v.params.pagination;b()||((e=v.pagination.el)&&(e=A(e)).forEach(function(e){var t;e.classList.remove(a.hiddenClass),e.classList.remove(a.modifierClass+a.type),e.classList.remove(v.isHorizontal()?a.horizontalClass:a.verticalClass),a.clickable&&((t=e.classList).remove.apply(t,_toConsumableArray((a.clickableClass||"").split(" "))),e.removeEventListener("click",r))}),v.pagination.bullets&&v.pagination.bullets.forEach(function(e){return(e=e.classList).remove.apply(e,_toConsumableArray(a.bulletActiveClass.split(" ")))}))}a("changeDirection",function(){var t;v.pagination&&v.pagination.el&&(t=v.params.pagination,A(v.pagination.el).forEach(function(e){e.classList.remove(t.horizontalClass,t.verticalClass),e.classList.add(v.isHorizontal()?t.horizontalClass:t.verticalClass)}))}),a("init",function(){(!1===v.params.pagination.enabled?l:(n(),s(),i))()}),a("activeIndexChange",function(){void 0===v.snapIndex&&i()}),a("snapIndexChange",function(){i()}),a("snapGridLengthChange",function(){s(),i()}),a("destroy",function(){o()}),a("enable disable",function(){var e=v.pagination.el;e&&(e=A(e)).forEach(function(e){return e.classList[v.enabled?"remove":"add"](v.params.pagination.lockClass)})}),a("lock unlock",function(){i()}),a("click",function(e,t){var t=t.target,a=A(v.pagination.el);v.params.pagination.el&&v.params.pagination.hideOnClick&&a&&0<a.length&&!t.classList.contains(v.params.pagination.bulletClass)&&(v.navigation&&(v.navigation.nextEl&&t===v.navigation.nextEl||v.navigation.prevEl&&t===v.navigation.prevEl)||(t=a[0].classList.contains(v.params.pagination.hiddenClass),g(!0===t?"paginationShow":"paginationHide"),a.forEach(function(e){return e.classList.toggle(v.params.pagination.hiddenClass)})))});var l=function(){v.el.classList.add(v.params.pagination.paginationDisabledClass);var e=v.pagination.el;e&&(e=A(e)).forEach(function(e){return e.classList.add(v.params.pagination.paginationDisabledClass)}),o()};Object.assign(v.pagination,{enable:function(){v.el.classList.remove(v.params.pagination.paginationDisabledClass);var e=v.pagination.el;e&&(e=A(e)).forEach(function(e){return e.classList.remove(v.params.pagination.paginationDisabledClass)}),n(),s(),i()},disable:l,render:s,update:i,init:n,destroy:o})},function(e){var s,n,o,r,l=e.swiper,t=e.extendParams,a=e.on,d=e.emit,c=I(),p=!1,u=null,m=null;function i(){var e,t,a,r,i,s;l.params.scrollbar.el&&l.scrollbar.el&&(r=l.scrollbar,e=l.rtlTranslate,t=r.dragEl,a=r.el,r=l.params.scrollbar,s=l.params.loop?l.progressLoop:l.progress,s=(o-(i=n))*s,e?0<(s=-s)?(i=n-s,s=0):o<-s+n&&(i=o+s):s<0?(i=n+s,s=0):o<s+n&&(i=o-s),l.isHorizontal()?(t.style.transform="translate3d(".concat(s,"px, 0, 0)"),t.style.width="".concat(i,"px")):(t.style.transform="translate3d(0px, ".concat(s,"px, 0)"),t.style.height="".concat(i,"px")),r.hide)&&(clearTimeout(u),a.style.opacity=1,u=setTimeout(function(){a.style.opacity=0,a.style.transitionDuration="400ms"},1e3))}function f(){var e,t,a;l.params.scrollbar.el&&l.scrollbar.el&&(t=(e=l.scrollbar).dragEl,a=e.el,t.style.width="",t.style.height="",o=l.isHorizontal()?a.offsetWidth:a.offsetHeight,r=l.size/(l.virtualSize+l.params.slidesOffsetBefore-(l.params.centeredSlides?l.snapGrid[0]:0)),n="auto"===l.params.scrollbar.dragSize?o*r:parseInt(l.params.scrollbar.dragSize,10),l.isHorizontal()?t.style.width="".concat(n,"px"):t.style.height="".concat(n,"px"),a.style.display=1<=r?"none":"",l.params.scrollbar.hide&&(a.style.opacity=0),l.params.watchOverflow)&&l.enabled&&e.el.classList[l.isLocked?"add":"remove"](l.params.scrollbar.lockClass)}function h(e){return l.isHorizontal()?e.clientX:e.clientY}function v(e){var t=l.scrollbar,a=l.rtlTranslate,t=t.el,e=(h(e)-B(t)[l.isHorizontal()?"left":"top"]-(null!==s?s:n/2))/(o-n),t=(e=Math.max(Math.min(e,1),0),a&&(e=1-e),l.minTranslate()+(l.maxTranslate()-l.minTranslate())*e);l.updateProgress(t),l.setTranslate(t),l.updateActiveIndex(),l.updateSlidesClasses()}function g(e){var t=l.params.scrollbar,a=l.scrollbar,r=l.wrapperEl,i=a.el,a=a.dragEl;p=!0,s=e.target===a?h(e)-e.target.getBoundingClientRect()[l.isHorizontal()?"left":"top"]:null,e.preventDefault(),e.stopPropagation(),r.style.transitionDuration="100ms",a.style.transitionDuration="100ms",v(e),clearTimeout(m),i.style.transitionDuration="0ms",t.hide&&(i.style.opacity=1),l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="none"),d("scrollbarDragStart",e)}function y(e){var t=l.scrollbar,a=l.wrapperEl,r=t.el,t=t.dragEl;p&&(e.preventDefault&&e.cancelable?e.preventDefault():e.returnValue=!1,v(e),a.style.transitionDuration="0ms",r.style.transitionDuration="0ms",t.style.transitionDuration="0ms",d("scrollbarDragMove",e))}function b(e){var t=l.params.scrollbar,a=l.scrollbar,r=l.wrapperEl,i=a.el;p&&(p=!1,l.params.cssMode&&(l.wrapperEl.style["scroll-snap-type"]="",r.style.transitionDuration=""),t.hide&&(clearTimeout(m),m=M(function(){i.style.opacity=0,i.style.transitionDuration="400ms"},1e3)),d("scrollbarDragEnd",e),t.snapOnRelease)&&l.slideToClosest()}function w(e){var t,a=l.scrollbar,r=l.params,a=a.el;a&&(t=!!r.passiveListeners&&{passive:!1,capture:!1},r=!!r.passiveListeners&&{passive:!0,capture:!1},a=a)&&(a[a="on"===e?"addEventListener":"removeEventListener"]("pointerdown",g,t),c[a]("pointermove",y,t),c[a]("pointerup",b,r))}function E(){var e,t,a=l.scrollbar,r=l.el,i=(l.params.scrollbar=W(l,l.originalParams.scrollbar,l.params.scrollbar,{el:"swiper-scrollbar"}),l.params.scrollbar);if(i.el){if((e="string"==typeof i.el&&l.isElement?l.el.querySelector(i.el):e)||"string"!=typeof i.el)e=e||i.el;else if(!(e=c.querySelectorAll(i.el)).length)return;(e=0<(e=l.params.uniqueNavElements&&"string"==typeof i.el&&1<e.length&&1===r.querySelectorAll(i.el).length?r.querySelector(i.el):e).length?e[0]:e).classList.add(l.isHorizontal()?i.horizontalClass:i.verticalClass),e&&((t=e.querySelector(k(l.params.scrollbar.dragClass)))||(t=C("div",l.params.scrollbar.dragClass),e.append(t))),Object.assign(a,{el:e,dragEl:t}),i.draggable&&l.params.scrollbar.el&&l.scrollbar.el&&w("on"),e&&(r=e.classList)[l.enabled?"remove":"add"].apply(r,_toConsumableArray(T(l.params.scrollbar.lockClass)))}}function x(){var e=l.params.scrollbar,t=l.scrollbar.el;t&&(t=t.classList).remove.apply(t,_toConsumableArray(T(l.isHorizontal()?e.horizontalClass:e.verticalClass))),l.params.scrollbar.el&&l.scrollbar.el&&w("off")}t({scrollbar:{el:null,dragSize:"auto",hide:!1,draggable:!1,snapOnRelease:!0,lockClass:"swiper-scrollbar-lock",dragClass:"swiper-scrollbar-drag",scrollbarDisabledClass:"swiper-scrollbar-disabled",horizontalClass:"swiper-scrollbar-horizontal",verticalClass:"swiper-scrollbar-vertical"}}),l.scrollbar={el:null,dragEl:null},a("changeDirection",function(){var t;l.scrollbar&&l.scrollbar.el&&(t=l.params.scrollbar,A(l.scrollbar.el).forEach(function(e){e.classList.remove(t.horizontalClass,t.verticalClass),e.classList.add(l.isHorizontal()?t.horizontalClass:t.verticalClass)}))}),a("init",function(){(!1===l.params.scrollbar.enabled?S:(E(),f(),i))()}),a("update resize observerUpdate lock unlock changeDirection",function(){f()}),a("setTranslate",function(){i()}),a("setTransition",function(e,t){l.params.scrollbar.el&&l.scrollbar.el&&(l.scrollbar.dragEl.style.transitionDuration="".concat(t,"ms"))}),a("enable disable",function(){var e=l.scrollbar.el;e&&(e=e.classList)[l.enabled?"remove":"add"].apply(e,_toConsumableArray(T(l.params.scrollbar.lockClass)))}),a("destroy",function(){x()});var S=function(){var e;(e=l.el.classList).add.apply(e,_toConsumableArray(T(l.params.scrollbar.scrollbarDisabledClass))),l.scrollbar.el&&(e=l.scrollbar.el.classList).add.apply(e,_toConsumableArray(T(l.params.scrollbar.scrollbarDisabledClass))),x()};Object.assign(l.scrollbar,{enable:function(){var e;(e=l.el.classList).remove.apply(e,_toConsumableArray(T(l.params.scrollbar.scrollbarDisabledClass))),l.scrollbar.el&&(e=l.scrollbar.el.classList).remove.apply(e,_toConsumableArray(T(l.params.scrollbar.scrollbarDisabledClass))),E(),f(),i()},disable:S,updateSize:f,setTranslate:i,init:E,destroy:x})},function(e){function s(e,t){var a=d.rtl?-1:1,r=e.getAttribute("data-swiper-parallax")||"0",i=e.getAttribute("data-swiper-parallax-x"),s=e.getAttribute("data-swiper-parallax-y"),n=e.getAttribute("data-swiper-parallax-scale"),o=e.getAttribute("data-swiper-parallax-opacity"),l=e.getAttribute("data-swiper-parallax-rotate"),a=(i||s?(i=i||"0",s=s||"0"):d.isHorizontal()?(i=r,s="0"):(s=r,i="0"),i=0<=i.indexOf("%")?parseInt(i,10)*t*a+"%":i*t*a+"px",s=0<=s.indexOf("%")?parseInt(s,10)*t+"%":s*t+"px",null!=o&&(r=o-(o-1)*(1-Math.abs(t)),e.style.opacity=r),"translate3d(".concat(i,", ").concat(s,", 0px)"));null!=n&&(a+=" scale(".concat(n-(n-1)*(1-Math.abs(t)),")")),l&&null!=l&&(a+=" rotate(".concat(l*t*-1,"deg)")),e.style.transform=a}function t(){var e=d.el,t=d.slides,r=d.progress,i=d.snapGrid,e=(d.isElement,Q(e,n));d.isElement&&e.push.apply(e,_toConsumableArray(Q(d.hostEl,n))),e.forEach(function(e){s(e,r)}),t.forEach(function(e,t){var a=e.progress;1<d.params.slidesPerGroup&&"auto"!==d.params.slidesPerView&&(a+=Math.ceil(t/2)-r*(i.length-1)),a=Math.min(Math.max(a,-1),1),e.querySelectorAll("".concat(n,", [data-swiper-parallax-rotate]")).forEach(function(e){s(e,a)})})}var d=e.swiper,a=e.extendParams,e=e.on,n=(a({parallax:{enabled:!1}}),"[data-swiper-parallax], [data-swiper-parallax-x], [data-swiper-parallax-y], [data-swiper-parallax-opacity], [data-swiper-parallax-scale]");e("beforeInit",function(){d.params.parallax.enabled&&(d.params.watchSlidesProgress=!0,d.originalParams.watchSlidesProgress=!0)}),e("init",function(){d.params.parallax.enabled&&t()}),e("setTranslate",function(){d.params.parallax.enabled&&t()}),e("setTransition",function(e,t){var a,r;d.params.parallax.enabled&&(void 0===(a=t)&&(a=d.params.speed),t=d.el,r=d.hostEl,t=_toConsumableArray(t.querySelectorAll(n)),d.isElement&&t.push.apply(t,_toConsumableArray(r.querySelectorAll(n))),t.forEach(function(e){var t=parseInt(e.getAttribute("data-swiper-parallax-duration"),10)||a;0===a&&(t=0),e.style.transitionDuration="".concat(t,"ms")}))})},function(e){var i,s,n,c=e.swiper,t=e.extendParams,a=e.on,r=e.emit,p=G(),u=(t({zoom:{enabled:!1,limitToOriginalSize:!1,maxRatio:3,minRatio:1,panOnMouseMove:!1,toggle:!0,containerClass:"swiper-zoom-container",zoomedSlideClass:"swiper-slide-zoomed"}}),c.zoom={enabled:!1},1),o=!1,l=!1,d={x:0,y:0},m=-3,f=[],h={originX:0,originY:0,slideEl:void 0,slideWidth:void 0,slideHeight:void 0,imageEl:void 0,imageWrapEl:void 0,maxRatio:3},v={isTouched:void 0,isMoved:void 0,currentX:void 0,currentY:void 0,minX:void 0,minY:void 0,maxX:void 0,maxY:void 0,width:void 0,height:void 0,startX:void 0,startY:void 0,touchesStart:{},touchesCurrent:{}},g={x:void 0,y:void 0,prevPositionX:void 0,prevPositionY:void 0,prevTime:void 0},y=1;function b(){var e,t,a,r;return f.length<2?1:(e=f[0].pageX,t=f[0].pageY,a=f[1].pageX,r=f[1].pageY,Math.sqrt(Math.pow(a-e,2)+Math.pow(r-t,2)))}function w(){var e=c.params.zoom,t=h.imageWrapEl.getAttribute("data-swiper-zoom")||e.maxRatio;return e.limitToOriginalSize&&h.imageEl&&h.imageEl.naturalWidth?(e=h.imageEl.naturalWidth/h.imageEl.offsetWidth,Math.min(e,t)):t}function E(t){var e=c.isElement?"swiper-slide":".".concat(c.params.slideClass);return t.target.matches(e)||0<c.slides.filter(function(e){return e.contains(t.target)}).length}function x(t){var e=".".concat(c.params.zoom.containerClass);return t.target.matches(e)||0<_toConsumableArray(c.hostEl.querySelectorAll(e)).filter(function(e){return e.contains(t.target)}).length}function S(e){if("mouse"===e.pointerType&&f.splice(0,f.length),E(e)){var t=c.params.zoom;if(s=i=!1,f.push(e),!(f.length<2)){if(i=!0,h.scaleStart=b(),!h.slideEl){h.slideEl=e.target.closest(".".concat(c.params.slideClass,", swiper-slide")),h.slideEl||(h.slideEl=c.slides[c.activeIndex]);var e=(e=h.slideEl.querySelector(".".concat(t.containerClass)))&&e.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0];if(h.imageEl=e,h.imageWrapEl=e?X(h.imageEl,".".concat(t.containerClass))[0]:void 0,!h.imageWrapEl)return void(h.imageEl=void 0);h.maxRatio=w()}h.imageEl&&(e=(t=_slicedToArray(f.length<2?{x:null,y:null}:(e=h.imageEl.getBoundingClientRect(),[(f[0].pageX+(f[1].pageX-f[0].pageX)/2-e.x-p.scrollX)/u,(f[0].pageY+(f[1].pageY-f[0].pageY)/2-e.y-p.scrollY)/u]),2))[0],t=t[1],h.originX=e,h.originY=t,h.imageEl.style.transitionDuration="0ms"),o=!0}}}function T(t){var e,a,r;E(t)&&(e=c.params.zoom,a=c.zoom,0<=(r=f.findIndex(function(e){return e.pointerId===t.pointerId}))&&(f[r]=t),f.length<2||(s=!0,h.scaleMove=b(),h.imageEl&&(a.scale=h.scaleMove/h.scaleStart*u,a.scale>h.maxRatio&&(a.scale=h.maxRatio-1+Math.pow(a.scale-h.maxRatio+1,.5)),a.scale<e.minRatio&&(a.scale=e.minRatio+1-Math.pow(e.minRatio-a.scale+1,.5)),h.imageEl.style.transform="translate3d(0,0,0) scale(".concat(a.scale,")"))))}function M(t){var e,a,r;!E(t)||"mouse"===t.pointerType&&"pointerout"===t.type||(e=c.params.zoom,a=c.zoom,0<=(r=f.findIndex(function(e){return e.pointerId===t.pointerId}))&&f.splice(r,1),i&&s&&(s=i=!1,h.imageEl)&&(a.scale=Math.max(Math.min(a.scale,h.maxRatio),e.minRatio),h.imageEl.style.transitionDuration="".concat(c.params.speed,"ms"),h.imageEl.style.transform="translate3d(0,0,0) scale(".concat(a.scale,")"),u=a.scale,o=!1,1<a.scale&&h.slideEl?h.slideEl.classList.add("".concat(e.zoomedSlideClass)):a.scale<=1&&h.slideEl&&h.slideEl.classList.remove("".concat(e.zoomedSlideClass)),1===a.scale)&&(h.originX=0,h.originY=0,h.slideEl=void 0))}function C(){c.touchEventsData.preventTouchMoveFromPointerMove=!1}function P(e){var t="mouse"===e.pointerType&&c.params.zoom.panOnMouseMove;if(E(e)&&x(e)){var a=c.zoom;if(h.imageEl)if(v.isTouched&&h.slideEl)if(t)L(e);else{v.isMoved||(v.width=h.imageEl.offsetWidth||h.imageEl.clientWidth,v.height=h.imageEl.offsetHeight||h.imageEl.clientHeight,v.startX=H(h.imageWrapEl,"x")||0,v.startY=H(h.imageWrapEl,"y")||0,h.slideWidth=h.slideEl.offsetWidth,h.slideHeight=h.slideEl.offsetHeight,h.imageWrapEl.style.transitionDuration="0ms");var r=v.width*a.scale,i=v.height*a.scale;if(v.minX=Math.min(h.slideWidth/2-r/2,0),v.maxX=-v.minX,v.minY=Math.min(h.slideHeight/2-i/2,0),v.maxY=-v.minY,v.touchesCurrent.x=(0<f.length?f[0]:e).pageX,v.touchesCurrent.y=(0<f.length?f[0]:e).pageY,5<Math.max(Math.abs(v.touchesCurrent.x-v.touchesStart.x),Math.abs(v.touchesCurrent.y-v.touchesStart.y))&&(c.allowClick=!1),!v.isMoved&&!o){if(c.isHorizontal()&&(Math.floor(v.minX)===Math.floor(v.startX)&&v.touchesCurrent.x<v.touchesStart.x||Math.floor(v.maxX)===Math.floor(v.startX)&&v.touchesCurrent.x>v.touchesStart.x))return v.isTouched=!1,void C();if(!c.isHorizontal()&&(Math.floor(v.minY)===Math.floor(v.startY)&&v.touchesCurrent.y<v.touchesStart.y||Math.floor(v.maxY)===Math.floor(v.startY)&&v.touchesCurrent.y>v.touchesStart.y))return v.isTouched=!1,void C()}e.cancelable&&e.preventDefault(),e.stopPropagation(),clearTimeout(n),c.touchEventsData.preventTouchMoveFromPointerMove=!0,n=setTimeout(function(){c.destroyed||C()}),v.isMoved=!0;r=(a.scale-u)/(h.maxRatio-c.params.zoom.minRatio),i=h.originX,a=h.originY;v.currentX=v.touchesCurrent.x-v.touchesStart.x+v.startX+r*(v.width-2*i),v.currentY=v.touchesCurrent.y-v.touchesStart.y+v.startY+r*(v.height-2*a),v.currentX<v.minX&&(v.currentX=v.minX+1-Math.pow(v.minX-v.currentX+1,.8)),v.currentX>v.maxX&&(v.currentX=v.maxX-1+Math.pow(v.currentX-v.maxX+1,.8)),v.currentY<v.minY&&(v.currentY=v.minY+1-Math.pow(v.minY-v.currentY+1,.8)),v.currentY>v.maxY&&(v.currentY=v.maxY-1+Math.pow(v.currentY-v.maxY+1,.8)),g.prevPositionX||(g.prevPositionX=v.touchesCurrent.x),g.prevPositionY||(g.prevPositionY=v.touchesCurrent.y),g.prevTime||(g.prevTime=Date.now()),g.x=(v.touchesCurrent.x-g.prevPositionX)/(Date.now()-g.prevTime)/2,g.y=(v.touchesCurrent.y-g.prevPositionY)/(Date.now()-g.prevTime)/2,Math.abs(v.touchesCurrent.x-g.prevPositionX)<2&&(g.x=0),Math.abs(v.touchesCurrent.y-g.prevPositionY)<2&&(g.y=0),g.prevPositionX=v.touchesCurrent.x,g.prevPositionY=v.touchesCurrent.y,g.prevTime=Date.now(),h.imageWrapEl.style.transform="translate3d(".concat(v.currentX,"px, ").concat(v.currentY,"px,0)")}else t&&L(e)}}function A(){var e=c.zoom;h.slideEl&&c.activeIndex!==c.slides.indexOf(h.slideEl)&&(h.imageEl&&(h.imageEl.style.transform="translate3d(0,0,0) scale(1)"),h.imageWrapEl&&(h.imageWrapEl.style.transform="translate3d(0,0,0)"),h.slideEl.classList.remove("".concat(c.params.zoom.zoomedSlideClass)),e.scale=1,u=1,h.slideEl=void 0,h.imageEl=void 0,h.imageWrapEl=void 0,h.originX=0,h.originY=0)}function L(e){var t,a,r,i,s,n,o;u<=1||!h.imageWrapEl||E(e)&&x(e)&&(t=p.getComputedStyle(h.imageWrapEl).transform,t=new p.DOMMatrix(t),l?(n=(e.clientX-d.x)*m,a=(e.clientY-d.y)*m,o=v.width*u,s=v.height*u,r=h.slideWidth,i=h.slideHeight,o=-(r=Math.min(r/2-o/2,0)),s=-(i=Math.min(i/2-s/2,0)),n=Math.max(Math.min(v.startX+n,o),r),o=Math.max(Math.min(v.startY+a,s),i),h.imageWrapEl.style.transitionDuration="0ms",h.imageWrapEl.style.transform="translate3d(".concat(n,"px, ").concat(o,"px, 0)"),d.x=e.clientX,d.y=e.clientY,v.startX=n,v.startY=o):(l=!0,d.x=e.clientX,d.y=e.clientY,v.startX=t.e,v.startY=t.f,v.width=h.imageEl.offsetWidth||h.imageEl.clientWidth,v.height=h.imageEl.offsetHeight||h.imageEl.clientHeight,h.slideWidth=h.slideEl.offsetWidth,h.slideHeight=h.slideEl.offsetHeight))}function k(e){var t,a,r,i,s,n,o,l=c.zoom,d=c.params.zoom;h.slideEl||(e&&e.target&&(h.slideEl=e.target.closest(".".concat(c.params.slideClass,", swiper-slide"))),h.slideEl||(c.params.virtual&&c.params.virtual.enabled&&c.virtual?h.slideEl=Q(c.slidesEl,".".concat(c.params.slideActiveClass))[0]:h.slideEl=c.slides[c.activeIndex]),a=(a=h.slideEl.querySelector(".".concat(d.containerClass)))&&a.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0],h.imageEl=a,h.imageWrapEl=a?X(h.imageEl,".".concat(d.containerClass))[0]:void 0),h.imageEl&&h.imageWrapEl&&(c.params.cssMode&&(c.wrapperEl.style.overflow="hidden",c.wrapperEl.style.touchAction="none"),h.slideEl.classList.add("".concat(d.zoomedSlideClass)),a=void 0===v.touchesStart.x&&e?(t=e.pageX,e.pageY):(t=v.touchesStart.x,v.touchesStart.y),d="number"==typeof e?e:null,1===u&&d&&(v.touchesStart.x=a=t=void 0,v.touchesStart.y=void 0),o=w(),l.scale=d||o,u=d||o,!e||1===u&&d?i=r=0:(o=h.slideEl.offsetWidth,e=h.slideEl.offsetHeight,t=B(h.slideEl).left+p.scrollX+o/2-t,a=B(h.slideEl).top+p.scrollY+e/2-a,s=h.imageEl.offsetWidth||h.imageEl.clientWidth,n=h.imageEl.offsetHeight||h.imageEl.clientHeight,s=s*l.scale,n=n*l.scale,o=Math.min(o/2-s/2,0),s=Math.min(e/2-n/2,0),(e=-o)<(r=(r=t*l.scale)<o?o:r)&&(r=e),(n=-s)<(i=(i=a*l.scale)<s?s:i)&&(i=n)),d&&1===l.scale&&(h.originX=0,h.originY=0),h.imageWrapEl.style.transitionDuration="300ms",h.imageWrapEl.style.transform="translate3d(".concat(r,"px, ").concat(i,"px,0)"),h.imageEl.style.transitionDuration="300ms",h.imageEl.style.transform="translate3d(0,0,0) scale(".concat(l.scale,")"))}function I(){var e,t=c.zoom,a=c.params.zoom;h.slideEl||(c.params.virtual&&c.params.virtual.enabled&&c.virtual?h.slideEl=Q(c.slidesEl,".".concat(c.params.slideActiveClass))[0]:h.slideEl=c.slides[c.activeIndex],e=(e=h.slideEl.querySelector(".".concat(a.containerClass)))&&e.querySelectorAll("picture, img, svg, canvas, .swiper-zoom-target")[0],h.imageEl=e,h.imageWrapEl=e?X(h.imageEl,".".concat(a.containerClass))[0]:void 0),h.imageEl&&h.imageWrapEl&&(c.params.cssMode&&(c.wrapperEl.style.overflow="",c.wrapperEl.style.touchAction=""),t.scale=1,u=1,v.touchesStart.x=void 0,v.touchesStart.y=void 0,h.imageWrapEl.style.transitionDuration="300ms",h.imageWrapEl.style.transform="translate3d(0,0,0)",h.imageEl.style.transitionDuration="300ms",h.imageEl.style.transform="translate3d(0,0,0) scale(1)",h.slideEl.classList.remove("".concat(a.zoomedSlideClass)),h.slideEl=void 0,h.originX=0,h.originY=0,c.params.zoom.panOnMouseMove)&&(d={x:0,y:0},l)&&(l=!1,v.startX=0,v.startY=0)}function z(e){var t=c.zoom;t.scale&&1!==t.scale?I():k(e)}function O(){return{passiveListener:!!c.params.passiveListeners&&{passive:!0,capture:!1},activeListenerWithCapture:!c.params.passiveListeners||{passive:!1,capture:!0}}}function _(){var t,e=c.zoom;e.enabled||(e.enabled=!0,e=O(),t=e.passiveListener,e=e.activeListenerWithCapture,c.wrapperEl.addEventListener("pointerdown",S,t),c.wrapperEl.addEventListener("pointermove",T,e),["pointerup","pointercancel","pointerout"].forEach(function(e){c.wrapperEl.addEventListener(e,M,t)}),c.wrapperEl.addEventListener("pointermove",P,e))}function D(){var t,e=c.zoom;e.enabled&&(e.enabled=!1,e=O(),t=e.passiveListener,e=e.activeListenerWithCapture,c.wrapperEl.removeEventListener("pointerdown",S,t),c.wrapperEl.removeEventListener("pointermove",T,e),["pointerup","pointercancel","pointerout"].forEach(function(e){c.wrapperEl.removeEventListener(e,M,t)}),c.wrapperEl.removeEventListener("pointermove",P,e))}Object.defineProperty(c.zoom,"scale",{get:function(){return y},set:function(e){var t,a;y!==e&&(t=h.imageEl,a=h.slideEl,r("zoomChange",e,t,a)),y=e}}),a("init",function(){c.params.zoom.enabled&&_()}),a("destroy",function(){D()}),a("touchStart",function(e,t){var a;c.zoom.enabled&&(t=t,a=c.device,h.imageEl)&&!v.isTouched&&(a.android&&t.cancelable&&t.preventDefault(),v.isTouched=!0,a=0<f.length?f[0]:t,v.touchesStart.x=a.pageX,v.touchesStart.y=a.pageY)}),a("touchEnd",function(e,t){if(c.zoom.enabled){var a=c.zoom;if(f.length=0,h.imageEl){if(!v.isTouched||!v.isMoved)return void(v.isTouched=!1,v.isMoved=!1);v.isTouched=!1,v.isMoved=!1;var r=300,i=300,s=g.x*r,s=v.currentX+s,n=g.y*i,n=v.currentY+n,r=(0!==g.x&&(r=Math.abs((s-v.currentX)/g.x)),0!==g.y&&(i=Math.abs((n-v.currentY)/g.y)),Math.max(r,i)),i=(v.currentX=s,v.currentY=n,v.width*a.scale),s=v.height*a.scale;v.minX=Math.min(h.slideWidth/2-i/2,0),v.maxX=-v.minX,v.minY=Math.min(h.slideHeight/2-s/2,0),v.maxY=-v.minY,v.currentX=Math.max(Math.min(v.currentX,v.maxX),v.minX),v.currentY=Math.max(Math.min(v.currentY,v.maxY),v.minY),h.imageWrapEl.style.transitionDuration="".concat(r,"ms"),h.imageWrapEl.style.transform="translate3d(".concat(v.currentX,"px, ").concat(v.currentY,"px,0)")}}}),a("doubleTap",function(e,t){!c.animating&&c.params.zoom.enabled&&c.zoom.enabled&&c.params.zoom.toggle&&z(t)}),a("transitionEnd",function(){c.zoom.enabled&&c.params.zoom.enabled&&A()}),a("slideChange",function(){c.zoom.enabled&&c.params.zoom.enabled&&c.params.cssMode&&A()}),Object.assign(c.zoom,{enable:_,disable:D,in:k,out:I,toggle:z})},function(e){var l=e.swiper,t=e.extendParams,e=e.on;function d(e,t){var a,r,i,s,n,o=function(e,t){for(r=-1,a=e.length;1<a-r;)e[i=a+r>>1]<=t?r=i:a=i;return a};return this.x=e,this.y=t,this.lastIndex=e.length-1,this.interpolate=function(e){return e?(n=o(this.x,e),s=n-1,(e-this.x[s])*(this.y[n]-this.y[s])/(this.x[n]-this.x[s])+this.y[s]):0},this}function a(){l.controller.control&&l.controller.spline&&(l.controller.spline=void 0,delete l.controller.spline)}t({controller:{control:void 0,inverse:!1,by:"slide"}}),l.controller={control:void 0},e("beforeInit",function(){"undefined"!=typeof window&&("string"==typeof l.params.controller.control||l.params.controller.control instanceof HTMLElement)?("string"==typeof l.params.controller.control?_toConsumableArray(document.querySelectorAll(l.params.controller.control)):[l.params.controller.control]).forEach(function(t){var a,r;l.controller.control||(l.controller.control=[]),t&&t.swiper?l.controller.control.push(t.swiper):t&&(a="".concat(l.params.eventsPrefix,"init"),t.addEventListener(a,r=function(e){l.controller.control.push(e.detail[0]),l.update(),t.removeEventListener(a,r)}))}):l.controller.control=l.params.controller.control}),e("update",function(){a()}),e("resize",function(){a()}),e("observerUpdate",function(){a()}),e("setTranslate",function(e,t,a){l.controller.control&&!l.controller.control.destroyed&&l.controller.setTranslate(t,a)}),e("setTransition",function(e,t,a){l.controller.control&&!l.controller.control.destroyed&&l.controller.setTransition(t,a)}),Object.assign(l.controller,{setTranslate:function(e,t){var r,i,a=l.controller.control,s=l.constructor;function n(e){var t,a;e.destroyed||(t=l.rtlTranslate?-l.translate:l.translate,"slide"===l.params.controller.by&&(a=e,l.controller.spline=l.params.loop?new d(l.slidesGrid,a.slidesGrid):new d(l.snapGrid,a.snapGrid),i=-l.controller.spline.interpolate(-t)),i&&"container"!==l.params.controller.by||(r=(e.maxTranslate()-e.minTranslate())/(l.maxTranslate()-l.minTranslate()),!Number.isNaN(r)&&Number.isFinite(r)||(r=1),i=(t-l.minTranslate())*r+e.minTranslate()),l.params.controller.inverse&&(i=e.maxTranslate()-i),e.updateProgress(i),e.setTranslate(i,l),e.updateActiveIndex(),e.updateSlidesClasses())}if(Array.isArray(a))for(var o=0;o<a.length;o+=1)a[o]!==t&&a[o]instanceof s&&n(a[o]);else a instanceof s&&t!==a&&n(a)},setTransition:function(t,e){var a,r=l.constructor,i=l.controller.control;function s(e){e.destroyed||(e.setTransition(t,l),0!==t&&(e.transitionStart(),e.params.autoHeight&&M(function(){e.updateAutoHeight()}),g(e.wrapperEl,function(){i&&e.transitionEnd()})))}if(Array.isArray(i))for(a=0;a<i.length;a+=1)i[a]!==e&&i[a]instanceof r&&s(i[a]);else i instanceof r&&e!==i&&s(i)}})},function(e){var n=e.swiper,t=e.extendParams,e=e.on;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),n.a11y={clicked:!1};var i,s,o=null,l=(new Date).getTime();function r(e){var t=o;0!==t.length&&(t.innerHTML="",t.innerHTML=e)}function d(e){(e=A(e)).forEach(function(e){e.setAttribute("tabIndex","0")})}function a(e){(e=A(e)).forEach(function(e){e.setAttribute("tabIndex","-1")})}function c(e,t){(e=A(e)).forEach(function(e){e.setAttribute("role",t)})}function p(e,t){(e=A(e)).forEach(function(e){e.setAttribute("aria-roledescription",t)})}function u(e,t){(e=A(e)).forEach(function(e){e.setAttribute("aria-label",t)})}function m(e){(e=A(e)).forEach(function(e){e.setAttribute("aria-disabled",!0)})}function f(e){(e=A(e)).forEach(function(e){e.setAttribute("aria-disabled",!1)})}function h(e){var t,a;13!==e.keyCode&&32!==e.keyCode||(t=n.params.a11y,a=e.target,n.pagination&&n.pagination.el&&(a===n.pagination.el||n.pagination.el.contains(e.target))&&!e.target.matches(k(n.params.pagination.bulletClass)))||(n.navigation&&n.navigation.prevEl&&n.navigation.nextEl&&(e=A(n.navigation.prevEl),A(n.navigation.nextEl).includes(a)&&(n.isEnd&&!n.params.loop||n.slideNext(),n.isEnd?r(t.lastSlideMessage):r(t.nextSlideMessage)),e.includes(a))&&(n.isBeginning&&!n.params.loop||n.slidePrev(),n.isBeginning?r(t.firstSlideMessage):r(t.prevSlideMessage)),n.pagination&&a.matches(k(n.params.pagination.bulletClass))&&a.click())}function v(){return n.pagination&&n.pagination.bullets&&n.pagination.bullets.length}function g(){return v()&&n.params.pagination.clickable}function y(e,t,a){var r;d(e),"BUTTON"!==e.tagName&&(c(e,"button"),e.addEventListener("keydown",h)),u(e,a),r=t,A(e).forEach(function(e){e.setAttribute("aria-controls",r)})}function b(e){s&&s!==e.target&&!s.contains(e.target)&&(i=!0),n.a11y.clicked=!0}function w(){i=!1,requestAnimationFrame(function(){requestAnimationFrame(function(){n.destroyed||(n.a11y.clicked=!1)})})}function E(e){l=(new Date).getTime()}function x(e){var t,a,r;n.a11y.clicked||!n.params.a11y.scrollOnFocus||(new Date).getTime()-l<100||(t=e.target.closest(".".concat(n.params.slideClass,", swiper-slide")))&&n.slides.includes(t)&&(s=t,a=n.slides.indexOf(t)===n.activeIndex,r=n.params.watchSlidesProgress&&n.visibleSlides&&n.visibleSlides.includes(t),a||r||e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(n.isHorizontal()?n.el.scrollLeft=0:n.el.scrollTop=0,requestAnimationFrame(function(){i||(n.params.loop?n.slideToLoop(parseInt(t.getAttribute("data-swiper-slide-index")),0):n.slideTo(n.slides.indexOf(t),0),i=!1)})))}function S(){var a=n.params.a11y,r=(a.itemRoleDescriptionMessage&&p(n.slides,a.itemRoleDescriptionMessage),a.slideRole&&c(n.slides,a.slideRole),n.slides.length);a.slideLabelMessage&&n.slides.forEach(function(e,t){t=n.params.loop?parseInt(e.getAttribute("data-swiper-slide-index"),10):t;u(e,a.slideLabelMessage.replace(/\{\{index\}\}/,t+1).replace(/\{\{slidesLength\}\}/,r))})}e("beforeInit",function(){(o=C("span",n.params.a11y.notificationClass)).setAttribute("aria-live","assertive"),o.setAttribute("aria-atomic","true")}),e("afterInit",function(){var t,a,e,r,i,s;n.params.a11y.enabled&&(a=n.params.a11y,n.el.append(o),e=n.el,a.containerRoleDescriptionMessage&&p(e,a.containerRoleDescriptionMessage),a.containerMessage&&u(e,a.containerMessage),a.containerRole&&c(e,a.containerRole),e=n.wrapperEl,r=a.id||e.getAttribute("id")||"swiper-wrapper-".concat("x".repeat(i=void 0===(i=16)?16:i).replace(/x/g,function(){return Math.round(16*Math.random()).toString(16)})),i=n.params.autoplay&&n.params.autoplay.enabled?"off":"polite",s=r,A(e).forEach(function(e){e.setAttribute("id",s)}),t=i,A(e).forEach(function(e){e.setAttribute("aria-live",t)}),S(),i=n.navigation||{},e=i.nextEl,i=i.prevEl,e=A(e),i=A(i),e&&e.forEach(function(e){return y(e,r,a.nextSlideMessage)}),i&&i.forEach(function(e){return y(e,r,a.prevSlideMessage)}),g()&&A(n.pagination.el).forEach(function(e){e.addEventListener("keydown",h)}),I().addEventListener("visibilitychange",E),n.el.addEventListener("focus",x,!0),n.el.addEventListener("focus",x,!0),n.el.addEventListener("pointerdown",b,!0),n.el.addEventListener("pointerup",w,!0))}),e("slidesLengthChange snapGridLengthChange slidesGridLengthChange",function(){n.params.a11y.enabled&&S()}),e("fromEdge toEdge afterInit lock unlock",function(){var e,t;n.params.a11y.enabled&&!n.params.loop&&!n.params.rewind&&n.navigation&&(e=(t=n.navigation).nextEl,(t=t.prevEl)&&(n.isBeginning?(m(t),a):(f(t),d))(t),e)&&(n.isEnd?(m(e),a):(f(e),d))(e)}),e("paginationUpdate",function(){var t;n.params.a11y.enabled&&(t=n.params.a11y,v())&&n.pagination.bullets.forEach(function(e){n.params.pagination.clickable&&(d(e),n.params.pagination.renderBullet||(c(e,"button"),u(e,t.paginationBulletMessage.replace(/\{\{index\}\}/,P(e)+1)))),e.matches(k(n.params.pagination.bulletActiveClass))?e.setAttribute("aria-current","true"):e.removeAttribute("aria-current")})}),e("destroy",function(){var e,t;n.params.a11y.enabled&&(o&&o.remove(),e=n.navigation||{},t=e.nextEl,e=e.prevEl,t=A(t),e=A(e),t&&t.forEach(function(e){return e.removeEventListener("keydown",h)}),e&&e.forEach(function(e){return e.removeEventListener("keydown",h)}),g()&&A(n.pagination.el).forEach(function(e){e.removeEventListener("keydown",h)}),I().removeEventListener("visibilitychange",E),n.el)&&"string"!=typeof n.el&&(n.el.removeEventListener("focus",x,!0),n.el.removeEventListener("pointerdown",b,!0),n.el.removeEventListener("pointerup",w,!0))})},function(e){function n(e){return e.toString().replace(/\s+/g,"-").replace(/[^\w-]+/g,"").replace(/--+/g,"-").replace(/^-+/,"").replace(/-+$/,"")}function t(e){var t=G(),t=(e=(e?new URL(e):t.location).pathname.slice(1).split("/").filter(function(e){return""!==e})).length;return{key:e[t-2],value:e[t-1]}}function a(e,t){var a,r,i=G();l&&o.params.history.enabled&&(a=o.params.url?new URL(o.params.url):i.location,t=o.virtual&&o.params.virtual.enabled?o.slidesEl.querySelector('[data-swiper-slide-index="'.concat(t,'"]')):o.slides[t],t=n(t.getAttribute("data-history")),0<o.params.history.root.length?("/"===(r=o.params.history.root)[r.length-1]&&(r=r.slice(0,r.length-1)),t="".concat(r,"/").concat(e?"".concat(e,"/"):"").concat(t)):a.pathname.includes(e)||(t="".concat(e?"".concat(e,"/"):"").concat(t)),o.params.history.keepQuery&&(t+=a.search),(r=i.history.state)&&r.value===t||(o.params.history.replaceState?i.history.replaceState({value:t},null,t):i.history.pushState({value:t},null,t)))}function r(e,t,a){if(t)for(var r=0,i=o.slides.length;r<i;r+=1){var s=o.slides[r];n(s.getAttribute("data-history"))===t&&(s=o.getSlideIndex(s),o.slideTo(s,e,a))}else o.slideTo(0,e,a)}function i(){d=t(o.params.url),r(o.params.speed,d.value,!1)}var o=e.swiper,s=e.extendParams,e=e.on,l=(s({history:{enabled:!1,root:"",replaceState:!1,key:"slides",keepQuery:!1}}),!1),d={};e("init",function(){if(o.params.history.enabled){var e=G();if(o.params.history){if(!e.history||!e.history.pushState)return void(o.params.history.enabled=!1,o.params.hashNavigation.enabled=!0);l=!0,((d=t(o.params.url)).key||d.value)&&r(0,d.value,o.params.runCallbacksOnInit),o.params.history.replaceState||e.addEventListener("popstate",i)}}}),e("destroy",function(){var e;o.params.history.enabled&&(e=G(),o.params.history.replaceState||e.removeEventListener("popstate",i))}),e("transitionEnd _freeModeNoMomentumRelease",function(){l&&a(o.params.history.key,o.activeIndex)}),e("slideChange",function(){l&&o.params.cssMode&&a(o.params.history.key,o.activeIndex)})},function(e){function t(){s("hashChange");var e=o.location.hash.replace("#",""),t=r.virtual&&r.params.virtual.enabled?r.slidesEl.querySelector('[data-swiper-slide-index="'.concat(r.activeIndex,'"]')):r.slides[r.activeIndex];e===(t?t.getAttribute("data-hash"):"")||void 0===(t=r.params.hashNavigation.getSlideIndex(r,e))||Number.isNaN(t)||r.slideTo(t)}function a(){var e;n&&r.params.hashNavigation.enabled&&(e=(e=r.virtual&&r.params.virtual.enabled?r.slidesEl.querySelector('[data-swiper-slide-index="'.concat(r.activeIndex,'"]')):r.slides[r.activeIndex])?e.getAttribute("data-hash")||e.getAttribute("data-history"):"",r.params.hashNavigation.replaceState&&l.history&&l.history.replaceState?l.history.replaceState(null,null,"#".concat(e)||""):o.location.hash=e||"",s("hashSet"))}var r=e.swiper,i=e.extendParams,s=e.emit,e=e.on,n=!1,o=I(),l=G();i({hashNavigation:{enabled:!1,replaceState:!1,watchState:!1,getSlideIndex:function(e,t){var a;return r.virtual&&r.params.virtual.enabled?(a=r.slides.find(function(e){return e.getAttribute("data-hash")===t}))?parseInt(a.getAttribute("data-swiper-slide-index"),10):0:r.getSlideIndex(Q(r.slidesEl,".".concat(r.params.slideClass,'[data-hash="').concat(t,'"], swiper-slide[data-hash="').concat(t,'"]'))[0])}}});e("init",function(){var e;!r.params.hashNavigation.enabled||!r.params.hashNavigation.enabled||r.params.history&&r.params.history.enabled||(n=!0,(e=o.location.hash.replace("#",""))&&(e=r.params.hashNavigation.getSlideIndex(r,e),r.slideTo(e||0,0,r.params.runCallbacksOnInit,!0)),r.params.hashNavigation.watchState&&l.addEventListener("hashchange",t))}),e("destroy",function(){r.params.hashNavigation.enabled&&r.params.hashNavigation.watchState&&l.removeEventListener("hashchange",t)}),e("transitionEnd _freeModeNoMomentumRelease",function(){n&&a()}),e("slideChange",function(){n&&r.params.cssMode&&a()})},function(e){var s,n,o=e.swiper,t=e.extendParams,a=e.on,l=e.emit,e=e.params;t({autoplay:{enabled:!(o.autoplay={running:!1,paused:!1,timeLeft:0}),delay:3e3,waitForTransition:!0,disableOnInteraction:!1,stopOnLastSlide:!1,reverseDirection:!1,pauseOnMouseEnter:!1}});var d,r,i,c,p,u,m,f,h=e&&e.autoplay?e.autoplay.delay:3e3,v=e&&e.autoplay?e.autoplay.delay:3e3,g=(new Date).getTime();function y(e){o&&!o.destroyed&&o.wrapperEl&&e.target===o.wrapperEl&&(o.wrapperEl.removeEventListener("transitionend",y),f||e.detail&&e.detail.bySwiperTouchMove||P())}function b(){g=(new Date).getTime(),o.autoplay.running=!0,C(),l("autoplayStart")}function w(){o.autoplay.running=!1,clearTimeout(s),cancelAnimationFrame(n),l("autoplayStop")}function E(e,t){!o.destroyed&&o.autoplay.running&&(clearTimeout(s),e||(m=!0),e=function(){l("autoplayPause"),o.params.autoplay.waitForTransition?o.wrapperEl.addEventListener("transitionend",y):P()},o.autoplay.paused=!0,t?(u&&(d=o.params.autoplay.delay),u=!1,e()):(t=d||o.params.autoplay.delay,d=t-((new Date).getTime()-g),o.isEnd&&d<0&&!o.params.loop||(d<0&&(d=0),e())))}function x(){var e;!o.destroyed&&o.autoplay.running&&("hidden"===(e=I()).visibilityState&&E(m=!0),"visible"===e.visibilityState)&&P()}function S(e){"mouse"===e.pointerType&&(f=m=!0,o.animating||o.autoplay.paused||E(!0))}function T(e){"mouse"===e.pointerType&&(f=!1,o.autoplay.paused)&&P()}var M=function(){var e;!o.destroyed&&o.autoplay.running&&(o.autoplay.paused?r=!0:r&&(v=d,r=!1),e=o.autoplay.paused?d:g+v-(new Date).getTime(),o.autoplay.timeLeft=e,l("autoplayTimeLeft",e,e/h),n=requestAnimationFrame(function(){M()}))},C=function(e){var t,a,r,i;if(!o.destroyed&&o.autoplay.running)return cancelAnimationFrame(n),M(),t=void 0===e?o.params.autoplay.delay:e,h=o.params.autoplay.delay,v=o.params.autoplay.delay,a=function(){var e=o.virtual&&o.params.virtual.enabled?o.slides.find(function(e){return e.classList.contains("swiper-slide-active")}):o.slides[o.activeIndex];if(e)return parseInt(e.getAttribute("data-swiper-autoplay"),10)}(),!Number.isNaN(a)&&0<a&&void 0===e&&(v=h=t=a),d=t,r=o.params.speed,i=function(){o&&!o.destroyed&&(o.params.autoplay.reverseDirection?!o.isBeginning||o.params.loop||o.params.rewind?(o.slidePrev(r,!0,!0),l("autoplay")):o.params.autoplay.stopOnLastSlide||(o.slideTo(o.slides.length-1,r,!0,!0),l("autoplay")):!o.isEnd||o.params.loop||o.params.rewind?(o.slideNext(r,!0,!0),l("autoplay")):o.params.autoplay.stopOnLastSlide||(o.slideTo(0,r,!0,!0),l("autoplay")),o.params.cssMode)&&(g=(new Date).getTime(),requestAnimationFrame(function(){C()}))},0<t?(clearTimeout(s),s=setTimeout(function(){i()},t)):requestAnimationFrame(function(){i()}),t},P=function(){o.isEnd&&d<0&&!o.params.loop||o.destroyed||!o.autoplay.running||(g=(new Date).getTime(),m?(m=!1,C(d)):C(),o.autoplay.paused=!1,l("autoplayResume"))};a("init",function(){o.params.autoplay.enabled&&(o.params.autoplay.pauseOnMouseEnter&&(o.el.addEventListener("pointerenter",S),o.el.addEventListener("pointerleave",T)),I().addEventListener("visibilitychange",x),b())}),a("destroy",function(){o.el&&"string"!=typeof o.el&&(o.el.removeEventListener("pointerenter",S),o.el.removeEventListener("pointerleave",T)),I().removeEventListener("visibilitychange",x),o.autoplay.running&&w()}),a("_freeModeStaticRelease",function(){(c||m)&&P()}),a("_freeModeNoMomentumRelease",function(){o.params.autoplay.disableOnInteraction?w():E(!0,!0)}),a("beforeTransitionStart",function(e,t,a){!o.destroyed&&o.autoplay.running&&(a||!o.params.autoplay.disableOnInteraction?E(!0,!0):w())}),a("sliderFirstMove",function(){!o.destroyed&&o.autoplay.running&&(o.params.autoplay.disableOnInteraction?w():(m=c=!(i=!0),p=setTimeout(function(){E(c=m=!0)},200)))}),a("touchEnd",function(){!o.destroyed&&o.autoplay.running&&i&&(clearTimeout(p),clearTimeout(s),i=c=(o.params.autoplay.disableOnInteraction||c&&o.params.cssMode&&P(),!1))}),a("slideChange",function(){!o.destroyed&&o.autoplay.running&&(u=!0)}),Object.assign(o.autoplay,{start:b,stop:w,pause:E,resume:P})},function(e){var u=e.swiper,t=e.extendParams,e=e.on,a=(t({thumbs:{swiper:null,multipleActiveThumbs:!0,autoScrollOffset:0,slideThumbActiveClass:"swiper-slide-thumb-active",thumbsContainerClass:"swiper-thumbs"}}),!1),r=!1;function i(){var e,t,a=u.thumbs.swiper;!a||a.destroyed||(e=a.clickedIndex,(t=a.clickedSlide)&&t.classList.contains(u.params.thumbs.slideThumbActiveClass))||null!=e&&(t=a.params.loop?parseInt(a.clickedSlide.getAttribute("data-swiper-slide-index"),10):e,u.params.loop?u.slideToLoop(t):u.slideTo(t))}function n(){var e=u.params.thumbs;if(a)return!1;a=!0;var t=u.constructor;return e.swiper instanceof t?(u.thumbs.swiper=e.swiper,Object.assign(u.thumbs.swiper.originalParams,{watchSlidesProgress:!0,slideToClickedSlide:!1}),Object.assign(u.thumbs.swiper.params,{watchSlidesProgress:!0,slideToClickedSlide:!1}),u.thumbs.swiper.update()):m(e.swiper)&&(e=Object.assign({},e.swiper),Object.assign(e,{watchSlidesProgress:!0,slideToClickedSlide:!1}),u.thumbs.swiper=new t(e),r=!0),u.thumbs.swiper.el.classList.add(u.params.thumbs.thumbsContainerClass),u.thumbs.swiper.on("tap",i),!0}function o(e){var t=u.thumbs.swiper;if(t&&!t.destroyed){var a="auto"===t.params.slidesPerView?t.slidesPerViewDynamic():t.params.slidesPerView,r=1,i=u.params.thumbs.slideThumbActiveClass;if(1<u.params.slidesPerView&&!u.params.centeredSlides&&(r=u.params.slidesPerView),u.params.thumbs.multipleActiveThumbs||(r=1),r=Math.floor(r),t.slides.forEach(function(e){return e.classList.remove(i)}),t.params.loop||t.params.virtual&&t.params.virtual.enabled)for(var s=0;s<r;s+=1)Q(t.slidesEl,'[data-swiper-slide-index="'.concat(u.realIndex+s,'"]')).forEach(function(e){e.classList.add(i)});else for(var n=0;n<r;n+=1)t.slides[u.realIndex+n]&&t.slides[u.realIndex+n].classList.add(i);var o,l,d,c=u.params.thumbs.autoScrollOffset,p=c&&!t.params.loop;(u.realIndex!==t.realIndex||p)&&(o=t.activeIndex,d=t.params.loop?(l=t.slides.find(function(e){return e.getAttribute("data-swiper-slide-index")==="".concat(u.realIndex)}),l=t.slides.indexOf(l),u.activeIndex>u.previousIndex?"next":"prev"):(l=u.realIndex)>u.previousIndex?"next":"prev",p&&(l+="next"===d?c:-1*c),t.visibleSlidesIndexes)&&t.visibleSlidesIndexes.indexOf(l)<0&&(t.params.centeredSlides?l=o<l?l-Math.floor(a/2)+1:l+Math.floor(a/2)-1:o<l&&t.params.slidesPerGroup,t.slideTo(l,e?0:void 0))}}u.thumbs={swiper:null},e("beforeInit",function(){var e,i,s=u.params.thumbs;s&&s.swiper&&("string"==typeof s.swiper||s.swiper instanceof HTMLElement?(e=I(),i=function(){var t,a,r;u.destroyed||((r="string"==typeof s.swiper?e.querySelector(s.swiper):s.swiper)&&r.swiper?(s.swiper=r.swiper,n(),o(!0)):r&&(t="".concat(u.params.eventsPrefix,"init"),r.addEventListener(t,a=function(e){s.swiper=e.detail[0],r.removeEventListener(t,a),n(),o(!0),s.swiper.update(),u.update()})),r)||requestAnimationFrame(i)},requestAnimationFrame(i)):(n(),o(!0)))}),e("slideChange update resize observerUpdate",function(){o()}),e("setTransition",function(e,t){var a=u.thumbs.swiper;a&&!a.destroyed&&a.setTransition(t)}),e("beforeDestroy",function(){var e=u.thumbs.swiper;e&&!e.destroyed&&r&&e.destroy()}),Object.assign(u.thumbs,{init:n,update:o})},function(e){var f=e.swiper,t=e.extendParams,h=e.emit,v=e.once;t({freeMode:{enabled:!1,momentum:!0,momentumRatio:1,momentumBounce:!0,momentumBounceRatio:1,momentumVelocityRatio:1,sticky:!1,minimumVelocity:.02}}),Object.assign(f,{freeMode:{onTouchStart:function(){var e;f.params.cssMode||(e=f.getTranslate(),f.setTranslate(e),f.setTransition(0),f.touchEventsData.velocities.length=0,f.freeMode.onTouchEnd({currentPos:f.rtl?f.translate:-f.translate}))},onTouchMove:function(){var e,t;f.params.cssMode||(e=f.touchEventsData,t=f.touches,0===e.velocities.length&&e.velocities.push({position:t[f.isHorizontal()?"startX":"startY"],time:e.touchStartTime}),e.velocities.push({position:t[f.isHorizontal()?"currentX":"currentY"],time:b()}))},onTouchEnd:function(e){e=e.currentPos;if(!f.params.cssMode){var t=f.params,a=f.wrapperEl,r=f.rtlTranslate,i=f.snapGrid,s=f.touchEventsData,n=b()-s.touchStartTime;if(e<-f.minTranslate())f.slideTo(f.activeIndex);else if(e>-f.maxTranslate())f.slides.length<i.length?f.slideTo(i.length-1):f.slideTo(f.slides.length-1);else{if(t.freeMode.momentum){(!(1<s.velocities.length)||(e=s.velocities.pop(),l=s.velocities.pop(),o=e.position-l.position,l=e.time-l.time,f.velocity=o/l,f.velocity/=2,Math.abs(f.velocity)<t.freeMode.minimumVelocity&&(f.velocity=0),150<l)||300<b()-e.time)&&(f.velocity=0),f.velocity*=t.freeMode.momentumVelocityRatio,s.velocities.length=0;var o=1e3*t.freeMode.momentumRatio,l=f.velocity*o,d=f.translate+l;r&&(d=-d);var c,p,e=!1,l=20*Math.abs(f.velocity)*t.freeMode.momentumBounceRatio;if(d<f.maxTranslate())t.freeMode.momentumBounce?(d+f.maxTranslate()<-l&&(d=f.maxTranslate()-l),c=f.maxTranslate(),s.allowMomentumBounce=e=!0):d=f.maxTranslate(),t.loop&&t.centeredSlides&&(p=!0);else if(d>f.minTranslate())t.freeMode.momentumBounce?(d-f.minTranslate()>l&&(d=f.minTranslate()+l),c=f.minTranslate(),s.allowMomentumBounce=e=!0):d=f.minTranslate(),t.loop&&t.centeredSlides&&(p=!0);else if(t.freeMode.sticky){for(var u,m=0;m<i.length;m+=1)if(i[m]>-d){u=m;break}d=-(d=Math.abs(i[u]-d)<Math.abs(i[u-1]-d)||"next"===f.swipeDirection?i[u]:i[u-1])}if(p&&v("transitionEnd",function(){f.loopFix()}),0!==f.velocity)o=r?Math.abs((-d-f.translate)/f.velocity):Math.abs((d-f.translate)/f.velocity),t.freeMode.sticky&&(o=(l=Math.abs((r?-d:d)-f.translate))<(p=f.slidesSizesGrid[f.activeIndex])?t.speed:l<2*p?1.5*t.speed:2.5*t.speed);else if(t.freeMode.sticky)return void f.slideToClosest();t.freeMode.momentumBounce&&e?(f.updateProgress(c),f.setTransition(o),f.setTranslate(d),f.transitionStart(!0,f.swipeDirection),f.animating=!0,g(a,function(){f&&!f.destroyed&&s.allowMomentumBounce&&(h("momentumBounce"),f.setTransition(t.speed),setTimeout(function(){f.setTranslate(c),g(a,function(){f&&!f.destroyed&&f.transitionEnd()})},0))})):f.velocity?(h("_freeModeNoMomentumRelease"),f.updateProgress(d),f.setTransition(o),f.setTranslate(d),f.transitionStart(!0,f.swipeDirection),f.animating||(f.animating=!0,g(a,function(){f&&!f.destroyed&&f.transitionEnd()}))):f.updateProgress(d),f.updateActiveIndex(),f.updateSlidesClasses()}else{if(t.freeMode.sticky)return void f.slideToClosest();t.freeMode&&h("_freeModeNoMomentumRelease")}(!t.freeMode.momentum||n>=t.longSwipesMs)&&(h("_freeModeStaticRelease"),f.updateProgress(),f.updateActiveIndex(),f.updateSlidesClasses())}}}}})},function(e){function d(){var e=m.params.spaceBetween;return"string"==typeof e&&0<=e.indexOf("%")?e=parseFloat(e.replace("%",""))/100*m.size:"string"==typeof e&&(e=parseFloat(e)),e}var c,p,u,r,m=e.swiper,t=e.extendParams,e=e.on;t({grid:{rows:1,fill:"column"}});e("init",function(){r=m.params.grid&&1<m.params.grid.rows}),e("update",function(){var e=m.params,t=m.el,a=e.grid&&1<e.grid.rows;r&&!a?(t.classList.remove("".concat(e.containerModifierClass,"grid"),"".concat(e.containerModifierClass,"grid-column")),u=1,m.emitContainerClasses()):!r&&a&&(t.classList.add("".concat(e.containerModifierClass,"grid")),"column"===e.grid.fill&&t.classList.add("".concat(e.containerModifierClass,"grid-column")),m.emitContainerClasses()),r=a}),m.grid={initSlides:function(e){var t=m.params.slidesPerView,a=m.params.grid,r=a.rows,a=a.fill,e=(m.virtual&&m.params.virtual.enabled?m.virtual.slides:e).length;u=Math.floor(e/r),c=Math.floor(e/r)===e/r?e:Math.ceil(e/r)*r,"auto"!==t&&"row"===a&&(c=Math.max(c,t*r)),p=c/r},unsetSlides:function(){m.slides&&m.slides.forEach(function(e){e.swiperSlideGridSet&&(e.style.height="",e.style[m.getDirectionLabel("margin-top")]="")})},updateSlide:function(e,t,a){var r,i,s=m.params.slidesPerGroup,n=d(),o=m.params.grid,l=o.rows,o=o.fill,a=(m.virtual&&m.params.virtual.enabled?m.virtual.slides:a).length;"row"===o&&1<s?(r=e-l*s*(i=Math.floor(e/(s*l))),a=0===i?s:Math.min(Math.ceil((a-i*l*s)/l),s),a=(i=r-(r=Math.floor(r/a))*a+i*s)+r*c/l,t.style.order=a):"column"===o?(r=e-(i=Math.floor(e/l))*l,(u<i||i===u&&r===l-1)&&l<=(r+=1)&&(r=0,i+=1)):i=e-(r=Math.floor(e/p))*p,t.row=r,t.column=i,t.style.height="calc((100% - ".concat((l-1)*n,"px) / ").concat(l,")"),t.style[m.getDirectionLabel("margin-top")]=0!==r?n&&"".concat(n,"px"):"",t.swiperSlideGridSet=!0},updateWrapperSize:function(e,t){var a=m.params,r=a.centeredSlides,i=a.roundLengths,a=d(),s=m.params.grid.rows;if(m.virtualSize=(e+a)*c,m.virtualSize=Math.ceil(m.virtualSize/s)-a,m.params.cssMode||(m.wrapperEl.style[m.getDirectionLabel("width")]="".concat(m.virtualSize+a,"px")),r){for(var n=[],o=0;o<t.length;o+=1){var l=t[o];i&&(l=Math.floor(l)),t[o]<m.virtualSize+t[0]&&n.push(l)}t.splice(0,t.length),t.push.apply(t,n)}}}},function(e){e=e.swiper;Object.assign(e,{appendSlide:U.bind(e),prependSlide:K.bind(e),addSlide:function(e,t){var a=this,r=a.params,i=a.activeIndex,s=a.slidesEl,n=(r.loop&&(i-=a.loopedSlides,a.loopDestroy(),a.recalcSlides()),a.slides.length);if(e<=0)a.prependSlide(t);else if(n<=e)a.appendSlide(t);else{for(var o=e<i?i+1:i,l=[],d=n-1;e<=d;--d){var c=a.slides[d];c.remove(),l.unshift(c)}if("object"==_typeof(t)&&"length"in t){for(var p=0;p<t.length;p+=1)t[p]&&s.append(t[p]);o=e<i?i+t.length:i}else s.append(t);for(var u=0;u<l.length;u+=1)s.append(l[u]);a.recalcSlides(),r.loop&&a.loopCreate(),r.observer&&!a.isElement||a.update(),r.loop?a.slideTo(o+a.loopedSlides,0,!1):a.slideTo(o,0,!1)}}.bind(e),removeSlide:function(e){var t=this,a=t.params,r=t.activeIndex;a.loop&&(r-=t.loopedSlides,t.loopDestroy());var i,s=r;if("object"==_typeof(e)&&"length"in e)for(var n=0;n<e.length;n+=1)i=e[n],t.slides[i]&&t.slides[i].remove(),i<s&&--s;else t.slides[i=e]&&t.slides[i].remove(),i<s&&--s;s=Math.max(s,0),t.recalcSlides(),a.loop&&t.loopCreate(),a.observer&&!t.isElement||t.update(),a.loop?t.slideTo(s+t.loopedSlides,0,!1):t.slideTo(s,0,!1)}.bind(e),removeAllSlides:function(){for(var e=[],t=0;t<this.slides.length;t+=1)e.push(t);this.removeSlide(e)}.bind(e)})},function(e){var n=e.swiper,t=e.extendParams,e=e.on;t({fadeEffect:{crossFade:!1}}),l({effect:"fade",swiper:n,on:e,setTranslate:function(){var e=n.slides;n.params.fadeEffect;for(var t=0;t<e.length;t+=1){var a=n.slides[t],r=-a.swiperSlideOffset,i=(n.params.virtualTranslate||(r-=n.translate),0),s=(n.isHorizontal()||(i=r,r=0),n.params.fadeEffect.crossFade?Math.max(1-Math.abs(a.progress),0):1+Math.min(Math.max(a.progress,-1),0)),a=x(0,a);a.style.opacity=s,a.style.transform="translate3d(".concat(r,"px, ").concat(i,"px, 0px)")}},setTransition:function(t){var e=n.slides.map(i);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms")}),d({swiper:n,duration:t,transformElements:e,allSlides:!0})},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!n.params.cssMode}}})},function(e){function T(e,t,a){var r=a?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),i=a?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom");r||(r=C("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(a?"left":"top")).split(" ")),e.append(r)),i||(i=C("div",("swiper-slide-shadow-cube swiper-slide-shadow-"+(a?"right":"bottom")).split(" ")),e.append(i)),r&&(r.style.opacity=Math.max(-t,0)),i&&(i.style.opacity=Math.max(t,0))}var M=e.swiper,t=e.extendParams,e=e.on;t({cubeEffect:{slideShadows:!0,shadow:!0,shadowOffset:20,shadowScale:.94}});l({effect:"cube",swiper:M,on:e,setTranslate:function(){var e=M.el,t=M.wrapperEl,a=M.slides,r=M.width,i=M.height,s=M.rtlTranslate,n=M.size,o=M.browser,l=L(M),d=M.params.cubeEffect,c=M.isHorizontal(),p=M.virtual&&M.params.virtual.enabled,u=0;d.shadow&&(c?((S=M.wrapperEl.querySelector(".swiper-cube-shadow"))||(S=C("div","swiper-cube-shadow"),M.wrapperEl.append(S)),S.style.height="".concat(r,"px")):(S=e.querySelector(".swiper-cube-shadow"))||(S=C("div","swiper-cube-shadow"),e.append(S)));for(var m,f=0;f<a.length;f+=1){var h=a[f],v=f,g=90*(v=p?parseInt(h.getAttribute("data-swiper-slide-index"),10):v),y=Math.floor(g/360),b=(s&&(g=-g,y=Math.floor(-g/360)),Math.max(Math.min(h.progress,1),-1)),w=0,E=0,x=0,y=(v%4==0?(w=4*-y*n,x=0):(v-1)%4==0?(w=0,x=4*-y*n):(v-2)%4==0?(w=n+4*y*n,x=n):(v-3)%4==0&&(w=-n,x=3*n+4*n*y),s&&(w=-w),c||(E=w,w=0),"rotateX(".concat(l(c?0:-g),"deg) rotateY(").concat(l(c?g:0),"deg) translate3d(").concat(w,"px, ").concat(E,"px, ").concat(x,"px)"));b<=1&&-1<b&&(u=90*v+90*b,s)&&(u=90*-v-90*b),h.style.transform=y,d.slideShadows&&T(h,b,c)}t.style.transformOrigin="50% 50% -".concat(n/2,"px"),t.style["-webkit-transform-origin"]="50% 50% -".concat(n/2,"px"),d.shadow&&(c?S.style.transform="translate3d(0px, ".concat(r/2+d.shadowOffset,"px, ").concat(-r/2,"px) rotateX(89.99deg) rotateZ(0deg) scale(").concat(d.shadowScale,")"):(e=Math.abs(u)-90*Math.floor(Math.abs(u)/90),r=1.5-(Math.sin(2*e*Math.PI/360)/2+Math.cos(2*e*Math.PI/360)/2),e=d.shadowScale,r=d.shadowScale/r,m=d.shadowOffset,S.style.transform="scale3d(".concat(e,", 1, ").concat(r,") translate3d(0px, ").concat(i/2+m,"px, ").concat(-i/2/r,"px) rotateX(-89.99deg)")));var S=(o.isSafari||o.isWebView)&&o.needPerspectiveFix?-n/2:0;t.style.transform="translate3d(0px,0,".concat(S,"px) rotateX(").concat(l(M.isHorizontal()?0:u),"deg) rotateY(").concat(l(M.isHorizontal()?-u:0),"deg)"),t.style.setProperty("--swiper-cube-translate-z","".concat(S,"px"))},setTransition:function(t){var e=M.el;M.slides.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),M.params.cubeEffect.shadow&&!M.isHorizontal()&&(e=e.querySelector(".swiper-cube-shadow"))&&(e.style.transitionDuration="".concat(t,"ms"))},recreateShadows:function(){var a=M.isHorizontal();M.slides.forEach(function(e){var t=Math.max(Math.min(e.progress,1),-1);T(e,t,a)})},getEffectParams:function(){return M.params.cubeEffect},perspective:function(){return!0},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,resistanceRatio:0,spaceBetween:0,centeredSlides:!1,virtualTranslate:!0}}})},function(e){function p(e,t){var a=u.isHorizontal()?e.querySelector(".swiper-slide-shadow-left"):e.querySelector(".swiper-slide-shadow-top"),r=u.isHorizontal()?e.querySelector(".swiper-slide-shadow-right"):e.querySelector(".swiper-slide-shadow-bottom"),a=a||S("flip",e,u.isHorizontal()?"left":"top");r=r||S("flip",e,u.isHorizontal()?"right":"bottom"),a&&(a.style.opacity=Math.max(-t,0)),r&&(r.style.opacity=Math.max(t,0))}var u=e.swiper,t=e.extendParams,e=e.on;t({flipEffect:{slideShadows:!0,limitRotation:!0}});l({effect:"flip",swiper:u,on:e,setTranslate:function(){for(var e=u.slides,t=u.rtlTranslate,a=u.params.flipEffect,r=L(u),i=0;i<e.length;i+=1){var s=e[i],n=s.progress,o=(u.params.flipEffect.limitRotation&&(n=Math.max(Math.min(s.progress,1),-1)),s.swiperSlideOffset),l=-180*n,d=0,o=u.params.cssMode?-o-u.translate:-o,c=0,n=(u.isHorizontal()?t&&(l=-l):(c=o,d=-l,l=o=0),s.style.zIndex=-Math.abs(Math.round(n))+e.length,a.slideShadows&&p(s,n),"translate3d(".concat(o,"px, ").concat(c,"px, 0px) rotateX(").concat(r(d),"deg) rotateY(").concat(r(l),"deg)"));x(0,s).style.transform=n}},setTransition:function(t){var e=u.slides.map(i);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),d({swiper:u,duration:t,transformElements:e})},recreateShadows:function(){u.params.flipEffect,u.slides.forEach(function(e){var t=e.progress;u.params.flipEffect.limitRotation&&(t=Math.max(Math.min(e.progress,1),-1)),p(e,t)})},getEffectParams:function(){return u.params.flipEffect},perspective:function(){return!0},overwriteParams:function(){return{slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!u.params.cssMode}}})},function(e){var E=e.swiper,t=e.extendParams,e=e.on;t({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}}),l({effect:"coverflow",swiper:E,on:e,setTranslate:function(){for(var e=E.width,t=E.height,a=E.slides,r=E.slidesSizesGrid,i=E.params.coverflowEffect,s=E.isHorizontal(),n=E.translate,o=s?e/2-n:t/2-n,l=s?i.rotate:-i.rotate,d=i.depth,c=L(E),p=0,u=a.length;p<u;p+=1){var m=a[p],f=r[p],h=(o-m.swiperSlideOffset-f/2)/f,h="function"==typeof i.modifier?i.modifier(h):h*i.modifier,v=s?l*h:0,g=s?0:l*h,y=-d*Math.abs(h),b=i.stretch,f=("string"==typeof b&&-1!==b.indexOf("%")&&(b=parseFloat(i.stretch)/100*f),s?0:b*h),b=s?b*h:0,w=1-(1-i.scale)*Math.abs(h),b=(Math.abs(b)<.001&&(b=0),Math.abs(f)<.001&&(f=0),Math.abs(y)<.001&&(y=0),Math.abs(v)<.001&&(v=0),Math.abs(g)<.001&&(g=0),Math.abs(w)<.001&&(w=0),"translate3d(".concat(b,"px,").concat(f,"px,").concat(y,"px)  rotateX(").concat(c(g),"deg) rotateY(").concat(c(v),"deg) scale(").concat(w,")"));x(0,m).style.transform=b,m.style.zIndex=1-Math.abs(Math.round(h)),i.slideShadows&&(f=s?m.querySelector(".swiper-slide-shadow-left"):m.querySelector(".swiper-slide-shadow-top"),y=s?m.querySelector(".swiper-slide-shadow-right"):m.querySelector(".swiper-slide-shadow-bottom"),f=f||S("coverflow",m,s?"left":"top"),y=y||S("coverflow",m,s?"right":"bottom"),f&&(f.style.opacity=0<h?h:0),y)&&(y.style.opacity=0<-h?-h:0)}},setTransition:function(t){E.slides.map(i).forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})})},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0}}})},function(e){var v=e.swiper,t=e.extendParams,e=e.on;t({creativeEffect:{limitProgress:1,shadowPerProgress:!1,progressMultiplier:1,perspective:!0,prev:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1},next:{translate:[0,0,0],rotate:[0,0,0],opacity:1,scale:1}}});l({effect:"creative",swiper:v,on:e,setTranslate:function(){for(var c=v.slides,e=v.wrapperEl,t=v.slidesSizesGrid,p=v.params.creativeEffect,u=p.progressMultiplier,m=v.params.centeredSlides,f=L(v),h=(m&&(t=t[0]/2-v.params.slidesOffsetBefore||0,e.style.transform="translateX(calc(50% - ".concat(t,"px))")),0);h<c.length;h+=1)!function(){var e=c[h],t=e.progress,r=Math.min(Math.max(e.progress,-p.limitProgress),p.limitProgress),a=r,i=(m||(a=Math.min(Math.max(e.originalProgress,-p.limitProgress),p.limitProgress)),e.swiperSlideOffset),s=[v.params.cssMode?-i-v.translate:-i,0,0],n=[0,0,0],i=!1,o=(v.isHorizontal()||(s[1]=s[0],s[0]=0),{translate:[0,0,0],rotate:[0,0,0],scale:1,opacity:1}),t=(r<0?(o=p.next,i=!0):0<r&&(o=p.prev,i=!0),s.forEach(function(e,t){s[t]="calc(".concat(e,"px + (").concat("string"==typeof(e=o.translate[t])?e:"".concat(e,"px")," * ").concat(Math.abs(r*u),"))")}),n.forEach(function(e,t){var a=o.rotate[t]*Math.abs(r*u);n[t]=a}),e.style.zIndex=-Math.abs(Math.round(t))+c.length,s.join(", ")),l="rotateX(".concat(f(n[0]),"deg) rotateY(").concat(f(n[1]),"deg) rotateZ(").concat(f(n[2]),"deg)"),d="scale(".concat(a<0?1+(1-o.scale)*a*u:1-(1-o.scale)*a*u,")"),a=a<0?1+(1-o.opacity)*a*u:1-(1-o.opacity)*a*u,t="translate3d(".concat(t,") ").concat(l," ").concat(d),i=((i&&o.shadow||!i)&&(l=!(l=e.querySelector(".swiper-slide-shadow"))&&o.shadow?S("creative",e):l)&&(d=p.shadowPerProgress?r*(1/p.limitProgress):r,l.style.opacity=Math.min(Math.max(Math.abs(d),0),1)),x(0,e));i.style.transform=t,i.style.opacity=a,o.origin&&(i.style.transformOrigin=o.origin)}()},setTransition:function(t){var e=v.slides.map(i);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),d({swiper:v,duration:t,transformElements:e,allSlides:!0})},perspective:function(){return v.params.creativeEffect.perspective},overwriteParams:function(){return{watchSlidesProgress:!0,virtualTranslate:!v.params.cssMode}}})},function(e){var w=e.swiper,t=e.extendParams,e=e.on;t({cardsEffect:{slideShadows:!0,rotate:!0,perSlideRotate:2,perSlideOffset:8}}),l({effect:"cards",swiper:w,on:e,setTranslate:function(){for(var e=w.slides,t=w.activeIndex,a=w.rtlTranslate,r=w.params.cardsEffect,i=w.touchEventsData,s=i.startTranslate,n=i.isTouched,o=a?-w.translate:w.translate,l=0;l<e.length;l+=1){var d=e[l],c=d.progress,p=Math.min(Math.max(c,-4),4),u=d.swiperSlideOffset,u=(w.params.centeredSlides&&!w.params.cssMode&&(w.wrapperEl.style.transform="translateX(".concat(w.minTranslate(),"px)")),w.params.centeredSlides&&w.params.cssMode&&(u-=e[0].swiperSlideOffset),w.params.cssMode?-u-w.translate:-u),m=0,f=-100*Math.abs(p),h=1,v=-r.perSlideRotate*p,g=r.perSlideOffset-.75*Math.abs(p),y=w.virtual&&w.params.virtual.enabled?w.virtual.from+l:l,b=(y===t||y===t-1)&&0<p&&p<1&&(n||w.params.cssMode)&&o<s,y=(y===t||y===t+1)&&p<0&&-1<p&&(n||w.params.cssMode)&&s<o,b=((b||y)&&(v+=-28*p*(b=Math.pow(1-Math.abs((Math.abs(p)-.5)/.5),.5)),h+=-.5*b,g+=96*b,m=-25*b*Math.abs(p)+"%"),u=p<0?"calc(".concat(u,"px ").concat(a?"-":"+"," (").concat(g*Math.abs(p),"%))"):0<p?"calc(".concat(u,"px ").concat(a?"-":"+"," (-").concat(g*Math.abs(p),"%))"):"".concat(u,"px"),w.isHorizontal()||(y=m,m=u,u=y),p<0?""+(1+(1-h)*p):""+(1-(1-h)*p)),g="\n        translate3d(".concat(u,", ").concat(m,", ").concat(f,"px)\n        rotateZ(").concat(r.rotate?a?-v:v:0,"deg)\n        scale(").concat(b,")\n      ");r.slideShadows&&(y=(y=d.querySelector(".swiper-slide-shadow"))||S("cards",d))&&(y.style.opacity=Math.min(Math.max((Math.abs(p)-.5)/.5,0),1)),d.style.zIndex=-Math.abs(Math.round(c))+e.length,x(0,d).style.transform=g}},setTransition:function(t){var e=w.slides.map(i);e.forEach(function(e){e.style.transitionDuration="".concat(t,"ms"),e.querySelectorAll(".swiper-slide-shadow").forEach(function(e){e.style.transitionDuration="".concat(t,"ms")})}),d({swiper:w,duration:t,transformElements:e})},perspective:function(){return!0},overwriteParams:function(){return{watchSlidesProgress:!0,virtualTranslate:!w.params.cssMode}}})}]),o}();