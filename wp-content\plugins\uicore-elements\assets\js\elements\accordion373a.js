"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,_toPropertyKey(o.key),o)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function _callSuper(e,t,n){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,n||[],_getPrototypeOf(e).constructor):t.apply(e,n))}function _possibleConstructorReturn(e,t){if(t&&("object"==_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}window.addEventListener("DOMContentLoaded",function(){var t=function(){function e(){return _classCallCheck(this,e),_callSuper(this,e,arguments)}return _inherits(e,elementorModules.frontend.handlers.Base),_createClass(e,[{key:"getDefaultSettings",value:function(){return{selectors:{accordions:".ui-e-accordion-item",accordionsID:".ui-section-id"},scrollSpy:"yes"==this.getElementSettings("active_scrollspy"),animation:this.getElementSettings("accordion_animation"),multiple:this.getElementSettings("multiple"),collapse:this.getElementSettings("collapsible"),hash:"yes"==this.getElementSettings("active_hash"),scrollOffset:Object.values(this.getElementSettings("hash_top_offset")||[0,0]),scrollDelay:Object.values(this.getElementSettings("hash_scrollspy_delay")||[0,0]),scrollDuration:Object.values(this.getElementSettings("hash_scrollspy_duration")||[0,0])}}},{key:"getDefaultElements",value:function(){var e=this.getSettings("selectors");return{$accordions:this.$element.find(e.accordions),$accordionsID:this.$element.find(e.accordionsID)}}},{key:"watchAccordions",value:function(){var e=this.getSettings(),t=this;e.scrollSpy&&this.elements.$accordions.each(function(){"true"===jQuery(this).attr("aria-expanded")&&t.scrollTo(jQuery(this).find(".ui-e-accordion-content"))}),this.elements.$accordions.on("click",function(e){t.toggleAccordion(e)}),this.elements.$accordions.on("keydown",function(e){"Enter"!==e.key&&" "!==e.key||t.toggleAccordion(e)})}},{key:"toggleAccordion",value:function(e){if(!jQuery(e.target).closest("a").length&&!jQuery(e.target).is("a")){e.preventDefault();var t=this.getSettings(),e=jQuery(e.currentTarget),n=e.find(".ui-e-accordion-content"),o=e.find(".ui-e-accordion-title").attr("id"),r=0;switch(t.animation){case"ui-e-animation-acc-slow":r=400;break;case"ui-e-animation-acc-expand":r=250;break;default:r=void 0===t.animation||""===t.animation?0:200}jQuery.easing.uiCustomEase=function(e,t,n,o,r){return 0==t?n:t==r?n+o:(t/=r/2)<1?o/2*Math.pow(2,10*(t-1))+n:o/2*(2-Math.pow(2,-10*--t))+n},"true"===e.attr("aria-expanded")?(t.collapse||1<this.elements.$accordions.filter('[aria-expanded="true"]').length)&&(e.attr("aria-expanded","false"),e.removeClass("ui-open"),n.slideUp(r,"uiCustomEase")):(t.multiple||(this.elements.$accordions.attr("aria-expanded","false"),this.elements.$accordions.removeClass("ui-open"),this.elements.$accordions.find(".ui-e-accordion-content").slideUp(r,"uiCustomEase")),e.attr("aria-expanded","true"),e.addClass("ui-open"),n.slideDown(r,"uiCustomEase"),t.hash&&o&&this.scrollTo(n))}}},{key:"scrollTo",value:function(e){var t=this.getSettings(),n=e.offset().top-parseInt(t.scrollOffset[1]);setTimeout(function(){jQuery("html, body").animate({scrollTop:n},t.scrollDuration[1])},t.scrollDelay[1])}},{key:"moveElements",value:function(){this.elements.$accordionsID.each(function(){var e=jQuery(this).attr("id"),t=e.substring(3),e=jQuery("#"+e),n=jQuery("#"+t);0<n.length?n.detach().appendTo(e):e.html("<p>No element with the <code>"+t+"</code> ID was found on the page.</p>")})}},{key:"bindEvents",value:function(){this.watchAccordions(),elementorFrontend.isEditMode()||this.moveElements()}}])}();jQuery(window).on("elementor/frontend/init",function(){elementorFrontend.hooks.addAction("frontend/element_ready/uicore-accordion.default",function(e){elementorFrontend.elementsHandler.addHandler(t,{$element:e})})})});