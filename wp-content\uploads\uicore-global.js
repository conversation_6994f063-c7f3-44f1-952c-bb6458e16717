(()=>{var e={793:()=>{document.addEventListener("DOMContentLoaded",(function(){if(document.querySelector(".uicore-sticky")||document.querySelector(".uicore-will-be-sticky")){var e=function(){var e=window.matchMedia("(max-width: 1025px)").matches,t=window.scrollY||window.pageYOffset,i=document.querySelector(".uicore-top-bar"),n=i?i.offsetHeight:null,r=120;document.querySelector(".uicore-transparent .ui-header-row1")?r=19:document.querySelector(".ui-header-row1")&&(r=400),null==n&&(n=e?25:r),t>n?setTimeout((function(){document.querySelectorAll(".uicore-navbar").forEach((function(e){e.classList.add("uicore-scrolled")}))}),10):document.querySelectorAll(".uicore-navbar").forEach((function(e){e.classList.remove("uicore-scrolled")})),t>o&&o>0&&t>n-.85*n?document.querySelectorAll(".ui-smart-sticky").forEach((function(e){e.classList.add("ui-hide")})):document.querySelectorAll(".ui-smart-sticky").forEach((function(e){e.classList.remove("ui-hide")})),t+window.innerHeight>document.documentElement.scrollHeight-50&&document.querySelectorAll(".ui-smart-sticky").forEach((function(e){e.classList.remove("ui-hide")})),o=t},o=0;e();var t=(i=e,n=500,function(){var e=this,o=arguments;clearTimeout(r),r=setTimeout((function(){r=null,i.apply(e,o)}),n)});window.addEventListener("scroll",e),document.body.addEventListener("touchmove",t)}var i,n,r})),-1!=navigator.appVersion.indexOf("Win")&&document.body.classList.add("win"),document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll(".uicore-search-btn").forEach((function(e){e.addEventListener("click",(function(){document.body.classList.add("uicore-search-active");var e=document.querySelector(".uicore-search .search-field");e&&e.focus()}))})),document.querySelectorAll(".uicore-search .uicore-close").forEach((function(e){e.addEventListener("click",(function(){document.body.classList.remove("uicore-search-active")}))})),document.addEventListener("keydown",(function(e){27===e.keyCode&&document.body.classList.remove("uicore-search-active")})),document.querySelectorAll(".uicore-h-classic .menu-item-has-children:not(.menu-item-has-megamenu.custom-width)").forEach((function(e){e.addEventListener("mouseenter",(function(o){if(e.querySelector("ul")){var t=e.querySelector(".sub-menu");if(!t)return;t.getBoundingClientRect().left+window.scrollX+t.offsetWidth>document.body.offsetWidth&&e.classList.add("uicore-edge")}})),e.addEventListener("mouseleave",(function(o){e.classList.remove("uicore-edge")}))}));var e=document.getElementById("uicore-back-to-top");e&&(window.addEventListener("scroll",(function(){window.scrollY>300?e.classList.add("uicore-visible"):e.classList.remove("uicore-visible")})),e.addEventListener("click",(function(e){e.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})})))})),document.addEventListener("DOMContentLoaded",(function(){if(document.querySelector(".uicore-progress-bar")){var e,o,t=function(){var e=document.body.scrollHeight-window.innerHeight,o=document.querySelector(".uicore-post-content article");return o&&(e=o.offsetHeight+o.getBoundingClientRect().top+window.scrollY-window.innerHeight),e},i=document.querySelector(".uicore-progress-bar"),n=t(),r=function(){return e=window.scrollY||window.pageYOffset,(o=e/n*100)>100&&(o=100),o+="%"},c=function(){i&&(i.style.width=r())};window.addEventListener("scroll",c),window.addEventListener("resize",(function(){n=t(),c()}))}})),document.addEventListener("DOMContentLoaded",(function(){var e=document.querySelectorAll(".menu-item-has-megamenu.custom-width"),o=function(e){var o=window.matchMedia("(max-width: 1025px)").matches,t=e.querySelector("ul.uicore-megamenu");if(t&&(t.style.left="auto"),!o&&t){t.style.left="0px";var i=t.offsetWidth,n={left:t.getBoundingClientRect().left+window.scrollX},r=window.innerWidth;if(!i||!n)return;if(n.left+i>=r){var c=n.left+i-r;t.style.left=-c-0+"px"}}};e.forEach((function(e){e.addEventListener("mouseenter",(function(t){o(e)})),e.addEventListener("mouseleave",(function(t){o(e)})),o(e),e.classList.add("with-offsets")}))}))},3895:()=>{document.addEventListener("DOMContentLoaded",(function(){var e=window.matchMedia("(max-width: "+window.uicore_frontend.mobile_br+"px)").matches;function o(e,o,t){document.querySelectorAll(e).forEach((function(e){e.addEventListener(o,t)}))}function t(e,o,t){document.querySelectorAll(e).forEach((function(e){e.removeEventListener(o,t)}))}function i(e,o,t){e.style.transition="opacity "+o+"ms",e.style.opacity=t}function n(){t(".uicore-mobile-menu-wrapper .menu-item-has-children > a","click",c),t(".uicore-mobile-menu-wrapper .uicore-menu-container .sub-menu .back > a","click",u),t(".uicore-mobile-menu-wrapper .menu-item-has-children > a:not(.ui-custom-tap)","click",r),t(".uicore-mobile-menu-wrapper li:not(.menu-item-has-children):not(.back) > a","click",E),t(".uicore-mobile-menu-wrapper .uicore-extra a","click",E),t(".uicore-mobile-menu-wrapper.uicore-ham-classic .menu-item-has-children","mouseenter",a),t(".uicore-mobile-menu-wrapper.uicore-ham-classic .menu-item-has-children","mouseleave",l),t(".uicore-mobile-menu-wrapper.uicore-ham-center .menu-item-has-children a, .uicore-mobile-menu-wrapper.uicore-ham-creative .menu-item-has-children a","click",s),t(".uicore-menu-focus .uicore-menu li","mouseenter",d),t(".uicore-menu-focus .uicore-menu li","mouseleave",f),e?document.body.classList.contains("ui-a-dsmm-slide")?(!function(){if(!window.uicoreBackLinks){for(var e=document.querySelectorAll(".menu-item-has-children"),o=0;o<e.length;o++){var t=uicore_frontend.back;t||(t=e[o].children[0].innerText);var i=e[o],n=document.createElement("a");n.href="#",n.textContent=t;var r=document.createElement("li");r.className="menu-item back",r.appendChild(n),i.children[1]&&i.children[1].insertBefore(r,i.children[1].firstChild)}window.uicoreBackLinks=!0}}(),o(".uicore-mobile-menu-wrapper .menu-item-has-children > a:not(.ui-custom-tap)","click",c),o(".uicore-mobile-menu-wrapper .uicore-menu-container .sub-menu .back > a","click",u)):o(".uicore-mobile-menu-wrapper .menu-item-has-children > a:not(.ui-custom-tap)","click",r):(o(".uicore-mobile-menu-wrapper.uicore-ham-classic .menu-item-has-children","mouseenter",a),o(".uicore-mobile-menu-wrapper.uicore-ham-classic .menu-item-has-children","mouseleave",l),o(".uicore-mobile-menu-wrapper.uicore-ham-center .menu-item-has-children > a, .uicore-mobile-menu-wrapper.uicore-ham-creative .menu-item-has-children > a","click",s),o(".uicore-mobile-menu-wrapper.uicore-ham-center .menu-item-has-children > a, .uicore-mobile-menu-wrapper.uicore-ham-creative .menu-item-has-children > a","click",m),o(".uicore-menu-focus .uicore-menu li","mouseenter",d),o(".uicore-menu-focus .uicore-menu li","mouseleave",f)),o(".uicore-mobile-menu-wrapper li:not(.menu-item-has-children):not(.back) > a","click",E),o(".uicore-mobile-menu-wrapper .uicore-extra a","click",E)}function r(e){if(e.preventDefault(),window.closeOtherSubMenus){var o=this.parentElement;Array.from(o.parentElement.children).filter((function(e){return e!==o})).forEach((function(e){e.classList.remove("ui-expanded");var o=e.querySelector(".sub-menu");o&&(o.style.display="none")}))}var t=this.parentElement.querySelector(".sub-menu");t&&("block"===t.style.display?t.style.display="none":t.style.display="block"),this.classList.toggle("ui-expanded"),Array.from(this.parentElement.children).forEach((function(e){e!==this&&e.classList.toggle("uicore-active")}),this),"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow(),S()}function c(e){e.preventDefault();var o="";document.querySelectorAll(".uicore-mobile-menu-wrapper .uicore-menu").forEach((function(e){e.classList.add("uicore-animating")})),Array.from(this.parentElement.children).forEach((function(e){e!==this&&e.classList.add("uicore-active")}),this),"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow();var t=document.querySelector(".uicore-mobile-menu-wrapper .uicore-menu-container .uicore-menu"),i=t?t.style.left:"";"0"==i||"0%"==i||""==i?o="-100%":(i=i.replace("-","").replace("%",""),o="-"+(parseInt(i)+100)+"%"),"1"===uicore_frontend.rtl&&(o=o.replace("-","+")),"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow(),setTimeout((function(){t&&(t.style.transition="left 150ms",t.style.left=o,setTimeout((function(){S(),t.classList.remove("uicore-animating")}),150))}),100)}function u(e){e.preventDefault();var o="";document.querySelectorAll(".uicore-mobile-menu-wrapper .uicore-menu-container .uicore-menu").forEach((function(e){e.classList.add("uicore-animating")}));var t=document.querySelector(".uicore-mobile-menu-wrapper .uicore-menu-container .uicore-menu"),i=t?t.style.left:"";"-100%"==i||"0%"==i||""==i||"+100%"==i||"100%"==i?o="0%":(i=i.replace("-","").replace("%",""),o="-"+(parseInt(i)-100)+"%"),"1"===uicore_frontend.rtl&&(o=o.replace("-","+"));var n=this;setTimeout((function(){var e=n.parentElement;e&&e.parentElement&&e.parentElement.classList.remove("uicore-active"),"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow()}),300),setTimeout((function(){t&&(t.style.transition="left 150ms",t.style.left=o,setTimeout((function(){document.querySelectorAll(".uicore-mobile-menu-wrapper .uicore-menu .sub-menu:not(.uicore-active) li").forEach((function(e){e.classList.remove("uicore-visible")})),S(),t.classList.remove("uicore-animating")}),150))}),100)}function a(e){"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow();var o=this.querySelector(".sub-menu");o&&o.classList.add("uicore-active"),S()}function l(e){var o=this.querySelectorAll(".sub-menu");o.length&&o[o.length-1].classList.remove("uicore-active"),this.querySelectorAll("li").forEach((function(e){e.classList.remove("uicore-visible")})),"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow()}function s(e){var o=this.parentElement,t=o.querySelector(".sub-menu");t&&(t.classList.toggle("uicore-active"),"block"===t.style.display?t.style.display="none":t.style.display="block",t.classList.contains("uicore-active")?S():o.querySelectorAll("li").forEach((function(e){e.classList.remove("uicore-visible")})))}function m(o){o.preventDefault();var t=this.getAttribute("href");/^#.+$/.test(t)&&document.body.classList.contains("uicore-overflow-hidden")&&(document.querySelectorAll(".uicore-mobile-menu-overflow").forEach((function(e){e.classList.remove("uicore-overflow-hidden")})),document.body.classList.remove("uicore-overflow-hidden"),document.body.classList.remove("uicore-mobile-nav-show"),e?document.querySelector(".uicore-animate-fade")?document.querySelectorAll(".uicore-navigation-wrapper").forEach((function(e){e.style.transition="opacity 100ms",e.style.opacity=0})):setTimeout((function(){document.querySelectorAll(".uicore-navigation-wrapper").forEach((function(e){e.style.transition="opacity 50ms",e.style.opacity=0}))}),50):document.querySelectorAll(".uicore-navigation-wrapper").forEach((function(e){e.style.transition="opacity 100ms",e.style.opacity=0})),window.location.href=t)}function d(){Array.from(this.parentNode.children).filter((function(e){return e!==this}),this).forEach((function(e){i(e,300,.4)})),Array.from(this.parentNode.parentNode.children).filter((function(e){return e!==this.parentNode}),this).forEach((function(e){i(e,300,.3)}))}function f(){Array.from(this.parentNode.children).filter((function(e){return e!==this}),this).forEach((function(e){i(e,300,1)})),Array.from(this.parentNode.parentNode.children).filter((function(e){return e!==this.parentNode}),this).forEach((function(e){i(e,300,1)}))}n(),window.onresize=n,document.querySelectorAll(".uicore-toggle").forEach((function(e){e.addEventListener("click",E)}));var h=document.querySelectorAll(".uicore-ham-reveal"),p=document.querySelector(".uicore-mobile-menu-wrapper .uicore-menu-container .uicore-menu"),w=document.querySelector(".uicore-navigation-wrapper"),y=document.querySelectorAll(".uicore-mobile-menu-overflow"),v=document.querySelectorAll(".uicore-mobile-menu-wrapper .uicore-menu-container"),b=document.querySelectorAll(".uicore-mobile-menu-wrapper li"),L=document.querySelectorAll(".uicore-ham-reveal");function E(o){if(!window.uicoreAnimation){window.uicoreAnimation=!0;var t=this.getAttribute("href");if(t){var i=t.split("#");if(i[0]&&i[0]!=window.location.pathname&&i[0]!=window.location.href.split("#")[0])return}if(p.classList.toggle("uicore-active"),document.body.classList.contains("uicore-overflow-hidden"))"function"==typeof window.uicoreBeforeMobileMenuHide&&window.uicoreBeforeMobileMenuHide(),y.forEach((function(e){e.classList.remove("uicore-overflow-hidden")})),document.body.classList.remove("uicore-overflow-hidden"),document.documentElement.classList.remove("uicore-overflow-hidden"),document.body.classList.remove("uicore-mobile-nav-show"),e?document.querySelector(".uicore-animate-fade")?requestAnimationFrame((function(){w.style.transition="opacity 100ms",w.style.opacity=0})):setTimeout((function(){requestAnimationFrame((function(){w.style.transition="opacity 50ms",w.style.opacity=0}))}),50):requestAnimationFrame((function(){w.style.transition="opacity 100ms",w.style.opacity=0})),b.forEach((function(e){e.classList.remove("uicore-visible")})),L.forEach((function(e){e.style.animationName="none"}));else{"function"==typeof window.uicoreBeforeMobileMenuShow&&window.uicoreBeforeMobileMenuShow();var n=0;h.length?(e||(h.forEach((function(e){requestAnimationFrame((function(){e.style.animationName="uiCoreAnimationsHamReveal"}))})),n=100),setTimeout((function(){requestAnimationFrame((function(){w.style.opacity=1}))}),0+n)):(n=20,setTimeout((function(){requestAnimationFrame((function(){w.style.transition="opacity 100ms",w.style.opacity=1}))}),n)),document.body.classList.add("uicore-overflow-hidden"),document.documentElement.classList.add("uicore-overflow-hidden"),document.body.classList.add("uicore-mobile-nav-show"),setTimeout((function(){y.forEach((function(e){e.classList.add("uicore-overflow-hidden")})),v.forEach((function(e){requestAnimationFrame((function(){e.style.left="0%"}))})),S()}),n)}this.classList.toggle("collapsed"),setTimeout((function(){window.uicoreAnimation=!1}),50)}}function S(){var e=document.querySelectorAll("ul.uicore-active > li:not(.uicore-visible)");e.length&&e.forEach((function(e,o){setTimeout((function(){e.classList.add("uicore-visible"),e.classList.contains("menu-item-has-children")&&function(e){var o=e||document.querySelectorAll(".uicore-mobile-menu-wrapper .uicore-menu-container .menu-item-has-children");(o=o instanceof NodeList?Array.from(o):Array.isArray(o)?o:[o]).forEach((function(e){var o=e.querySelector(":scope > a"),t=o?o.getAttribute("href"):null;if(e.querySelectorAll(".ui-custom-tap").forEach((function(e){e.remove()})),t&&"#"!==t){var i=document.createElement("a");i.href=t,i.textContent="",i.className="ui-custom-tap",i.style.minHeight=e.offsetHeight+"px",i.style.transform="translate3d(0,-"+e.offsetHeight+"px,0)",o&&o.nextSibling?e.insertBefore(i,o.nextSibling):o&&e.appendChild(i)}}))}(e)}),50*o)}))}}))},7952:()=>{document.addEventListener("DOMContentLoaded",(function(){document.querySelectorAll(".menu-item-object-uicore-tb").forEach((function(e){["mouseenter","mouseleave"].forEach((function(o){e.addEventListener(o,(function(){e.querySelectorAll(".elementor-element").forEach((function(e){elementorFrontend.elementsHandler.runReadyTrigger(e)}))}))}))}))}))}},o={};function t(i){var n=o[i];if(void 0!==n)return n.exports;var r=o[i]={exports:{}};return e[i](r,r.exports,t),r.exports}t.n=e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return t.d(o,{a:o}),o},t.d=(e,o)=>{for(var i in o)t.o(o,i)&&!t.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:o[i]})},t.o=(e,o)=>Object.prototype.hasOwnProperty.call(e,o),(()=>{"use strict";t(793),t(3895),t(7952)})()})();function _toConsumableArray(r){return _arrayWithoutHoles(r)||_iterableToArray(r)||_unsupportedIterableToArray(r)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(r,t){var a;if(r)return"string"==typeof r?_arrayLikeToArray(r,t):"Map"===(a="Object"===(a={}.toString.call(r).slice(8,-1))&&r.constructor?r.constructor.name:a)||"Set"===a?Array.from(r):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(r,t):void 0}function _iterableToArray(r){if("undefined"!=typeof Symbol&&null!=r[Symbol.iterator]||null!=r["@@iterator"])return Array.from(r)}function _arrayWithoutHoles(r){if(Array.isArray(r))return _arrayLikeToArray(r)}function _arrayLikeToArray(r,t){(null==t||t>r.length)&&(t=r.length);for(var a=0,e=Array(t);a<t;a++)e[a]=r[a];return e}document.addEventListener("DOMContentLoaded",function(){var r=document.querySelectorAll("[data-ui-animation]"),t=new IntersectionObserver(function(r,n){r.forEach(function(r){var t,a,e;r.isIntersecting&&(a=r.target.getAttribute("data-ui-animation"),e=r.target.getAttribute("data-ui-duration"),(t=r.target.classList).add.apply(t,_toConsumableArray(a.split(" "))),r.target.classList.remove("uicore-animate-hide"),"fast"===e?r.target.classList.add("animated","animated-fast"):"slow"===e?r.target.classList.add("animated","animated-slow"):r.target.classList.add("animated"),n.unobserve(r.target))})},{rootMargin:"50px",threshold:0});r.forEach(function(r){t.observe(r)})});const observerOffset=window.innerHeight*0.1;var topbarItems=document.querySelectorAll(".uicore-top-bar .uicore-animate");if(topbarItems.length){[].forEach.call(topbarItems,function(div,index){setTimeout(function(){div.style.animationPlayState="running"},(200*(index+1))-200)})}
var logo=document.querySelector(".uicore-header-wrapper .uicore-branding");var mq=window.matchMedia("(max-width: 1025px)");if(mq.matches){var ham=document.querySelectorAll("nav .uicore-ham");menuItems=logo?[logo,...ham]:[...ham]}else{var menuItems=document.querySelectorAll(".uicore-header-wrapper ul.uicore-menu > .menu-item > a");var extraItems=document.querySelectorAll("#wrapper-navbar .uicore-extra > *");menuItems=logo?[logo,...menuItems,...extraItems]:[...menuItems,...extraItems];var ham=document.querySelectorAll("#wrapper-navbar nav .uicore-ham:not(.uicore-drawer-toggle)")[0];if(ham){ham.style.animationPlayState="running"}}
if(menuItems.length){[].forEach.call(menuItems,function(div,index){setTimeout(function(){div.style.animationPlayState="running"},(200*(index+1))-200)})}
var pagetitle=document.querySelectorAll(".uicore-page-title");if(pagetitle.length){pagetitle[0].classList.add("ui-a-in-view")}
var blogItems=document.querySelectorAll(".uicore-blog-animation .uicore-animate");var blogTitleItems=document.querySelectorAll(".ui-simple-creative .uicore-animate");blogItems=[...blogItems,...blogTitleItems];if(blogItems.length){const blog_observer=new IntersectionObserver(function(entries){entries.forEach(function(entry){if(entry.isIntersecting){entry.target.style.animationPlayState="running"}})},{root:null,rootMargin:`0px 0px -${observerOffset}px 0px`,threshold:0});blogItems.forEach(function(div){blog_observer.observe(div)})}
if(window.innerWidth>=1025){document.querySelectorAll('.uicore-menu li').forEach(function(li){li.addEventListener('mouseenter',function(e){var animClass='ui-anim-flip';if(!li.classList.contains(animClass)){var a=li.querySelector('a');if(!a)return;var btnContent=a.querySelector('.ui-menu-item-wrapper');if(!btnContent)return;var clone=btnContent.cloneNode(!0);btnContent.parentNode.insertBefore(clone,btnContent.nextSibling);var wrappers=a.querySelectorAll('.ui-menu-item-wrapper');var wrapDiv=document.createElement('div');wrapDiv.className='ui-flip-anim-wrapp';wrappers.forEach(function(w){wrapDiv.appendChild(w)});a.appendChild(wrapDiv);setTimeout(function(){li.classList.add(animClass)},10)}})})}
document.addEventListener('DOMContentLoaded',function(){var menuItem=document.querySelector('.menu-item-has-megamenu');if(menuItem){menuItem.addEventListener('mouseenter',function(){setTimeout(function(){var tabTitle=document.getElementById('ui-e-tab-title-2642');if(tabTitle){tabTitle.click()}},0)})}})