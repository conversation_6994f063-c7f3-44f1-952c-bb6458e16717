{"name": "<PERSON><PERSON><PERSON>", "description": "", "url": "https://www.puffmivape.com", "home": "https://www.puffmivape.com", "gmt_offset": "0", "timezone_string": "", "page_for_posts": 1639, "page_on_front": 44, "show_on_front": "page", "namespaces": ["oembed/1.0", "age-gate/v3", "flying-press", "uicore/v1", "wordfence/v1", "bdt/v1", "elementor/v1", "wp-mail-smtp/v1", "seopress/v1", "wp-smush/v1", "elementor/v1/documents", "elementor-ai/v1", "elementor-pro/v1", "uianim/v1", "uielem/v1", "wp/v2", "wp-site-health/v1", "wp-block-editor/v1"], "authentication": [], "routes": {"/": {"namespace": "", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/"}]}}, "/batch/v1": {"namespace": "", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"validation": {"type": "string", "enum": ["require-all-validate", "normal"], "default": "normal", "required": false}, "requests": {"type": "array", "maxItems": 25, "items": {"type": "object", "properties": {"method": {"type": "string", "enum": ["POST", "PUT", "PATCH", "DELETE"], "default": "POST"}, "path": {"type": "string", "required": true}, "body": {"type": "object", "properties": [], "additionalProperties": true}, "headers": {"type": "object", "properties": [], "additionalProperties": {"type": ["string", "array"], "items": {"type": "string"}}}}}, "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/batch/v1"}]}}, "/oembed/1.0": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "oembed/1.0", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/oembed/1.0"}]}}, "/oembed/1.0/embed": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "The URL of the resource for which to fetch oEmbed data.", "type": "string", "format": "uri", "required": true}, "format": {"default": "json", "required": false}, "maxwidth": {"default": 600, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/oembed/1.0/embed"}]}}, "/oembed/1.0/proxy": {"namespace": "oembed/1.0", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "The URL of the resource for which to fetch oEmbed data.", "type": "string", "format": "uri", "required": true}, "format": {"description": "The oEmbed format to use.", "type": "string", "default": "json", "enum": ["json", "xml"], "required": false}, "maxwidth": {"description": "The maximum width of the embed frame in pixels.", "type": "integer", "default": 600, "required": false}, "maxheight": {"description": "The maximum height of the embed frame in pixels.", "type": "integer", "required": false}, "discover": {"description": "Whether to perform an oEmbed discovery request for unsanctioned providers.", "type": "boolean", "default": true, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/oembed/1.0/proxy"}]}}, "/age-gate/v3": {"namespace": "age-gate/v3", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "age-gate/v3", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/age-gate/v3"}]}}, "/age-gate/v3/check": {"namespace": "age-gate/v3", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/age-gate/v3/check"}]}}, "/age-gate/v3/admin/terms": {"namespace": "age-gate/v3", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/age-gate/v3/admin/terms"}]}}, "/age-gate/v3/admin/terms/selected": {"namespace": "age-gate/v3", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/age-gate/v3/admin/terms/selected"}]}}, "/age-gate/v3/admin/terms/select-all": {"namespace": "age-gate/v3", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/age-gate/v3/admin/terms/select-all"}]}}, "/flying-press": {"namespace": "flying-press", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "flying-press", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/flying-press"}]}}, "/flying-press/validate/(?P<key>[a-zA-Z0-9]+)": {"namespace": "flying-press", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/uicore/v1": {"namespace": "uicore/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "uicore/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uicore/v1"}]}}, "/wordfence/v1": {"namespace": "wordfence/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wordfence/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1"}]}}, "/wordfence/v1/authenticate": {"namespace": "wordfence/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/authenticate"}]}}, "/wordfence/v1/authenticate-premium": {"namespace": "wordfence/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/authenticate-premium"}]}}, "/wordfence/v1/config": {"namespace": "wordfence/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/config"}]}}, "/wordfence/v1/disconnect": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/disconnect"}]}}, "/wordfence/v1/premium-connect": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/premium-connect"}]}}, "/wordfence/v1/scan/issues": {"namespace": "wordfence/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/scan/issues"}]}}, "/wordfence/v1/scan": {"namespace": "wordfence/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": []}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/scan"}]}}, "/wordfence/v1/scan/issue": {"namespace": "wordfence/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wordfence/v1/scan/issue"}]}}, "/bdt/v1": {"namespace": "bdt/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "bdt/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/bdt/v1"}]}}, "/bdt/v1/get-singular-list": {"namespace": "bdt/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/bdt/v1/get-singular-list"}]}}, "/elementor/v1": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1"}]}}, "/elementor/v1/site-editor": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-editor"}]}}, "/elementor/v1/site-editor/templates": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-editor/templates"}]}}, "/elementor/v1/site-editor/templates/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}]}, "/elementor/v1/site-editor/conditions-config": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-editor/conditions-config"}]}}, "/elementor/v1/site-editor/templates-conditions/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": false}}}]}, "/elementor/v1/site-editor/templates-conditions-conflicts": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-editor/templates-conditions-conflicts"}]}}, "/wp-mail-smtp/v1": {"namespace": "wp-mail-smtp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-mail-smtp/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-mail-smtp/v1"}]}}, "/wp-mail-smtp/v1/webhooks/sendinblue": {"namespace": "wp-mail-smtp/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-mail-smtp/v1/webhooks/sendinblue"}]}}, "/wp-mail-smtp/v1/e/(?P<data>.+)": {"namespace": "wp-mail-smtp/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}]}, "/seopress/v1": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "seopress/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1"}]}}, "/seopress/v1/posts/(?P<id>\\d+)/content-analysis": {"namespace": "seopress/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["POST"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/count-target-keywords-use": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/by-url": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/posts/by-url"}]}}, "/seopress/v1/terms/(?P<id>\\d+)": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/redirection-settings": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/meta-robot-settings": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/social-settings": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/options/advanced-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/advanced-settings"}]}}, "/seopress/v1/options/analytics-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/analytics-settings"}]}}, "/seopress/v1/options/bot-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/bot-settings"}]}}, "/seopress/v1/options/dashboard-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/dashboard-settings"}]}}, "/seopress/v1/options/indexing-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/indexing-settings"}]}}, "/seopress/v1/options/license-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/license-settings"}]}}, "/seopress/v1/options/pro-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/pro-settings"}]}}, "/seopress/v1/roles": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/roles"}]}}, "/seopress/v1/options/sitemaps-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/sitemaps-settings"}]}}, "/seopress/v1/options/social-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/social-settings"}]}}, "/seopress/v1/options/titles-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/titles-settings"}]}}, "/seopress/v1/options/woocommerce-settings": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/options/woocommerce-settings"}]}}, "/seopress/v1/posts/(?P<id>\\d+)/page-preview": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/preview-title-description-metas": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/search-url": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/search-url"}]}}, "/seopress/v1/posts/(?P<id>\\d+)/target-keywords": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/title-description-metas": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/wp-smush/v1": {"namespace": "wp-smush/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-smush/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-smush/v1"}]}}, "/wp-smush/v1/preset_configs": {"namespace": "wp-smush/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-smush/v1/preset_configs"}]}}, "/seopress/v1/ga4": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/ga4"}]}}, "/seopress/v1/matomo": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/matomo"}]}}, "/seopress/v1/seo-issues/(?P<id>\\d+)": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/seo-issues": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}, "ignore": {"required": false}, "type": {"required": false}, "priority": {"required": false}, "name": {"required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/seo-issues"}]}}, "/seopress/v1/posts/(?P<id>\\d+)/inspect": {"namespace": "seopress/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["POST"], "args": []}]}, "/seopress/v1/posts/(?P<id>\\d+)/generate-metas-by-ai": {"namespace": "seopress/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/significant-keywords": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/google-news-settings": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/posts/(?P<id>\\d+)/video-sitemap": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/seopress/v1/page-speed": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"device": {"required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/page-speed"}]}}, "/seopress/v1/redirections": {"namespace": "seopress/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}, "enabled": {"required": false}, "type": {"required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/seopress/v1/redirections"}]}}, "/seopress/v1/posts/(?P<id>\\d+)/schemas-manual": {"namespace": "seopress/v1", "methods": ["GET", "PUT"], "endpoints": [{"methods": ["GET"], "args": {"id": {"required": false}}}, {"methods": ["PUT"], "args": {"id": {"required": false}}}]}, "/elementor/v1/documents": {"namespace": "elementor/v1/documents", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor/v1/documents", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/documents"}]}}, "/elementor/v1/documents/(?P<id>\\d+)/media/import": {"namespace": "elementor/v1/documents", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"required": true}}}]}, "/elementor/v1/cache": {"namespace": "elementor/v1", "methods": ["DELETE"], "endpoints": [{"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/cache"}]}}, "/elementor/v1/globals": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/globals"}]}}, "/elementor/v1/globals/colors": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/globals/colors"}]}}, "/elementor/v1/globals/colors/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/globals/typography": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/globals/typography"}]}}, "/elementor/v1/globals/typography/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/settings/(?P<key>[\\w_-]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}]}, "/elementor/v1/post": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"excluded_post_types": {"description": "Post type to exclude", "type": ["array", "string"], "default": ["e-floating-buttons", "e-landing-page", "elementor_library", "attachment"], "required": false}, "term": {"description": "Posts to search", "type": "string", "default": "", "required": false}, "post_keys_conversion_map": {"description": "Specify keys to extract and convert, i.e. [\"key_1\" => \"new_key_1\"].", "type": ["array", "string"], "default": [], "required": false}, "max_count": {"description": "Max count of returned items", "type": "number", "default": 100, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/post"}]}}, "/elementor-ai/v1": {"namespace": "elementor-ai/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor-ai/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-ai/v1"}]}}, "/elementor-ai/v1/permissions": {"namespace": "elementor-ai/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-ai/v1/permissions"}]}}, "/elementor/v1/favorites": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/favorites"}]}}, "/elementor/v1/favorites/(?P<id>[\\w]+)": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Type of favorites.", "type": "string", "required": true}, "favorite": {"description": "The favorite slug to create.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Type of favorites.", "type": "string", "required": true}, "favorite": {"description": "The favorite slug to delete.", "type": "string", "required": true}}}]}, "/elementor/v1/kit-elements-defaults": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/kit-elements-defaults"}]}}, "/elementor/v1/kit-elements-defaults/(?P<type>[\\w\\-\\_]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"type": {"type": "string", "description": "The type of the element.", "required": true}, "settings": {"description": "All the default values for the requested type", "type": "object", "required": true}}}, {"methods": ["DELETE"], "args": {"type": {"type": "string", "description": "The type of the element.", "required": true}}}]}, "/elementor/v1/site-navigation/recent-posts": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"posts_per_page": {"description": "Number of posts to return", "type": "integer", "required": true}, "post_type": {"description": "Post types to retrieve", "type": "array", "default": ["page", "post", "elementor_library"], "required": false}, "post__not_in": {"description": "Post id`s to exclude", "type": "array", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-navigation/recent-posts"}]}}, "/elementor/v1/site-navigation/add-new-post": {"namespace": "elementor/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_type": {"description": "Post type to create", "type": "string", "default": "post", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/site-navigation/add-new-post"}]}}, "/elementor/v1/checklist": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/checklist"}]}}, "/elementor/v1/checklist/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/checklist/steps": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/checklist/steps"}]}}, "/elementor/v1/checklist/steps/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/checklist/steps/(?P<id>[\\w\\-\\_]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"type": "string", "description": "The step id.", "required": true}}}]}, "/elementor/v1/checklist/user-progress": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/checklist/user-progress"}]}}, "/elementor/v1/template-library/templates": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST"], "args": {"title": {"type": "string", "description": "The title of the document", "required": false}, "type": {"description": "The document type.", "type": "string", "enum": ["header", "footer", "popup", "single", "page", "section", "container", "floating-buttons", "single-post", "single-page", "archive", "search-results", "error-404", "loop-item"], "required": true}, "content": {"description": "Elementor data object", "type": "object", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/template-library/templates"}]}}, "/elementor/v1/global-widget/templates": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/global-widget/templates"}]}}, "/elementor-pro/v1": {"namespace": "elementor-pro/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "elementor-pro/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-pro/v1"}]}}, "/elementor-pro/v1/posts-widget": {"namespace": "elementor-pro/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-pro/v1/posts-widget"}]}}, "/elementor/v1/form-submissions": {"namespace": "elementor/v1", "methods": ["GET", "DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 50, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "order_by": {"description": "Sort collection by object attribute.", "type": "string", "default": "created_at", "enum": ["created_at", "id", "main_meta_id"], "required": false}, "status": {"description": "Limit result set to submissions assigned one or more statuses.", "type": "string", "default": "all", "enum": ["all", "unread", "read", "trash"], "additionalProperties": {"context": "filter"}, "required": false}, "form": {"description": "Limit result set to submissions assigned to specific forms. The form id should follow this pattern {post_id}_{element_id} e.g: 10_476d0ce", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "referer": {"description": "Limit result set to submissions assigned to specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "after": {"description": "Limit response to submissions sent after a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "before": {"description": "Limit response to submissions sent before a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}}}, {"methods": ["DELETE"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}, "force": {"description": "Delete the object permanently.", "type": "boolean", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}, "is_read": {"description": "mark whether the submission was read or not", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/form-submissions"}]}}, "/elementor/v1/form-submissions/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET", "DELETE", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}, "force": {"description": "Delete the object permanently.", "type": "boolean", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}, "is_read": {"description": "mark whether the submission was read or not", "type": "boolean", "required": false}, "values": {"description": "Form field values, receive an array, the key should be the form field id and the value should be the value.", "type": "object", "required": false}}}]}, "/elementor/v1/form-submissions/restore/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the object.", "type": "string", "required": true}}}]}, "/elementor/v1/form-submissions/restore": {"namespace": "elementor/v1", "methods": ["POST", "PUT", "PATCH"], "endpoints": [{"methods": ["POST", "PUT", "PATCH"], "args": {"ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/form-submissions/restore"}]}}, "/elementor/v1/form-submissions/export": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 10000, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "order_by": {"description": "Sort collection by object attribute.", "type": "string", "default": "created_at", "enum": ["created_at", "id", "main_meta_id"], "required": false}, "status": {"description": "Limit result set to submissions assigned one or more statuses.", "type": "string", "default": "all", "enum": ["all", "unread", "read", "trash"], "additionalProperties": {"context": "filter"}, "required": false}, "form": {"description": "Limit result set to submissions assigned to specific forms. The form id should follow this pattern {post_id}_{element_id} e.g: 10_476d0ce", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "referer": {"description": "Limit result set to submissions assigned to specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "after": {"description": "Limit response to submissions sent after a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "before": {"description": "Limit response to submissions sent before a given ISO8601 compliant date.", "type": "string", "format": "date", "additionalProperties": {"context": "filter"}, "required": false}, "ids": {"description": "Unique identifiers for the objects.", "type": "array", "items": {"type": "integer"}, "additionalProperties": {"context": "filter"}, "required": false}, "format": {"description": "The format of the export (for now only csv).", "enum": ["csv"], "default": "csv", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/form-submissions/export"}]}}, "/elementor/v1/form-submissions/referer": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made, determines fields present in response. (only \"options\" available for now)", "type": "string", "enum": ["options"], "default": "options", "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}, "value": {"description": "Limit results specific referer.", "type": "string", "additionalProperties": {"context": "filter"}, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/form-submissions/referer"}]}}, "/elementor/v1/forms": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made, determines fields present in response. (only \"options\" available for now)", "type": "string", "enum": ["options"], "default": "options", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/forms"}]}}, "/elementor-pro/v1/get-post-type-taxonomies": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_type": {"description": "The post type for which to fetch the list of taxonomies.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-pro/v1/get-post-type-taxonomies"}]}}, "/elementor-pro/v1/refresh-loop": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_id": {"description": "The post ID of the page containing the loop.", "type": "integer", "required": true}, "widget_id": {"description": "The ID of the loop widget.", "type": "string", "required": true}, "widget_filters": {"description": "The filters for the loop widget.", "type": "object", "required": true}, "widget_model": {"description": "The model of the loop widget. In Editor mode only.", "type": "object", "required": false}, "pagination_base_url": {"required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-pro/v1/refresh-loop"}]}}, "/elementor-pro/v1/refresh-search": {"namespace": "elementor-pro/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"post_id": {"description": "The post ID of the page containing the loop.", "type": "integer", "required": true}, "widget_id": {"description": "The ID of the loop widget.", "type": "string", "required": true}, "widget_model": {"description": "The model of the loop widget. In Editor mode only.", "type": "object", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor-pro/v1/refresh-search"}]}}, "/elementor/v1/notes": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "status": {"type": "string", "description": "The note status (e.g. \"publish\", \"draft\").", "enum": ["publish", "draft"], "default": "publish", "required": false}, "is_resolved": {"type": "boolean", "description": "Whether the note is resolved or not.", "required": false}, "parent_id": {"type": "integer", "description": "The note's parent id (use 0 for top-level).", "required": false}, "post_id": {"type": "integer", "description": "The ID of the post that the note is attached to.", "required": false}, "only_unread": {"type": "boolean", "description": "Show only unread notes (represents an unread thread if one of its replies is unread).", "required": false}, "only_relevant": {"type": "boolean", "description": "Show only notes that are relevant to the current user.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "last_activity_at", "enum": ["last_activity_at", "created_at"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "desc", "enum": ["asc", "desc"], "required": false}}}, {"methods": ["POST"], "args": {"post_id": {"type": "integer", "description": "The id of the post where the note was created at (can be template, post, page, etc.).", "required": true}, "element_id": {"type": "string", "description": "Each note must be attached to an elementor element.", "required": true}, "content": {"type": "string", "description": "The content of the note.", "required": true}, "position": {"type": "object", "properties": {"x": {"required": true, "type": "number"}, "y": {"required": true, "type": "number"}}, "description": "The position of the note.", "required": true}, "mentioned_usernames": {"type": "array", "description": "List of user names that have been mentioned in the note's content.", "default": [], "items": {"type": "string", "sanitize_callback": {}}, "required": false}, "route_post_id": {"description": "The ID of the post that's associated with the route (doesn't always exist, e.g: home page, archive)", "required": false}, "route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "route_title": {"type": "string", "description": "The title of the route where the note was created at.", "required": false}, "parent_id": {"type": "integer", "description": "If the new note is a reply to another note, the parent_id should be the thread's id.", "default": 0, "required": false}, "is_public": {"type": "boolean", "description": "Should this note be visible for everyone or just for its author.", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/notes"}]}}, "/elementor/v1/notes/read-status": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"ids": {"type": "array", "description": "The id's of the notes.", "items": {"type": "integer"}, "required": true}}}, {"methods": ["DELETE"], "args": {"ids": {"type": "array", "description": "The id's of the notes.", "items": {"type": "integer"}, "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/notes/read-status"}]}}, "/elementor/v1/notes/summary": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"route_url": {"type": "string", "description": "The URL of the route where the note was created at.", "required": false}, "status": {"type": "string", "description": "The note status (e.g. \"publish\", \"draft\").", "enum": ["publish", "draft"], "default": "publish", "required": false}, "is_resolved": {"type": "boolean", "description": "Whether the note is resolved or not.", "required": false}, "parent_id": {"type": "integer", "description": "The note's parent id (use 0 for top-level).", "required": false}, "post_id": {"type": "integer", "description": "The ID of the post that the note is attached to.", "required": false}, "only_unread": {"type": "boolean", "description": "Show only unread notes (represents an unread thread if one of its replies is unread).", "required": false}, "only_relevant": {"type": "boolean", "description": "Show only notes that are relevant to the current user.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "last_activity_at", "enum": ["last_activity_at", "created_at"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "desc", "enum": ["asc", "desc"], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/notes/summary"}]}}, "/elementor/v1/notes/users": {"namespace": "elementor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"limit": {"type": "integer", "description": "Limit the results.", "required": false}, "order_by": {"type": "string", "description": "A column to order the results by.", "default": "display_name", "enum": ["user_nicename", "display_name", "user_registered"], "required": false}, "order": {"type": "string", "description": "Results order direction.", "default": "asc", "enum": ["asc", "desc"], "required": false}, "search": {"type": "string", "description": "Filter users by a search term.", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/notes/users"}]}}, "/elementor/v1/notes/(?P<id>[\\d]+)": {"namespace": "elementor/v1", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"type": "integer", "description": "Note ID to find.", "required": true}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"type": "integer", "description": "The id the note.", "required": true}, "content": {"type": "string", "description": "The content of the note.", "required": false}, "mentioned_usernames": {"type": "array", "description": "List of user names that have been mentioned in the note's content.", "items": {"type": "string", "sanitize_callback": {}}, "required": false}, "status": {"type": "string", "description": "Note status can be draft or publish.", "enum": ["publish", "draft"], "required": false}, "is_public": {"type": "boolean", "description": "Should this note be visible for everyone or just for its author.", "required": false}, "is_resolved": {"type": "boolean", "description": "Is this note resolved and should be hidden.", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"type": "integer", "description": "The id of the note.", "required": true}, "force": {"type": "boolean", "description": "Determine if it should be deleted permanently or change the status to trash.", "default": false, "required": false}}}]}, "/uianim/v1": {"namespace": "uianim/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "uianim/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uianim/v1"}]}}, "/uielem/v1": {"namespace": "uielem/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "uielem/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uielem/v1"}]}}, "/uielem/v1/load_more": {"namespace": "uielem/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"widget_id": {"required": true}, "widget_type": {"required": true}, "page": {"default": 1, "required": false}, "type": {"default": "", "required": false}, "term": {"default": "", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uielem/v1/load_more"}]}}, "/uielem/v1/submit_actions": {"namespace": "uielem/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uielem/v1/submit_actions"}]}}, "/uielem/v1/prepare_template": {"namespace": "uielem/v1", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uielem/v1/prepare_template"}]}}, "/uielem/v1/check_connection": {"namespace": "uielem/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/uielem/v1/check_connection"}]}}, "/elementor/v1/library/connect": {"namespace": "elementor/v1", "methods": ["POST", "DELETE"], "endpoints": [{"methods": ["POST"], "args": {"token": {"type": "string", "description": "Connect CLI token", "required": true}}}, {"methods": ["DELETE"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/library/connect"}]}}, "/elementor/v1/send-event": {"namespace": "elementor/v1", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"event_data": {"description": "All the recorded event data in JSON format", "type": "object", "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/elementor/v1/send-event"}]}}, "/wp/v2": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp/v2", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2"}]}}, "/wp/v2/posts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Limit result set based on relationship between multiple taxonomies.", "type": "string", "enum": ["AND", "OR"], "required": false}, "categories": {"description": "Limit result set to items with specific terms assigned in the categories taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "Whether to include child terms in the terms limiting the result set.", "type": "boolean", "default": false}, "operator": {"description": "Whether items must be assigned all or any of the specified terms.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "categories_exclude": {"description": "Limit result set to items except those with specific terms assigned in the categories taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}, "include_children": {"description": "Whether to include child terms in the terms limiting the result set.", "type": "boolean", "default": false}}, "additionalProperties": false}], "required": false}, "tags": {"description": "Limit result set to items with specific terms assigned in the tags taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Whether items must be assigned all or any of the specified terms.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "tags_exclude": {"description": "Limit result set to items except those with specific terms assigned in the tags taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "sticky": {"description": "Limit result set to items that are sticky.", "type": "boolean", "required": false}, "ignore_sticky": {"description": "Whether to ignore sticky posts or not.", "type": "boolean", "default": true, "required": false}, "format": {"description": "Limit result set to items assigned one or more given formats.", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "Whether or not the post should be treated as sticky.", "type": "boolean", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "categories": {"description": "The terms assigned to the post in the category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "The terms assigned to the post in the post_tag taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/posts"}]}}, "/wp/v2/posts/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Override the default excerpt length.", "type": "integer", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "Whether or not the post should be treated as sticky.", "type": "boolean", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "categories": {"description": "The terms assigned to the post in the category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "The terms assigned to the post in the post_tag taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/posts/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "sticky": {"description": "Whether or not the post should be treated as sticky.", "type": "boolean", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "categories": {"description": "The terms assigned to the post in the category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}, "tags": {"description": "The terms assigned to the post in the post_tag taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/posts/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/pages": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "Limit result set to posts with a specific menu_order value.", "type": "integer", "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "parent": {"description": "Limit result set to items with particular parent IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "Limit result set to all items except those of a particular parent ID.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "parent": {"description": "The ID for the parent of the post.", "type": "integer", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/pages"}]}}, "/wp/v2/pages/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Override the default excerpt length.", "type": "integer", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "parent": {"description": "The ID for the parent of the post.", "type": "integer", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/pages/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}]}, "/wp/v2/pages/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/media": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "parent": {"description": "Limit result set to items with particular parent IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "parent_exclude": {"description": "Limit result set to all items except those of a particular parent ID.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "inherit", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["inherit", "private", "trash"], "type": "string"}, "required": false}, "media_type": {"default": null, "description": "Limit result set to attachments of a particular media type.", "type": "string", "enum": ["image", "video", "text", "application", "audio"], "required": false}, "mime_type": {"default": null, "description": "Limit result set to attachments of a particular MIME type.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "smush": {"description": "Smush data.", "type": "string", "required": false}, "alt_text": {"description": "Alternative text to display when attachment is not displayed.", "type": "string", "required": false}, "caption": {"description": "The attachment caption.", "type": "object", "properties": {"raw": {"description": "Caption for the attachment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML caption for the attachment, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "The attachment description.", "type": "object", "properties": {"raw": {"description": "Description for the attachment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML description for the attachment, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "The ID for the associated post of the attachment.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/media"}]}}, "/wp/v2/media/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "smush": {"description": "Smush data.", "type": "string", "required": false}, "alt_text": {"description": "Alternative text to display when attachment is not displayed.", "type": "string", "required": false}, "caption": {"description": "The attachment caption.", "type": "object", "properties": {"raw": {"description": "Caption for the attachment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML caption for the attachment, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "The attachment description.", "type": "object", "properties": {"raw": {"description": "Description for the attachment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML description for the attachment, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}}, "required": false}, "post": {"description": "The ID for the associated post of the attachment.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/post-process": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "Unique identifier for the attachment.", "type": "integer", "required": false}, "action": {"type": "string", "enum": ["create-image-subsizes"], "required": true}}}]}, "/wp/v2/media/(?P<id>[\\d]+)/edit": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"src": {"description": "URL to the edited image file.", "type": "string", "format": "uri", "required": true}, "modifiers": {"description": "Array of image edits.", "type": "array", "minItems": 1, "items": {"description": "Image edit.", "type": "object", "required": ["type", "args"], "oneOf": [{"title": "Rotation", "properties": {"type": {"description": "Rotation type.", "type": "string", "enum": ["rotate"]}, "args": {"description": "Rotation arguments.", "type": "object", "required": ["angle"], "properties": {"angle": {"description": "Angle to rotate clockwise in degrees.", "type": "number"}}}}}, {"title": "Crop", "properties": {"type": {"description": "Crop type.", "type": "string", "enum": ["crop"]}, "args": {"description": "Crop arguments.", "type": "object", "required": ["left", "top", "width", "height"], "properties": {"left": {"description": "Horizontal position from the left to begin the crop as a percentage of the image width.", "type": "number"}, "top": {"description": "Vertical position from the top to begin the crop as a percentage of the image height.", "type": "number"}, "width": {"description": "Width of the crop as a percentage of the image width.", "type": "number"}, "height": {"description": "Height of the crop as a percentage of the image height.", "type": "number"}}}}}]}, "required": false}, "rotation": {"description": "The amount to rotate the image clockwise in degrees. DEPRECATED: Use `modifiers` instead.", "type": "integer", "minimum": 0, "exclusiveMinimum": true, "maximum": 360, "exclusiveMaximum": true, "required": false}, "x": {"description": "As a percentage of the image, the x position to start the crop from. DEPRECATED: Use `modifiers` instead.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "y": {"description": "As a percentage of the image, the y position to start the crop from. DEPRECATED: Use `modifiers` instead.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "width": {"description": "As a percentage of the image, the width to crop the image to. DEPRECATED: Use `modifiers` instead.", "type": "number", "minimum": 0, "maximum": 100, "required": false}, "height": {"description": "As a percentage of the image, the height to crop the image to. DEPRECATED: Use `modifiers` instead.", "type": "number", "minimum": 0, "maximum": 100, "required": false}}}]}, "/wp/v2/menu-items": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "menu_order", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Limit result set based on relationship between multiple taxonomies.", "type": "string", "enum": ["AND", "OR"], "required": false}, "menus": {"description": "Limit result set to items with specific terms assigned in the menus taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Whether items must be assigned all or any of the specified terms.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "menus_exclude": {"description": "Limit result set to items except those with specific terms assigned in the menus taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}, "menu_order": {"description": "Limit result set to posts with a specific menu_order value.", "type": "integer", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"title": {"description": "The title for the object.", "type": ["string", "object"], "properties": {"raw": {"description": "Title for the object, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the object, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"default": "custom", "description": "The family of objects originally represented, such as \"post_type\" or \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"default": "publish", "description": "A named status for the object.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"default": 0, "description": "The ID for the parent of the object.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "Text for the title attribute of the link element for this menu item.", "type": "string", "required": false}, "classes": {"description": "Class names for the link element of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "The description of this menu item.", "type": "string", "required": false}, "menu_order": {"default": 1, "description": "The DB ID of the nav_menu_item that is this item's menu parent, if any, otherwise 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "The type of object originally represented, such as \"category\", \"post\", or \"attachment\".", "type": "string", "required": false}, "object_id": {"default": 0, "description": "The database ID of the original object this menu item represents, for example the ID for posts or the term_id for categories.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "The target attribute of the link element for this menu item.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "The URL to which this menu item points.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "The XFN relationship expressed in the link of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "The terms assigned to the object in the nav_menu taxonomy.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/menu-items"}]}}, "/wp/v2/menu-items/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "title": {"description": "The title for the object.", "type": ["string", "object"], "properties": {"raw": {"description": "Title for the object, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the object, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "The family of objects originally represented, such as \"post_type\" or \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "A named status for the object.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "parent": {"description": "The ID for the parent of the object.", "type": "integer", "minimum": 0, "required": false}, "attr_title": {"description": "Text for the title attribute of the link element for this menu item.", "type": "string", "required": false}, "classes": {"description": "Class names for the link element of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "The description of this menu item.", "type": "string", "required": false}, "menu_order": {"description": "The DB ID of the nav_menu_item that is this item's menu parent, if any, otherwise 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "The type of object originally represented, such as \"category\", \"post\", or \"attachment\".", "type": "string", "required": false}, "object_id": {"description": "The database ID of the original object this menu item represents, for example the ID for posts or the term_id for categories.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "The target attribute of the link element for this menu item.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "The URL to which this menu item points.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "The XFN relationship expressed in the link of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "The terms assigned to the object in the nav_menu taxonomy.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/menu-items/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the object.", "type": "integer", "minimum": 0, "required": false}, "title": {"description": "The title for the object.", "type": ["string", "object"], "properties": {"raw": {"description": "Title for the object, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the object, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "type": {"description": "The family of objects originally represented, such as \"post_type\" or \"taxonomy\".", "type": "string", "enum": ["taxonomy", "post_type", "post_type_archive", "custom"], "required": false}, "status": {"description": "A named status for the object.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "attr_title": {"description": "Text for the title attribute of the link element for this menu item.", "type": "string", "required": false}, "classes": {"description": "Class names for the link element of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "description": {"description": "The description of this menu item.", "type": "string", "required": false}, "menu_order": {"description": "The DB ID of the nav_menu_item that is this item's menu parent, if any, otherwise 0.", "type": "integer", "minimum": 1, "required": false}, "object": {"description": "The type of object originally represented, such as \"category\", \"post\", or \"attachment\".", "type": "string", "required": false}, "object_id": {"description": "The database ID of the original object this menu item represents, for example the ID for posts or the term_id for categories.", "type": "integer", "minimum": 0, "required": false}, "target": {"description": "The target attribute of the link element for this menu item.", "type": "string", "enum": ["_blank", ""], "required": false}, "url": {"description": "The URL to which this menu item points.", "type": "string", "format": "uri", "required": false}, "xfn": {"description": "The XFN relationship expressed in the link of this menu item.", "type": "array", "items": {"type": "string"}, "required": false}, "menus": {"description": "The terms assigned to the object in the nav_menu taxonomy.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}}}]}, "/wp/v2/menu-items/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/blocks": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "tax_relation": {"description": "Limit result set based on relationship between multiple taxonomies.", "type": "string", "enum": ["AND", "OR"], "required": false}, "wp_pattern_category": {"description": "Limit result set to items with specific terms assigned in the wp_pattern_category taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}, "operator": {"description": "Whether items must be assigned all or any of the specified terms.", "type": "string", "enum": ["AND", "OR"], "default": "OR"}}, "additionalProperties": false}], "required": false}, "wp_pattern_category_exclude": {"description": "Limit result set to items except those with specific terms assigned in the wp_pattern_category taxonomy.", "type": ["object", "array"], "oneOf": [{"title": "Term ID List", "description": "Match terms with the listed IDs.", "type": "array", "items": {"type": "integer"}}, {"title": "Term ID Taxonomy Query", "description": "Perform an advanced term query.", "type": "object", "properties": {"terms": {"description": "Term IDs.", "type": "array", "items": {"type": "integer"}, "default": []}}, "additionalProperties": false}], "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "wp_pattern_category": {"description": "The terms assigned to the post in the wp_pattern_category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/blocks"}]}}, "/wp/v2/blocks/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Override the default excerpt length.", "type": "integer", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "wp_pattern_category": {"description": "The terms assigned to the post in the wp_pattern_category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/blocks/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "wp_pattern_sync_status": {"type": "string", "title": "", "description": "", "default": "", "enum": ["partial", "unsynced"]}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}, "wp_pattern_category": {"description": "The terms assigned to the post in the wp_pattern_category taxonomy.", "type": "array", "items": {"type": "integer"}, "required": false}}}]}, "/wp/v2/blocks/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Description of template.", "type": "string", "required": false}, "status": {"description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}}}]}, "/wp/v2/templates/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/templates": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Limit to the specified post id.", "type": "integer", "required": false}, "area": {"description": "Limit to the specified template part area.", "type": "string", "required": false}, "post_type": {"description": "Post type to get the templates for.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"default": "", "description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "Description of template.", "type": "string", "required": false}, "status": {"default": "publish", "description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/templates"}]}}, "/wp/v2/templates/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "The slug of the template to get the fallback for", "type": "string", "required": true}, "is_custom": {"description": "Indicates if a template is custom or part of the template hierarchy", "type": "boolean", "required": false}, "template_prefix": {"description": "The template prefix for the created template. This is used to extract the main template type, e.g. in `taxonomy-books` extracts the `taxonomy`", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/templates/lookup"}]}}, "/wp/v2/templates/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Description of template.", "type": "string", "required": false}, "status": {"description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Description of template.", "type": "string", "required": false}, "status": {"description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}, "area": {"description": "Where the template part is intended for use (header, footer, etc.)", "type": "string", "required": false}}}]}, "/wp/v2/template-parts/(?P<parent>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The id of a template", "type": "string", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/template-parts": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "wp_id": {"description": "Limit to the specified post id.", "type": "integer", "required": false}, "area": {"description": "Limit to the specified template part area.", "type": "string", "required": false}, "post_type": {"description": "Post type to get the templates for.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": true}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"default": "", "description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"default": "", "description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"default": "", "description": "Description of template.", "type": "string", "required": false}, "status": {"default": "publish", "description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}, "area": {"description": "Where the template part is intended for use (header, footer, etc.)", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/template-parts"}]}}, "/wp/v2/template-parts/lookup": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"slug": {"description": "The slug of the template to get the fallback for", "type": "string", "required": true}, "is_custom": {"description": "Indicates if a template is custom or part of the template hierarchy", "type": "boolean", "required": false}, "template_prefix": {"description": "The template prefix for the created template. This is used to extract the main template type, e.g. in `taxonomy-books` extracts the `taxonomy`", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/template-parts/lookup"}]}}, "/wp/v2/template-parts/(?P<id>([^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)[\\/\\w%-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "slug": {"description": "Unique slug identifying the template.", "type": "string", "minLength": 1, "pattern": "[a-zA-Z0-9_\\%-]+", "required": false}, "theme": {"description": "Theme identifier for the template.", "type": "string", "required": false}, "type": {"description": "Type of template.", "type": "string", "required": false}, "content": {"description": "Content of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Content for the template, as it exists in the database.", "type": "string", "context": ["view", "edit"]}, "block_version": {"description": "Version of the content block format used by the template.", "type": "integer", "context": ["edit"], "readonly": true}}, "required": false}, "title": {"description": "Title of template.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the template, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the template, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "description": {"description": "Description of template.", "type": "string", "required": false}, "status": {"description": "Status of template.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "author": {"description": "The ID for the author of the template.", "type": "integer", "required": false}, "area": {"description": "Where the template part is intended for use (header, footer, etc.)", "type": "string", "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "The id of a template", "type": "string", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}}}]}, "/wp/v2/global-styles/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the global styles revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the global styles revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[\\/\\s%\\w\\.\\(\\)\\[\\]\\@_\\-]+)/variations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "The theme identifier", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"stylesheet": {"description": "The theme identifier", "type": "string", "required": false}}}]}, "/wp/v2/global-styles/(?P<id>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": false}, "args": {"id": {"description": "The id of a template", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": false}, "args": {"styles": {"description": "Global styles.", "type": ["object"], "required": false}, "settings": {"description": "Global settings.", "type": ["object"], "required": false}, "title": {"description": "Title of the global styles variation.", "type": ["object", "string"], "properties": {"raw": {"description": "Title for the global styles variation, as it exists in the database.", "type": "string", "context": ["view", "edit", "embed"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}}}]}, "/wp/v2/navigation": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/navigation"}]}}, "/wp/v2/navigation/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/navigation/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit", "embed"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit", "embed"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}]}, "/wp/v2/navigation/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/font-families": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "args": {"theme_json_version": {"description": "Version of the theme.json schema used for the typography settings.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "font-family declaration in theme.json format, encoded as a string.", "type": "string", "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/font-families"}]}}, "/wp/v2/font-families/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "theme_json_version": {"description": "Version of the theme.json schema used for the typography settings.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_family_settings": {"description": "font-family declaration in theme.json format, encoded as a string.", "type": "string", "required": true}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "The ID for the parent font family of the font face.", "type": "integer", "required": true}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "id", "enum": ["id", "include"], "required": false}}}, {"methods": ["POST"], "args": {"font_family_id": {"description": "The ID for the parent font family of the font face.", "type": "integer", "required": true}, "theme_json_version": {"description": "Version of the theme.json schema used for the typography settings.", "type": "integer", "default": 3, "minimum": 2, "maximum": 3, "required": false}, "font_face_settings": {"description": "font-face declaration in theme.json format, encoded as a string.", "type": "string", "required": true}}}]}, "/wp/v2/font-families/(?P<font_family_id>[\\d]+)/font-faces/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"font_family_id": {"description": "The ID for the parent font family of the font face.", "type": "integer", "required": true}, "id": {"description": "Unique identifier for the font face.", "type": "integer", "required": true}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"font_family_id": {"description": "The ID for the parent font family of the font face.", "type": "integer", "required": true}, "id": {"description": "Unique identifier for the font face.", "type": "integer", "required": true}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/e-floating-buttons": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "menu_order": {"description": "Limit result set to posts with a specific menu_order value.", "type": "integer", "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "menu_order"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}, "format": {"description": "Limit result set to items assigned one or more given formats.", "type": "array", "uniqueItems": true, "items": {"enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/e-floating-buttons"}]}}, "/wp/v2/e-floating-buttons/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "excerpt_length": {"description": "Override the default excerpt length.", "type": "integer", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/e-floating-buttons/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/e-floating-buttons/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/e-floating-buttons/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "excerpt": {"description": "The excerpt for the post.", "type": "object", "properties": {"raw": {"description": "Excerpt for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML excerpt for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}, "protected": {"description": "Whether the excerpt is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "comment_status": {"description": "Whether or not comments are open on the post.", "type": "string", "enum": ["open", "closed"], "required": false}, "ping_status": {"description": "Whether or not the post can be pinged.", "type": "string", "enum": ["open", "closed"], "required": false}, "menu_order": {"description": "The order of the post in relation to other posts.", "type": "integer", "required": false}, "format": {"description": "The format for the post.", "type": "string", "enum": ["standard", "aside", "chat", "gallery", "link", "image", "quote", "status", "video", "audio"], "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "footnotes": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}]}, "/wp/v2/e-floating-buttons/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/elementor_library": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/elementor_library"}]}}, "/wp/v2/elementor_library/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"_seopress_robots_primary_cat": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_title": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_titles_desc": {"type": "string", "title": "", "description": "", "default": ""}, "_seopress_robots_index": {"type": "string", "title": "", "description": "", "default": ""}, "_searchwp_excluded": {"type": "string", "title": "", "description": "", "default": ""}, "_elementor_edit_mode": {"type": "string", "title": "Elementor edit mode", "description": "Elementor edit mode, `builder` is required for Elementor editing", "default": "", "enum": ["", "builder"], "context": ["edit"]}, "_elementor_template_type": {"type": "string", "title": "Elementor template type", "description": "Elementor document type", "default": "", "enum": ["post", "wp-post", "wp-page", "header", "footer", "popup", "megamenu", "pagetitle", "single", "archieve", "kit", "not-supported", "page", "section", "container", "floating-buttons", "cloud-template-preview", "widget", "single-post", "single-page", "archive", "search-results", "error-404", "code_snippet", "loop-item", ""], "context": ["edit"]}, "_elementor_data": {"type": "string", "title": "Elementor data", "description": "Elementor JSON as a string", "default": "", "context": ["edit"]}, "_elementor_page_settings": {"type": "object", "title": "Elementor page settings", "description": "Elementor page level settings", "default": "{}", "properties": {"hide_title": {"type": "string", "enum": ["yes", "no"], "default": ""}}, "additionalProperties": true, "context": ["edit"]}, "_elementor_conditions": {"type": "array", "title": "", "description": "Elementor conditions", "default": [], "additionalProperties": true, "context": ["edit"]}}, "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/elementor_library/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/elementor_library/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/uicore-tb": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to posts published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_after": {"description": "Limit response to posts modified after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "author_exclude": {"description": "Ensure result set excludes posts assigned to specific authors.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "before": {"description": "Limit response to posts published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "modified_before": {"description": "Limit response to posts modified before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "search_semantics": {"description": "How to interpret the search input.", "type": "string", "enum": ["exact"], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title"], "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["post_title", "post_content", "post_excerpt"], "type": "string"}, "required": false}, "slug": {"description": "Limit result set to posts with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "status": {"default": "publish", "description": "Limit result set to posts assigned one or more statuses.", "type": "array", "items": {"enum": ["publish", "future", "draft", "pending", "private", "trash", "auto-draft", "inherit", "request-pending", "request-confirmed", "request-failed", "request-completed", "any"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/uicore-tb"}]}}, "/wp/v2/uicore-tb/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the post.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}}}]}, "/wp/v2/uicore-tb/(?P<parent>[\\d]+)/revisions": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by object attribute.", "type": "string", "default": "date", "enum": ["date", "id", "include", "relevance", "slug", "include_slugs", "title"], "required": false}}}]}, "/wp/v2/uicore-tb/(?P<parent>[\\d]+)/revisions/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["DELETE"], "args": {"parent": {"description": "The ID for the parent of the revision.", "type": "integer", "required": false}, "id": {"description": "Unique identifier for the revision.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as revisions do not support trashing.", "required": false}}}]}, "/wp/v2/uicore-tb/(?P<id>[\\d]+)/autosaves": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "date": {"description": "The date the post was published, in the site's timezone.", "type": ["string", "null"], "format": "date-time", "required": false}, "date_gmt": {"description": "The date the post was published, as GMT.", "type": ["string", "null"], "format": "date-time", "required": false}, "slug": {"description": "An alphanumeric identifier for the post unique to its type.", "type": "string", "required": false}, "status": {"description": "A named status for the post.", "type": "string", "enum": ["publish", "future", "draft", "pending", "private"], "required": false}, "password": {"description": "A password to protect access to the content and excerpt.", "type": "string", "required": false}, "title": {"description": "The title for the post.", "type": "object", "properties": {"raw": {"description": "Title for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML title for the post, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "content": {"description": "The content for the post.", "type": "object", "properties": {"raw": {"description": "Content for the post, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the post, transformed for display.", "type": "string", "context": ["view", "edit"], "readonly": true}, "block_version": {"description": "Version of the content block format used by the post.", "type": "integer", "context": ["edit"], "readonly": true}, "protected": {"description": "Whether the content is protected with a password.", "type": "boolean", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "author": {"description": "The ID for the author of the post.", "type": "integer", "required": false}, "featured_media": {"description": "The ID of the featured media for the post.", "type": "integer", "required": false}, "template": {"description": "The theme file to use to display the post.", "type": "string", "required": false}}}]}, "/wp/v2/uicore-tb/(?P<parent>[\\d]+)/autosaves/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"parent": {"description": "The ID for the parent of the autosave.", "type": "integer", "required": false}, "id": {"description": "The ID for the autosave.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/types"}]}}, "/wp/v2/types/(?P<type>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"type": {"description": "An alphanumeric identifier for the post type.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/statuses": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/statuses"}]}}, "/wp/v2/statuses/(?P<status>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "An alphanumeric identifier for the status.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/taxonomies": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "type": {"description": "Limit results to taxonomies associated with a specific post type.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/taxonomies"}]}}, "/wp/v2/taxonomies/(?P<taxonomy>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"taxonomy": {"description": "An alphanumeric identifier for the taxonomy.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/categories": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by term attribute.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Whether to hide terms not assigned to any posts.", "type": "boolean", "default": false, "required": false}, "parent": {"description": "Limit result set to terms assigned to a specific parent.", "type": "integer", "required": false}, "post": {"description": "Limit result set to terms assigned to a specific post.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Limit result set to terms with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": true}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "parent": {"description": "The parent term ID.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/categories"}]}}, "/wp/v2/categories/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "parent": {"description": "The parent term ID.", "type": "integer", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as terms do not support trashing.", "required": false}}}]}, "/wp/v2/tags": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by term attribute.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Whether to hide terms not assigned to any posts.", "type": "boolean", "default": false, "required": false}, "post": {"description": "Limit result set to terms assigned to a specific post.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Limit result set to terms with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": true}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/tags"}]}}, "/wp/v2/tags/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as terms do not support trashing.", "required": false}}}]}, "/wp/v2/menus": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by term attribute.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Whether to hide terms not assigned to any posts.", "type": "boolean", "default": false, "required": false}, "post": {"description": "Limit result set to terms assigned to a specific post.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Limit result set to terms with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": true}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}, "locations": {"description": "The locations assigned to the menu.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "Whether to automatically add top level pages to this menu.", "type": "boolean", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/menus"}]}}, "/wp/v2/menus/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}, "locations": {"description": "The locations assigned to the menu.", "type": "array", "items": {"type": "string"}, "required": false}, "auto_add": {"description": "Whether to automatically add top level pages to this menu.", "type": "boolean", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as terms do not support trashing.", "required": false}}}]}, "/wp/v2/wp_pattern_category": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "asc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by term attribute.", "type": "string", "default": "name", "enum": ["id", "include", "name", "slug", "include_slugs", "term_group", "description", "count"], "required": false}, "hide_empty": {"description": "Whether to hide terms not assigned to any posts.", "type": "boolean", "default": false, "required": false}, "post": {"description": "Limit result set to terms assigned to a specific post.", "type": "integer", "default": null, "required": false}, "slug": {"description": "Limit result set to terms with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": true}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/wp_pattern_category"}]}}, "/wp/v2/wp_pattern_category/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "description": {"description": "HTML description of the term.", "type": "string", "required": false}, "name": {"description": "HTML title for the term.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the term unique to its type.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the term.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as terms do not support trashing.", "required": false}}}]}, "/wp/v2/users": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"default": "asc", "description": "Order sort attribute ascending or descending.", "enum": ["asc", "desc"], "type": "string", "required": false}, "orderby": {"default": "name", "description": "Sort collection by user attribute.", "enum": ["id", "include", "name", "registered_date", "slug", "include_slugs", "email", "url"], "type": "string", "required": false}, "slug": {"description": "Limit result set to users with one or more specific slugs.", "type": "array", "items": {"type": "string"}, "required": false}, "roles": {"description": "Limit result set to users matching at least one specific role provided. Accepts csv list or single role.", "type": "array", "items": {"type": "string"}, "required": false}, "capabilities": {"description": "Limit result set to users matching at least one specific capability provided. Accepts csv list or single capability.", "type": "array", "items": {"type": "string"}, "required": false}, "who": {"description": "Limit result set to users who are considered authors.", "type": "string", "enum": ["authors"], "required": false}, "has_published_posts": {"description": "Limit result set to users who have published posts.", "type": ["boolean", "array"], "items": {"type": "string", "enum": {"post": "post", "page": "page", "attachment": "attachment", "nav_menu_item": "nav_menu_item", "wp_block": "wp_block", "wp_template": "wp_template", "wp_template_part": "wp_template_part", "wp_global_styles": "wp_global_styles", "wp_navigation": "wp_navigation", "wp_font_family": "wp_font_family", "wp_font_face": "wp_font_face", "e-floating-buttons": "e-floating-buttons", "elementor_library": "elementor_library", "uicore-tb": "uicore-tb"}}, "required": false}, "search_columns": {"default": [], "description": "Array of column names to be searched.", "type": "array", "items": {"enum": ["email", "name", "id", "username", "slug"], "type": "string"}, "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"username": {"description": "Login name for the user.", "type": "string", "required": true}, "name": {"description": "Display name for the user.", "type": "string", "required": false}, "first_name": {"description": "First name for the user.", "type": "string", "required": false}, "last_name": {"description": "Last name for the user.", "type": "string", "required": false}, "email": {"description": "The email address for the user.", "type": "string", "format": "email", "required": true}, "url": {"description": "URL of the user.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Description of the user.", "type": "string", "required": false}, "locale": {"description": "Locale for the user.", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "The nickname for the user.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the user.", "type": "string", "required": false}, "roles": {"description": "Roles assigned to the user.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Password for the user (never included).", "type": "string", "required": true}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "The date and time the preferences were updated.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/users"}]}}, "/wp/v2/users/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the user.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the user.", "type": "integer", "required": false}, "username": {"description": "Login name for the user.", "type": "string", "required": false}, "name": {"description": "Display name for the user.", "type": "string", "required": false}, "first_name": {"description": "First name for the user.", "type": "string", "required": false}, "last_name": {"description": "Last name for the user.", "type": "string", "required": false}, "email": {"description": "The email address for the user.", "type": "string", "format": "email", "required": false}, "url": {"description": "URL of the user.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Description of the user.", "type": "string", "required": false}, "locale": {"description": "Locale for the user.", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "The nickname for the user.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the user.", "type": "string", "required": false}, "roles": {"description": "Roles assigned to the user.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Password for the user (never included).", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "The date and time the preferences were updated.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the user.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Required to be true, as users do not support trashing.", "required": false}, "reassign": {"type": "integer", "description": "Reassign the deleted user's posts and links to this user ID.", "required": true}}}]}, "/wp/v2/users/me": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"username": {"description": "Login name for the user.", "type": "string", "required": false}, "name": {"description": "Display name for the user.", "type": "string", "required": false}, "first_name": {"description": "First name for the user.", "type": "string", "required": false}, "last_name": {"description": "Last name for the user.", "type": "string", "required": false}, "email": {"description": "The email address for the user.", "type": "string", "format": "email", "required": false}, "url": {"description": "URL of the user.", "type": "string", "format": "uri", "required": false}, "description": {"description": "Description of the user.", "type": "string", "required": false}, "locale": {"description": "Locale for the user.", "type": "string", "enum": ["", "en_US", "zh_CN"], "required": false}, "nickname": {"description": "The nickname for the user.", "type": "string", "required": false}, "slug": {"description": "An alphanumeric identifier for the user.", "type": "string", "required": false}, "roles": {"description": "Roles assigned to the user.", "type": "array", "items": {"type": "string"}, "required": false}, "password": {"description": "Password for the user (never included).", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": {"persisted_preferences": {"type": "object", "title": "", "description": "", "default": [], "context": ["edit"], "properties": {"_modified": {"description": "The date and time the preferences were updated.", "type": "string", "format": "date-time", "readonly": false}}, "additionalProperties": true}}, "required": false}, "elementor_introduction": {"description": "Elementor user meta data", "type": "object", "properties": {"ai_get_started": {"type": "boolean"}}, "additionalProperties": true, "required": false}}}, {"methods": ["DELETE"], "args": {"force": {"type": "boolean", "default": false, "description": "Required to be true, as users do not support trashing.", "required": false}, "reassign": {"type": "integer", "description": "Reassign the deleted user's posts and links to this user ID.", "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/users/me"}]}}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords": {"namespace": "wp/v2", "methods": ["GET", "POST", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST"], "args": {"app_id": {"description": "A UUID provided by the application to uniquely identify it. It is recommended to use an UUID v5 with the URL or DNS namespace.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "The name of the application password.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": true}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/introspect": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/users/(?P<user_id>(?:[\\d]+|me))/application-passwords/(?P<uuid>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"app_id": {"description": "A UUID provided by the application to uniquely identify it. It is recommended to use an UUID v5 with the URL or DNS namespace.", "type": "string", "format": "uuid", "required": false}, "name": {"description": "The name of the application password.", "type": "string", "minLength": 1, "pattern": ".*\\S.*", "required": false}}}, {"methods": ["DELETE"], "args": []}]}, "/wp/v2/comments": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "after": {"description": "Limit response to comments published after a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "author": {"description": "Limit result set to comments assigned to specific user IDs. Requires authorization.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_exclude": {"description": "Ensure result set excludes comments assigned to specific user IDs. Requires authorization.", "type": "array", "items": {"type": "integer"}, "required": false}, "author_email": {"default": null, "description": "Limit result set to that from a specific author email. Requires authorization.", "format": "email", "type": "string", "required": false}, "before": {"description": "Limit response to comments published before a given ISO8601 compliant date.", "type": "string", "format": "date-time", "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by comment attribute.", "type": "string", "default": "date_gmt", "enum": ["date", "date_gmt", "id", "include", "post", "parent", "type"], "required": false}, "parent": {"default": [], "description": "Limit result set to comments of specific parent IDs.", "type": "array", "items": {"type": "integer"}, "required": false}, "parent_exclude": {"default": [], "description": "Ensure result set excludes specific parent IDs.", "type": "array", "items": {"type": "integer"}, "required": false}, "post": {"default": [], "description": "Limit result set to comments assigned to specific post IDs.", "type": "array", "items": {"type": "integer"}, "required": false}, "status": {"default": "approve", "description": "Limit result set to comments assigned a specific status. Requires authorization.", "type": "string", "required": false}, "type": {"default": "comment", "description": "Limit result set to comments assigned a specific type. Requires authorization.", "type": "string", "required": false}, "password": {"description": "The password for the post if it is password protected.", "type": "string", "required": false}}}, {"methods": ["POST"], "args": {"author": {"description": "The ID of the user object, if author was a user.", "type": "integer", "required": false}, "author_email": {"description": "Email address for the comment author.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "IP address for the comment author.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "Display name for the comment author.", "type": "string", "required": false}, "author_url": {"description": "URL for the comment author.", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "User agent for the comment author.", "type": "string", "required": false}, "content": {"description": "The content for the comment.", "type": "object", "properties": {"raw": {"description": "Content for the comment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the comment, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "The date the comment was published, in the site's timezone.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "The date the comment was published, as GMT.", "type": "string", "format": "date-time", "required": false}, "parent": {"default": 0, "description": "The ID for the parent of the comment.", "type": "integer", "required": false}, "post": {"default": 0, "description": "The ID of the associated post object.", "type": "integer", "required": false}, "status": {"description": "State of the comment.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/comments"}]}}, "/wp/v2/comments/(?P<id>[\\d]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "Unique identifier for the comment.", "type": "integer", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "password": {"description": "The password for the parent post of the comment (if the post is password protected).", "type": "string", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"id": {"description": "Unique identifier for the comment.", "type": "integer", "required": false}, "author": {"description": "The ID of the user object, if author was a user.", "type": "integer", "required": false}, "author_email": {"description": "Email address for the comment author.", "type": "string", "format": "email", "required": false}, "author_ip": {"description": "IP address for the comment author.", "type": "string", "format": "ip", "required": false}, "author_name": {"description": "Display name for the comment author.", "type": "string", "required": false}, "author_url": {"description": "URL for the comment author.", "type": "string", "format": "uri", "required": false}, "author_user_agent": {"description": "User agent for the comment author.", "type": "string", "required": false}, "content": {"description": "The content for the comment.", "type": "object", "properties": {"raw": {"description": "Content for the comment, as it exists in the database.", "type": "string", "context": ["edit"]}, "rendered": {"description": "HTML content for the comment, transformed for display.", "type": "string", "context": ["view", "edit", "embed"], "readonly": true}}, "required": false}, "date": {"description": "The date the comment was published, in the site's timezone.", "type": "string", "format": "date-time", "required": false}, "date_gmt": {"description": "The date the comment was published, as GMT.", "type": "string", "format": "date-time", "required": false}, "parent": {"description": "The ID for the parent of the comment.", "type": "integer", "required": false}, "post": {"description": "The ID of the associated post object.", "type": "integer", "required": false}, "status": {"description": "State of the comment.", "type": "string", "required": false}, "meta": {"description": "Meta fields.", "type": "object", "properties": [], "required": false}}}, {"methods": ["DELETE"], "args": {"id": {"description": "Unique identifier for the comment.", "type": "integer", "required": false}, "force": {"type": "boolean", "default": false, "description": "Whether to bypass Trash and force deletion.", "required": false}, "password": {"description": "The password for the parent post of the comment (if the post is password protected).", "type": "string", "required": false}}}]}, "/wp/v2/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "type": {"default": "post", "description": "Limit results to items of an object type.", "type": "string", "enum": ["post", "term", "post-format"], "required": false}, "subtype": {"default": "any", "description": "Limit results to items of one or more object subtypes.", "type": "array", "items": {"enum": ["post", "page", "e-floating-buttons", "elementor_library", "uicore-tb", "category", "post_tag", "any"], "type": "string"}, "required": false}, "exclude": {"description": "Ensure result set excludes specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}, "include": {"description": "Limit result set to specific IDs.", "type": "array", "items": {"type": "integer"}, "default": [], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/search"}]}}, "/wp/v2/block-renderer/(?P<name>[a-z0-9-]+/[a-z0-9-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET", "POST"], "args": {"name": {"description": "Unique registered name for the block.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["edit"], "default": "view", "required": false}, "attributes": {"description": "Attributes for the block.", "type": "object", "default": [], "required": false}, "post_id": {"description": "ID of the post context.", "type": "integer", "required": false}}}]}, "/wp/v2/block-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Block namespace.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/block-types"}]}}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "namespace": {"description": "Block namespace.", "type": "string", "required": false}}}]}, "/wp/v2/block-types/(?P<namespace>[a-zA-Z0-9_-]+)/(?P<name>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"name": {"description": "Block name.", "type": "string", "required": false}, "namespace": {"description": "Block namespace.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/settings": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": []}, {"methods": ["POST", "PUT", "PATCH"], "args": {"title": {"title": "Title", "description": "Site title.", "type": "string", "required": false}, "description": {"title": "Tagline", "description": "Site tagline.", "type": "string", "required": false}, "url": {"title": "", "description": "Site URL.", "type": "string", "format": "uri", "required": false}, "email": {"title": "", "description": "This address is used for admin purposes, like new user notification.", "type": "string", "format": "email", "required": false}, "timezone": {"title": "", "description": "A city in the same timezone as you.", "type": "string", "required": false}, "date_format": {"title": "", "description": "A date format for all date strings.", "type": "string", "required": false}, "time_format": {"title": "", "description": "A time format for all time strings.", "type": "string", "required": false}, "start_of_week": {"title": "", "description": "A day number of the week that the week should start on.", "type": "integer", "required": false}, "language": {"title": "", "description": "WordPress locale code.", "type": "string", "required": false}, "use_smilies": {"title": "", "description": "Convert emoticons like :-) and :-P to graphics on display.", "type": "boolean", "required": false}, "default_category": {"title": "", "description": "Default post category.", "type": "integer", "required": false}, "default_post_format": {"title": "", "description": "Default post format.", "type": "string", "required": false}, "posts_per_page": {"title": "Maximum posts per page", "description": "Blog pages show at most.", "type": "integer", "required": false}, "show_on_front": {"title": "Show on front", "description": "What to show on the front page", "type": "string", "required": false}, "page_on_front": {"title": "Page on front", "description": "The ID of the page that should be displayed on the front page", "type": "integer", "required": false}, "page_for_posts": {"title": "", "description": "The ID of the page that should display the latest posts", "type": "integer", "required": false}, "default_ping_status": {"title": "", "description": "Allow link notifications from other blogs (pingbacks and trackbacks) on new articles.", "type": "string", "enum": ["open", "closed"], "required": false}, "default_comment_status": {"title": "Allow comments on new posts", "description": "Allow people to submit comments on new posts.", "type": "string", "enum": ["open", "closed"], "required": false}, "site_logo": {"title": "Logo", "description": "Site logo.", "type": "integer", "required": false}, "site_icon": {"title": "Icon", "description": "Site icon.", "type": "integer", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/settings"}]}}, "/wp/v2/themes": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"status": {"description": "Limit result set to themes assigned one or more statuses.", "type": "array", "items": {"enum": ["active", "inactive"], "type": "string"}, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/themes"}]}}, "/wp/v2/themes/(?P<stylesheet>[^\\/:<>\\*\\?\"\\|]+(?:\\/[^\\/:<>\\*\\?\"\\|]+)?)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"stylesheet": {"description": "The theme's stylesheet. This uniquely identifies the theme.", "type": "string", "required": false}}}]}, "/wp/v2/plugins": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "required": false}, "status": {"description": "Limits results to plugins with the given status.", "type": "array", "items": {"type": "string", "enum": ["inactive", "active"]}, "required": false}}}, {"methods": ["POST"], "args": {"slug": {"type": "string", "description": "WordPress.org plugin directory slug.", "pattern": "[\\w\\-]+", "required": true}, "status": {"description": "The plugin activation status.", "type": "string", "enum": ["inactive", "active"], "default": "inactive", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/plugins"}]}}, "/wp/v2/plugins/(?P<plugin>[^.\\/]+(?:\\/[^.\\/]+)?)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}, "status": {"description": "The plugin activation status.", "type": "string", "enum": ["inactive", "active"], "required": false}}}, {"methods": ["DELETE"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "plugin": {"type": "string", "pattern": "[^.\\/]+(?:\\/[^.\\/]+)?", "required": false}}}]}, "/wp/v2/sidebars": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/sidebars"}]}}, "/wp/v2/sidebars/(?P<id>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The id of a registered sidebar", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "args": {"widgets": {"description": "Nested widgets.", "type": "array", "items": {"type": ["object", "string"]}, "required": false}}}]}, "/wp/v2/widget-types": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/widget-types"}]}}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"id": {"description": "The widget type id.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/encode": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "The widget type id.", "type": "string", "required": true}, "instance": {"description": "Current instance settings of the widget.", "type": "object", "required": false}, "form_data": {"description": "Serialized widget form data to encode into instance settings.", "type": "string", "required": false}}}]}, "/wp/v2/widget-types/(?P<id>[a-zA-Z0-9_-]+)/render": {"namespace": "wp/v2", "methods": ["POST"], "endpoints": [{"methods": ["POST"], "args": {"id": {"description": "The widget type id.", "type": "string", "required": true}, "instance": {"description": "Current instance settings of the widget.", "type": "object", "required": false}}}]}, "/wp/v2/widgets": {"namespace": "wp/v2", "methods": ["GET", "POST"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "sidebar": {"description": "The sidebar to return widgets for.", "type": "string", "required": false}}}, {"methods": ["POST"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the widget.", "type": "string", "required": false}, "id_base": {"description": "The type of the widget. Corresponds to ID in widget-types endpoint.", "type": "string", "required": false}, "sidebar": {"default": "wp_inactive_widgets", "description": "The sidebar the widget belongs to.", "type": "string", "required": true}, "instance": {"description": "Instance settings of the widget, if supported.", "type": "object", "properties": {"encoded": {"description": "Base64 encoded representation of the instance settings.", "type": "string", "context": ["edit"]}, "hash": {"description": "Cryptographic hash of the instance settings.", "type": "string", "context": ["edit"]}, "raw": {"description": "Unencoded instance settings, if supported.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "URL-encoded form data from the widget admin form. Used to update a widget that does not support instance. Write only.", "type": "string", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/widgets"}]}}, "/wp/v2/widgets/(?P<id>[\\w\\-]+)": {"namespace": "wp/v2", "methods": ["GET", "POST", "PUT", "PATCH", "DELETE"], "endpoints": [{"methods": ["GET"], "allow_batch": {"v1": true}, "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}, {"methods": ["POST", "PUT", "PATCH"], "allow_batch": {"v1": true}, "args": {"id": {"description": "Unique identifier for the widget.", "type": "string", "required": false}, "id_base": {"description": "The type of the widget. Corresponds to ID in widget-types endpoint.", "type": "string", "required": false}, "sidebar": {"description": "The sidebar the widget belongs to.", "type": "string", "required": false}, "instance": {"description": "Instance settings of the widget, if supported.", "type": "object", "properties": {"encoded": {"description": "Base64 encoded representation of the instance settings.", "type": "string", "context": ["edit"]}, "hash": {"description": "Cryptographic hash of the instance settings.", "type": "string", "context": ["edit"]}, "raw": {"description": "Unencoded instance settings, if supported.", "type": "object", "context": ["edit"]}}, "required": false}, "form_data": {"description": "URL-encoded form data from the widget admin form. Used to update a widget that does not support instance. Write only.", "type": "string", "required": false}}}, {"methods": ["DELETE"], "allow_batch": {"v1": true}, "args": {"force": {"description": "Whether to force removal of the widget, or move it to the inactive sidebar.", "type": "boolean", "required": false}}}]}, "/wp/v2/block-directory/search": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}, "term": {"description": "Limit result set to blocks matching the search term.", "type": "string", "minLength": 1, "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/block-directory/search"}]}}, "/wp/v2/pattern-directory/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 100, "minimum": 1, "maximum": 100, "required": false}, "search": {"description": "Limit results to those matching a string.", "type": "string", "minLength": 1, "required": false}, "category": {"description": "Limit results to those matching a category ID.", "type": "integer", "minimum": 1, "required": false}, "keyword": {"description": "Limit results to those matching a keyword ID.", "type": "integer", "minimum": 1, "required": false}, "slug": {"description": "Limit results to those matching a pattern (slug).", "type": "array", "required": false}, "offset": {"description": "Offset the result set by a specific number of items.", "type": "integer", "required": false}, "order": {"description": "Order sort attribute ascending or descending.", "type": "string", "default": "desc", "enum": ["asc", "desc"], "required": false}, "orderby": {"description": "Sort collection by post attribute.", "type": "string", "default": "date", "enum": ["author", "date", "id", "include", "modified", "parent", "relevance", "slug", "include_slugs", "title", "favorite_count"], "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/pattern-directory/patterns"}]}}, "/wp/v2/block-patterns/patterns": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/block-patterns/patterns"}]}}, "/wp/v2/block-patterns/categories": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/block-patterns/categories"}]}}, "/wp-site-health/v1": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-site-health/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1"}]}}, "/wp-site-health/v1/tests/background-updates": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/background-updates"}]}}, "/wp-site-health/v1/tests/loopback-requests": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/loopback-requests"}]}}, "/wp-site-health/v1/tests/https-status": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/https-status"}]}}, "/wp-site-health/v1/tests/dotorg-communication": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/dotorg-communication"}]}}, "/wp-site-health/v1/tests/authorization-header": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/authorization-header"}]}}, "/wp-site-health/v1/directory-sizes": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/directory-sizes"}]}}, "/wp-site-health/v1/tests/page-cache": {"namespace": "wp-site-health/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-site-health/v1/tests/page-cache"}]}}, "/wp-block-editor/v1": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"namespace": {"default": "wp-block-editor/v1", "required": false}, "context": {"default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-block-editor/v1"}]}}, "/wp-block-editor/v1/url-details": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"url": {"description": "The URL to process.", "type": "string", "format": "uri", "required": true}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-block-editor/v1/url-details"}]}}, "/wp/v2/menu-locations": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/menu-locations"}]}}, "/wp/v2/menu-locations/(?P<location>[\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"location": {"description": "An alphanumeric identifier for the menu location.", "type": "string", "required": false}, "context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}, "/wp-block-editor/v1/export": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-block-editor/v1/export"}]}}, "/wp-block-editor/v1/navigation-fallback": {"namespace": "wp-block-editor/v1", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": []}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp-block-editor/v1/navigation-fallback"}]}}, "/wp/v2/font-collections": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}, "page": {"description": "Current page of the collection.", "type": "integer", "default": 1, "minimum": 1, "required": false}, "per_page": {"description": "Maximum number of items to be returned in result set.", "type": "integer", "default": 10, "minimum": 1, "maximum": 100, "required": false}}}], "_links": {"self": [{"href": "https://www.puffmivape.com/wp-json/wp/v2/font-collections"}]}}, "/wp/v2/font-collections/(?P<slug>[\\/\\w-]+)": {"namespace": "wp/v2", "methods": ["GET"], "endpoints": [{"methods": ["GET"], "args": {"context": {"description": "Scope under which the request is made; determines fields present in response.", "type": "string", "enum": ["view", "embed", "edit"], "default": "view", "required": false}}}]}}, "site_logo": 0, "site_icon": 0, "site_icon_url": "", "_links": {"help": [{"href": "https://developer.wordpress.org/rest-api/"}]}}