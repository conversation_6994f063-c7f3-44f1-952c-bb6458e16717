/*Marker Cluster*/
var MarkerClusterer=function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function e(t,e){return t(e={exports:{}},e.exports),e.exports}var r=function(t){return t&&t.Math==Math&&t},n=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof t&&t)||function(){return this}()||Function("return this")(),i=function(t){try{return!!t()}catch(t){return!0}},o=!i((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),s={}.propertyIsEnumerable,a=Object.getOwnPropertyDescriptor,u={f:a&&!s.call({1:2},1)?function(t){var e=a(this,t);return!!e&&e.enumerable}:s},l=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},c={}.toString,h=function(t){return c.call(t).slice(8,-1)},p="".split,f=i((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==h(t)?p.call(t,""):Object(t)}:Object,g=function(t){if(null==t)throw TypeError("Can't call method on "+t);return t},d=function(t){return f(g(t))},_=function(t){return"object"==typeof t?null!==t:"function"==typeof t},v=function(t,e){if(!_(t))return t;var r,n;if(e&&"function"==typeof(r=t.toString)&&!_(n=r.call(t)))return n;if("function"==typeof(r=t.valueOf)&&!_(n=r.call(t)))return n;if(!e&&"function"==typeof(r=t.toString)&&!_(n=r.call(t)))return n;throw TypeError("Can't convert object to primitive value")},m={}.hasOwnProperty,y=function(t,e){return m.call(t,e)},x=n.document,S=_(x)&&_(x.createElement),M=!o&&!i((function(){return 7!=Object.defineProperty((t="div",S?x.createElement(t):{}),"a",{get:function(){return 7}}).a;var t})),b=Object.getOwnPropertyDescriptor,C={f:o?b:function(t,e){if(t=d(t),e=v(e,!0),M)try{return b(t,e)}catch(t){}if(y(t,e))return l(!u.f.call(t,e),t[e])}},E=function(t){if(!_(t))throw TypeError(String(t)+" is not an object");return t},k=Object.defineProperty,I={f:o?k:function(t,e,r){if(E(t),e=v(e,!0),E(r),M)try{return k(t,e,r)}catch(t){}if("get"in r||"set"in r)throw TypeError("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},w=o?function(t,e,r){return I.f(t,e,l(1,r))}:function(t,e,r){return t[e]=r,t},O=function(t,e){try{w(n,t,e)}catch(r){n[t]=e}return e},A="__core-js_shared__",L=n[A]||O(A,{}),P=Function.toString;"function"!=typeof L.inspectSource&&(L.inspectSource=function(t){return P.call(t)});var T,z,R,j,B=L.inspectSource,Z=n.WeakMap,N="function"==typeof Z&&/native code/.test(B(Z)),D=e((function(t){(t.exports=function(t,e){return L[t]||(L[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.8.1",mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),H=0,$=Math.random(),F=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++H+$).toString(36)},U=D("keys"),G={},V=n.WeakMap;if(N){var W=L.state||(L.state=new V),X=W.get,K=W.has,q=W.set;T=function(t,e){return e.facade=t,q.call(W,t,e),e},z=function(t){return X.call(W,t)||{}},R=function(t){return K.call(W,t)}}else{var Y=U[j="state"]||(U[j]=F(j));G[Y]=!0,T=function(t,e){return e.facade=t,w(t,Y,e),e},z=function(t){return y(t,Y)?t[Y]:{}},R=function(t){return y(t,Y)}}var J={set:T,get:z,has:R,enforce:function(t){return R(t)?z(t):T(t,{})},getterFor:function(t){return function(e){var r;if(!_(e)||(r=z(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return r}}},Q=e((function(t){var e=J.get,r=J.enforce,i=String(String).split("String");(t.exports=function(t,e,o,s){var a,u=!!s&&!!s.unsafe,l=!!s&&!!s.enumerable,c=!!s&&!!s.noTargetGet;"function"==typeof o&&("string"!=typeof e||y(o,"name")||w(o,"name",e),(a=r(o)).source||(a.source=i.join("string"==typeof e?e:""))),t!==n?(u?!c&&t[e]&&(l=!0):delete t[e],l?t[e]=o:w(t,e,o)):l?t[e]=o:O(e,o)})(Function.prototype,"toString",(function(){return"function"==typeof this&&e(this).source||B(this)}))})),tt=n,et=function(t){return"function"==typeof t?t:void 0},rt=function(t,e){return arguments.length<2?et(tt[t])||et(n[t]):tt[t]&&tt[t][e]||n[t]&&n[t][e]},nt=Math.ceil,it=Math.floor,ot=function(t){return isNaN(t=+t)?0:(t>0?it:nt)(t)},st=Math.min,at=function(t){return t>0?st(ot(t),9007199254740991):0},ut=Math.max,lt=Math.min,ct=function(t,e){var r=ot(t);return r<0?ut(r+e,0):lt(r,e)},ht=function(t){return function(e,r,n){var i,o=d(e),s=at(o.length),a=ct(n,s);if(t&&r!=r){for(;s>a;)if((i=o[a++])!=i)return!0}else for(;s>a;a++)if((t||a in o)&&o[a]===r)return t||a||0;return!t&&-1}},pt={includes:ht(!0),indexOf:ht(!1)},ft=pt.indexOf,gt=function(t,e){var r,n=d(t),i=0,o=[];for(r in n)!y(G,r)&&y(n,r)&&o.push(r);for(;e.length>i;)y(n,r=e[i++])&&(~ft(o,r)||o.push(r));return o},dt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],_t=dt.concat("length","prototype"),vt={f:Object.getOwnPropertyNames||function(t){return gt(t,_t)}},mt={f:Object.getOwnPropertySymbols},yt=rt("Reflect","ownKeys")||function(t){var e=vt.f(E(t)),r=mt.f;return r?e.concat(r(t)):e},xt=function(t,e){for(var r=yt(e),n=I.f,i=C.f,o=0;o<r.length;o++){var s=r[o];y(t,s)||n(t,s,i(e,s))}},St=/#|\.prototype\./,Mt=function(t,e){var r=Ct[bt(t)];return r==kt||r!=Et&&("function"==typeof e?i(e):!!e)},bt=Mt.normalize=function(t){return String(t).replace(St,".").toLowerCase()},Ct=Mt.data={},Et=Mt.NATIVE="N",kt=Mt.POLYFILL="P",It=Mt,wt=C.f,Ot=function(t,e){var r,i,o,s,a,u=t.target,l=t.global,c=t.stat;if(r=l?n:c?n[u]||O(u,{}):(n[u]||{}).prototype)for(i in e){if(s=e[i],o=t.noTargetGet?(a=wt(r,i))&&a.value:r[i],!It(l?i:u+(c?".":"#")+i,t.forced)&&void 0!==o){if(typeof s==typeof o)continue;xt(s,o)}(t.sham||o&&o.sham)&&w(s,"sham",!0),Q(r,i,s,t)}},At=function(t,e){var r=[][t];return!!r&&i((function(){r.call(null,e||function(){throw 1},1)}))},Lt=Object.defineProperty,Pt={},Tt=function(t){throw t},zt=function(t,e){if(y(Pt,t))return Pt[t];e||(e={});var r=[][t],n=!!y(e,"ACCESSORS")&&e.ACCESSORS,s=y(e,0)?e[0]:Tt,a=y(e,1)?e[1]:void 0;return Pt[t]=!!r&&!i((function(){if(n&&!o)return!0;var t={length:-1};n?Lt(t,1,{enumerable:!0,get:Tt}):t[1]=1,r.call(t,s,a)}))},Rt=pt.indexOf,jt=[].indexOf,Bt=!!jt&&1/[1].indexOf(1,-0)<0,Zt=At("indexOf"),Nt=zt("indexOf",{ACCESSORS:!0,1:0});Ot({target:"Array",proto:!0,forced:Bt||!Zt||!Nt},{indexOf:function(t){return Bt?jt.apply(this,arguments)||0:Rt(this,t,arguments.length>1?arguments[1]:void 0)}});var Dt,Ht,$t=Array.isArray||function(t){return"Array"==h(t)},Ft=function(t,e,r){var n=v(e);n in t?I.f(t,n,l(0,r)):t[n]=r},Ut=!!Object.getOwnPropertySymbols&&!i((function(){return!String(Symbol())})),Gt=Ut&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Vt=D("wks"),Wt=n.Symbol,Xt=Gt?Wt:Wt&&Wt.withoutSetter||F,Kt=function(t){return y(Vt,t)||(Ut&&y(Wt,t)?Vt[t]=Wt[t]:Vt[t]=Xt("Symbol."+t)),Vt[t]},qt=rt("navigator","userAgent")||"",Yt=n.process,Jt=Yt&&Yt.versions,Qt=Jt&&Jt.v8;Qt?Ht=(Dt=Qt.split("."))[0]+Dt[1]:qt&&(!(Dt=qt.match(/Edge\/(\d+)/))||Dt[1]>=74)&&(Dt=qt.match(/Chrome\/(\d+)/))&&(Ht=Dt[1]);var te=Ht&&+Ht,ee=Kt("species"),re=function(t){return te>=51||!i((function(){var e=[];return(e.constructor={})[ee]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},ne=re("slice"),ie=zt("slice",{ACCESSORS:!0,0:0,1:2}),oe=Kt("species"),se=[].slice,ae=Math.max;Ot({target:"Array",proto:!0,forced:!ne||!ie},{slice:function(t,e){var r,n,i,o=d(this),s=at(o.length),a=ct(t,s),u=ct(void 0===e?s:e,s);if($t(o)&&("function"!=typeof(r=o.constructor)||r!==Array&&!$t(r.prototype)?_(r)&&null===(r=r[oe])&&(r=void 0):r=void 0,r===Array||void 0===r))return se.call(o,a,u);for(n=new(void 0===r?Array:r)(ae(u-a,0)),i=0;a<u;a++,i++)a in o&&Ft(n,i,o[a]);return n.length=i,n}});var ue=function(t){return Object(g(t))},le=Kt("species"),ce=function(t,e){var r;return $t(t)&&("function"!=typeof(r=t.constructor)||r!==Array&&!$t(r.prototype)?_(r)&&null===(r=r[le])&&(r=void 0):r=void 0),new(void 0===r?Array:r)(0===e?0:e)},he=re("splice"),pe=zt("splice",{ACCESSORS:!0,0:0,1:2}),fe=Math.max,ge=Math.min,de=9007199254740991,_e="Maximum allowed length exceeded";Ot({target:"Array",proto:!0,forced:!he||!pe},{splice:function(t,e){var r,n,i,o,s,a,u=ue(this),l=at(u.length),c=ct(t,l),h=arguments.length;if(0===h?r=n=0:1===h?(r=0,n=l-c):(r=h-2,n=ge(fe(ot(e),0),l-c)),l+r-n>de)throw TypeError(_e);for(i=ce(u,n),o=0;o<n;o++)(s=c+o)in u&&Ft(i,o,u[s]);if(i.length=n,r<n){for(o=c;o<l-n;o++)a=o+r,(s=o+n)in u?u[a]=u[s]:delete u[a];for(o=l;o>l-n+r;o--)delete u[o-1]}else if(r>n)for(o=l-n;o>c;o--)a=o+r-1,(s=o+n-1)in u?u[a]=u[s]:delete u[a];for(o=0;o<r;o++)u[o+c]=arguments[o+2];return u.length=l-n+r,i}});var ve={};ve[Kt("toStringTag")]="z";var me="[object z]"===String(ve),ye=Kt("toStringTag"),xe="Arguments"==h(function(){return arguments}()),Se=me?h:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=Object(t),ye))?r:xe?h(e):"Object"==(n=h(e))&&"function"==typeof e.callee?"Arguments":n},Me=me?{}.toString:function(){return"[object "+Se(this)+"]"};me||Q(Object.prototype,"toString",Me,{unsafe:!0});var be=function(){var t=E(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e},Ce="toString",Ee=RegExp.prototype,ke=Ee.toString,Ie=i((function(){return"/a/b"!=ke.call({source:"a",flags:"b"})})),we=ke.name!=Ce;(Ie||we)&&Q(RegExp.prototype,Ce,(function(){var t=E(this),e=String(t.source),r=t.flags;return"/"+e+"/"+String(void 0===r&&t instanceof RegExp&&!("flags"in Ee)?be.call(t):r)}),{unsafe:!0});var Oe=function(t,e){return(Oe=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])})(t,e)};function Ae(t,e){function r(){this.constructor=t}Oe(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var Le=function(){return(Le=Object.assign||function(t){for(var e,r=1,n=arguments.length;r<n;r++)for(var i in e=arguments[r])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)},Pe=[].join,Te=f!=Object,ze=At("join",",");Ot({target:"Array",proto:!0,forced:Te||!ze},{join:function(t){return Pe.call(d(this),void 0===t?",":t)}});var Re=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t},je=function(t){return function(e,r,n,i){Re(r);var o=ue(e),s=f(o),a=at(o.length),u=t?a-1:0,l=t?-1:1;if(n<2)for(;;){if(u in s){i=s[u],u+=l;break}if(u+=l,t?u<0:a<=u)throw TypeError("Reduce of empty array with no initial value")}for(;t?u>=0:a>u;u+=l)u in s&&(i=r(i,s[u],u,o));return i}},Be={left:je(!1),right:je(!0)},Ze="process"==h(n.process),Ne=Be.left,De=At("reduce"),He=zt("reduce",{1:0});Ot({target:"Array",proto:!0,forced:!De||!He||!Ze&&te>79&&te<83},{reduce:function(t){return Ne(this,t,arguments.length,arguments.length>1?arguments[1]:void 0)}});var $e=Object.keys||function(t){return gt(t,dt)};function Fe(t,e){return RegExp(t,e)}Ot({target:"Object",stat:!0,forced:i((function(){$e(1)}))},{keys:function(t){return $e(ue(t))}});var Ue,Ge,Ve={UNSUPPORTED_Y:i((function(){var t=Fe("a","y");return t.lastIndex=2,null!=t.exec("abcd")})),BROKEN_CARET:i((function(){var t=Fe("^r","gy");return t.lastIndex=2,null!=t.exec("str")}))},We=RegExp.prototype.exec,Xe=String.prototype.replace,Ke=We,qe=(Ue=/a/,Ge=/b*/g,We.call(Ue,"a"),We.call(Ge,"a"),0!==Ue.lastIndex||0!==Ge.lastIndex),Ye=Ve.UNSUPPORTED_Y||Ve.BROKEN_CARET,Je=void 0!==/()??/.exec("")[1];(qe||Je||Ye)&&(Ke=function(t){var e,r,n,i,o=this,s=Ye&&o.sticky,a=be.call(o),u=o.source,l=0,c=t;return s&&(-1===(a=a.replace("y","")).indexOf("g")&&(a+="g"),c=String(t).slice(o.lastIndex),o.lastIndex>0&&(!o.multiline||o.multiline&&"\n"!==t[o.lastIndex-1])&&(u="(?: "+u+")",c=" "+c,l++),r=new RegExp("^(?:"+u+")",a)),Je&&(r=new RegExp("^"+u+"$(?!\\s)",a)),qe&&(e=o.lastIndex),n=We.call(s?r:o,c),s?n?(n.input=n.input.slice(l),n[0]=n[0].slice(l),n.index=o.lastIndex,o.lastIndex+=n[0].length):o.lastIndex=0:qe&&n&&(o.lastIndex=o.global?n.index+n[0].length:e),Je&&n&&n.length>1&&Xe.call(n[0],r,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(n[i]=void 0)})),n});var Qe=Ke;Ot({target:"RegExp",proto:!0,forced:/./.exec!==Qe},{exec:Qe});var tr=Kt("species"),er=!i((function(){var t=/./;return t.exec=function(){var t=[];return t.groups={a:"7"},t},"7"!=="".replace(t,"$<a>")})),rr="$0"==="a".replace(/./,"$0"),nr=Kt("replace"),ir=!!/./[nr]&&""===/./[nr]("a","$0"),or=!i((function(){var t=/(?:)/,e=t.exec;t.exec=function(){return e.apply(this,arguments)};var r="ab".split(t);return 2!==r.length||"a"!==r[0]||"b"!==r[1]})),sr=function(t,e,r,n){var o=Kt(t),s=!i((function(){var e={};return e[o]=function(){return 7},7!=""[t](e)})),a=s&&!i((function(){var e=!1,r=/a/;return"split"===t&&((r={}).constructor={},r.constructor[tr]=function(){return r},r.flags="",r[o]=/./[o]),r.exec=function(){return e=!0,null},r[o](""),!e}));if(!s||!a||"replace"===t&&(!er||!rr||ir)||"split"===t&&!or){var u=/./[o],l=r(o,""[t],(function(t,e,r,n,i){return e.exec===Qe?s&&!i?{done:!0,value:u.call(e,r,n)}:{done:!0,value:t.call(r,e,n)}:{done:!1}}),{REPLACE_KEEPS_$0:rr,REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE:ir}),c=l[0],h=l[1];Q(String.prototype,t,c),Q(RegExp.prototype,o,2==e?function(t,e){return h.call(t,this,e)}:function(t){return h.call(t,this)})}n&&w(RegExp.prototype[o],"sham",!0)},ar=function(t){return function(e,r){var n,i,o=String(g(e)),s=ot(r),a=o.length;return s<0||s>=a?t?"":void 0:(n=o.charCodeAt(s))<55296||n>56319||s+1===a||(i=o.charCodeAt(s+1))<56320||i>57343?t?o.charAt(s):n:t?o.slice(s,s+2):i-56320+(n-55296<<10)+65536}},ur={codeAt:ar(!1),charAt:ar(!0)}.charAt,lr=function(t,e,r){return e+(r?ur(t,e).length:1)},cr=function(t,e){var r=t.exec;if("function"==typeof r){var n=r.call(t,e);if("object"!=typeof n)throw TypeError("RegExp exec method returned something other than an Object or null");return n}if("RegExp"!==h(t))throw TypeError("RegExp#exec called on incompatible receiver");return Qe.call(t,e)},hr=Math.max,pr=Math.min,fr=Math.floor,gr=/\$([$&'`]|\d\d?|<[^>]*>)/g,dr=/\$([$&'`]|\d\d?)/g;sr("replace",2,(function(t,e,r,n){var i=n.REGEXP_REPLACE_SUBSTITUTES_UNDEFINED_CAPTURE,o=n.REPLACE_KEEPS_$0,s=i?"$":"$0";return[function(r,n){var i=g(this),o=null==r?void 0:r[t];return void 0!==o?o.call(r,i,n):e.call(String(i),r,n)},function(t,n){if(!i&&o||"string"==typeof n&&-1===n.indexOf(s)){var u=r(e,t,this,n);if(u.done)return u.value}var l=E(t),c=String(this),h="function"==typeof n;h||(n=String(n));var p=l.global;if(p){var f=l.unicode;l.lastIndex=0}for(var g=[];;){var d=cr(l,c);if(null===d)break;if(g.push(d),!p)break;""===String(d[0])&&(l.lastIndex=lr(c,at(l.lastIndex),f))}for(var _,v="",m=0,y=0;y<g.length;y++){d=g[y];for(var x=String(d[0]),S=hr(pr(ot(d.index),c.length),0),M=[],b=1;b<d.length;b++)M.push(void 0===(_=d[b])?_:String(_));var C=d.groups;if(h){var k=[x].concat(M,S,c);void 0!==C&&k.push(C);var I=String(n.apply(void 0,k))}else I=a(x,c,S,M,C,n);S>=m&&(v+=c.slice(m,S)+I,m=S+x.length)}return v+c.slice(m)}];function a(t,r,n,i,o,s){var a=n+t.length,u=i.length,l=dr;return void 0!==o&&(o=ue(o),l=gr),e.call(s,l,(function(e,s){var l;switch(s.charAt(0)){case"$":return"$";case"&":return t;case"`":return r.slice(0,n);case"'":return r.slice(a);case"<":l=o[s.slice(1,-1)];break;default:var c=+s;if(0===c)return e;if(c>u){var h=fr(c/10);return 0===h?e:h<=u?void 0===i[h-1]?s.charAt(1):i[h-1]+s.charAt(1):e}l=i[c-1]}return void 0===l?"":l}))}}));var _r=Kt("match"),vr=Kt("species"),mr=[].push,yr=Math.min,xr=4294967295,Sr=!i((function(){return!RegExp(xr,"y")}));sr("split",2,(function(t,e,r){var n;return n="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1).length||2!="ab".split(/(?:ab)*/).length||4!=".".split(/(.?)(.?)/).length||".".split(/()()/).length>1||"".split(/.?/).length?function(t,r){var n,i,o=String(g(this)),s=void 0===r?xr:r>>>0;if(0===s)return[];if(void 0===t)return[o];if(!_(n=t)||!(void 0!==(i=n[_r])?i:"RegExp"==h(n)))return e.call(o,t,s);for(var a,u,l,c=[],p=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.unicode?"u":"")+(t.sticky?"y":""),f=0,d=new RegExp(t.source,p+"g");(a=Qe.call(d,o))&&!((u=d.lastIndex)>f&&(c.push(o.slice(f,a.index)),a.length>1&&a.index<o.length&&mr.apply(c,a.slice(1)),l=a[0].length,f=u,c.length>=s));)d.lastIndex===a.index&&d.lastIndex++;return f===o.length?!l&&d.test("")||c.push(""):c.push(o.slice(f)),c.length>s?c.slice(0,s):c}:"0".split(void 0,0).length?function(t,r){return void 0===t&&0===r?[]:e.call(this,t,r)}:e,[function(e,r){var i=g(this),o=null==e?void 0:e[t];return void 0!==o?o.call(e,i,r):n.call(String(i),e,r)},function(t,i){var o=r(n,t,this,i,n!==e);if(o.done)return o.value;var s=E(t),a=String(this),u=function(t,e){var r,n=E(t).constructor;return void 0===n||null==(r=E(n)[vr])?e:Re(r)}(s,RegExp),l=s.unicode,c=(s.ignoreCase?"i":"")+(s.multiline?"m":"")+(s.unicode?"u":"")+(Sr?"y":"g"),h=new u(Sr?s:"^(?:"+s.source+")",c),p=void 0===i?xr:i>>>0;if(0===p)return[];if(0===a.length)return null===cr(h,a)?[a]:[];for(var f=0,g=0,d=[];g<a.length;){h.lastIndex=Sr?g:0;var _,v=cr(h,Sr?a:a.slice(g));if(null===v||(_=yr(at(h.lastIndex+(Sr?0:g)),a.length))===f)g=lr(a,g,l);else{if(d.push(a.slice(f,g)),d.length===p)return d;for(var m=1;m<=v.length-1;m++)if(d.push(v[m]),d.length===p)return d;g=f=_}}return d.push(a.slice(f)),d}]}),!Sr);var Mr=function t(){!function(t,e){for(var r in e.prototype)t.prototype[r]=e.prototype[r]}(t,google.maps.OverlayView)};function br(t){return Object.keys(t).reduce((function(e,r){return t[r]&&e.push(r+":"+t[r]),e}),[]).join(";")}function Cr(t){return t?t+"px":void 0}var Er=function(t){function e(e,r){var n=t.call(this)||this;return n.cluster_=e,n.styles_=r,n.center_=null,n.div_=null,n.sums_=null,n.visible_=!1,n.style=null,n.setMap(e.getMap()),n}return Ae(e,t),e.prototype.onAdd=function(){var t,e,r=this,n=this.cluster_.getMarkerClusterer(),i=google.maps.version.split("."),o=i[0],s=i[1],a=100*parseInt(o,10)+parseInt(s,10);this.div_=document.createElement("div"),this.visible_&&this.show(),this.getPanes().overlayMouseTarget.appendChild(this.div_),this.boundsChangedListener_=google.maps.event.addListener(this.getMap(),"bounds_changed",(function(){e=t})),this.div_.addEventListener("mousedown",(function(){t=!0,e=!1})),a>=332&&this.div_.addEventListener("click",(function(t){t.stopPropagation()})),this.div_.addEventListener("click",(function(i){if(t=!1,!e){if(google.maps.event.trigger(n,"click",r.cluster_),google.maps.event.trigger(n,"clusterclick",r.cluster_),n.getZoomOnClick()){var o=n.getMaxZoom(),s=r.cluster_.getBounds();n.getMap().fitBounds(s),setTimeout((function(){n.getMap().fitBounds(s),null!==o&&n.getMap().getZoom()>o&&n.getMap().setZoom(o+1)}),100)}i.cancelBubble=!0,i.stopPropagation&&i.stopPropagation()}})),this.div_.addEventListener("mouseover",(function(){google.maps.event.trigger(n,"mouseover",r.cluster_)})),this.div_.addEventListener("mouseout",(function(){google.maps.event.trigger(n,"mouseout",r.cluster_)}))},e.prototype.onRemove=function(){this.div_&&this.div_.parentNode&&(this.hide(),google.maps.event.removeListener(this.boundsChangedListener_),google.maps.event.clearInstanceListeners(this.div_),this.div_.parentNode.removeChild(this.div_),this.div_=null)},e.prototype.draw=function(){if(this.visible_){var t=this.getPosFromLatLng_(this.center_);this.div_.style.top=t.y+"px",this.div_.style.left=t.x+"px"}},e.prototype.hide=function(){this.div_&&(this.div_.style.display="none"),this.visible_=!1},e.prototype.show=function(){this.div_&&(this.div_.className=this.className_,this.div_.style.cssText=this.createCss_(this.getPosFromLatLng_(this.center_)),this.div_.innerHTML=(this.style.url?this.getImageElementHtml():"")+this.getLabelDivHtml(),void 0===this.sums_.title||""===this.sums_.title?this.div_.title=this.cluster_.getMarkerClusterer().getTitle():this.div_.title=this.sums_.title,this.div_.style.display=""),this.visible_=!0},e.prototype.getLabelDivHtml=function(){return'\n<div aria-label="'+this.cluster_.getMarkerClusterer().ariaLabelFn(this.sums_.text)+'" style="'+br({position:"absolute",top:Cr(this.anchorText_[0]),left:Cr(this.anchorText_[1]),color:this.style.textColor,"font-size":Cr(this.style.textSize),"font-family":this.style.fontFamily,"font-weight":this.style.fontWeight,"font-style":this.style.fontStyle,"text-decoration":this.style.textDecoration,"text-align":"center",width:Cr(this.style.width),"line-height":Cr(this.style.textLineHeight)})+'" tabindex="0">\n  <span aria-hidden="true">'+this.sums_.text+"</span>\n</div>\n"},e.prototype.getImageElementHtml=function(){var t=(this.style.backgroundPosition||"0 0").split(" "),e=parseInt(t[0].replace(/^\s+|\s+$/g,""),10),r=parseInt(t[1].replace(/^\s+|\s+$/g,""),10),n={};if(this.cluster_.getMarkerClusterer().getEnableRetinaIcons())n={width:Cr(this.style.width),height:Cr(this.style.height)};else{var i=[-1*r,-1*e+this.style.width,-1*r+this.style.height,-1*e];n={clip:"rect("+i[0]+"px, "+i[1]+"px, "+i[2]+"px, "+i[3]+"px)"}}var o=br(Le({position:"absolute",top:Cr(r),left:Cr(e)},n));return'<img alt="'+this.sums_.text+'" aria-hidden="true" src="'+this.style.url+'" style="'+o+'"/>'},e.prototype.useStyle=function(t){this.sums_=t;var e=Math.max(0,t.index-1);e=Math.min(this.styles_.length-1,e),this.style=this.styles_[e],this.anchorText_=this.style.anchorText||[0,0],this.anchorIcon_=this.style.anchorIcon||[Math.floor(this.style.height/2),Math.floor(this.style.width/2)],this.className_=this.cluster_.getMarkerClusterer().getClusterClass()+" "+(this.style.className||"cluster-"+e)},e.prototype.setCenter=function(t){this.center_=t},e.prototype.createCss_=function(t){return br({"z-index":""+this.cluster_.getMarkerClusterer().getZIndex(),top:Cr(t.y),left:Cr(t.x),width:Cr(this.style.width),height:Cr(this.style.height),cursor:"pointer",position:"absolute","-webkit-user-select":"none","-khtml-user-select":"none","-moz-user-select":"none","-o-user-select":"none","user-select":"none"})},e.prototype.getPosFromLatLng_=function(t){var e=this.getProjection().fromLatLngToDivPixel(t);return e.x=Math.floor(e.x-this.anchorIcon_[1]),e.y=Math.floor(e.y-this.anchorIcon_[0]),e},e}(Mr),kr=function(){function t(t){this.markerClusterer_=t,this.map_=this.markerClusterer_.getMap(),this.minClusterSize_=this.markerClusterer_.getMinimumClusterSize(),this.averageCenter_=this.markerClusterer_.getAverageCenter(),this.markers_=[],this.center_=null,this.bounds_=null,this.clusterIcon_=new Er(this,this.markerClusterer_.getStyles())}return t.prototype.getSize=function(){return this.markers_.length},t.prototype.getMarkers=function(){return this.markers_},t.prototype.getCenter=function(){return this.center_},t.prototype.getMap=function(){return this.map_},t.prototype.getMarkerClusterer=function(){return this.markerClusterer_},t.prototype.getBounds=function(){for(var t=new google.maps.LatLngBounds(this.center_,this.center_),e=this.getMarkers(),r=0;r<e.length;r++)t.extend(e[r].getPosition());return t},t.prototype.remove=function(){this.clusterIcon_.setMap(null),this.markers_=[],delete this.markers_},t.prototype.addMarker=function(t){if(this.isMarkerAlreadyAdded_(t))return!1;if(this.center_){if(this.averageCenter_){var e=this.markers_.length+1,r=(this.center_.lat()*(e-1)+t.getPosition().lat())/e,n=(this.center_.lng()*(e-1)+t.getPosition().lng())/e;this.center_=new google.maps.LatLng(r,n),this.calculateBounds_()}}else this.center_=t.getPosition(),this.calculateBounds_();t.isAdded=!0,this.markers_.push(t);var i=this.markers_.length,o=this.markerClusterer_.getMaxZoom();if(null!==o&&this.map_.getZoom()>o)t.getMap()!==this.map_&&t.setMap(this.map_);else if(i<this.minClusterSize_)t.getMap()!==this.map_&&t.setMap(this.map_);else if(i===this.minClusterSize_)for(var s=0;s<i;s++)this.markers_[s].setMap(null);else t.setMap(null);return!0},t.prototype.isMarkerInClusterBounds=function(t){return this.bounds_.contains(t.getPosition())},t.prototype.calculateBounds_=function(){var t=new google.maps.LatLngBounds(this.center_,this.center_);this.bounds_=this.markerClusterer_.getExtendedBounds(t)},t.prototype.updateIcon=function(){var t=this.markers_.length,e=this.markerClusterer_.getMaxZoom();if(null!==e&&this.map_.getZoom()>e)this.clusterIcon_.hide();else if(t<this.minClusterSize_)this.clusterIcon_.hide();else{var r=this.markerClusterer_.getStyles().length,n=this.markerClusterer_.getCalculator()(this.markers_,r);this.clusterIcon_.setCenter(this.center_),this.clusterIcon_.useStyle(n),this.clusterIcon_.show()}},t.prototype.isMarkerAlreadyAdded_=function(t){if(this.markers_.indexOf)return-1!==this.markers_.indexOf(t);for(var e=0;e<this.markers_.length;e++)if(t===this.markers_[e])return!0;return!1},t}(),Ir=function(t,e,r){return void 0!==t[e]?t[e]:r};return function(t){function e(r,n,i){void 0===n&&(n=[]),void 0===i&&(i={});var o=t.call(this)||this;return o.options=i,o.markers_=[],o.clusters_=[],o.listeners_=[],o.activeMap_=null,o.ready_=!1,o.ariaLabelFn=o.options.ariaLabelFn||function(){return""},o.zIndex_=o.options.zIndex||google.maps.Marker.MAX_ZINDEX+1,o.gridSize_=o.options.gridSize||60,o.minClusterSize_=o.options.minimumClusterSize||2,o.maxZoom_=o.options.maxZoom||null,o.styles_=o.options.styles||[],o.title_=o.options.title||"",o.zoomOnClick_=Ir(o.options,"zoomOnClick",!0),o.averageCenter_=Ir(o.options,"averageCenter",!1),o.ignoreHidden_=Ir(o.options,"ignoreHidden",!1),o.enableRetinaIcons_=Ir(o.options,"enableRetinaIcons",!1),o.imagePath_=o.options.imagePath||e.IMAGE_PATH,o.imageExtension_=o.options.imageExtension||e.IMAGE_EXTENSION,o.imageSizes_=o.options.imageSizes||e.IMAGE_SIZES,o.calculator_=o.options.calculator||e.CALCULATOR,o.batchSize_=o.options.batchSize||e.BATCH_SIZE,o.batchSizeIE_=o.options.batchSizeIE||e.BATCH_SIZE_IE,o.clusterClass_=o.options.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(o.batchSize_=o.batchSizeIE_),o.setupStyles_(),o.addMarkers(n,!0),o.setMap(r),o}return Ae(e,t),e.prototype.onAdd=function(){var t=this;this.activeMap_=this.getMap(),this.ready_=!0,this.repaint(),this.prevZoom_=this.getMap().getZoom(),this.listeners_=[google.maps.event.addListener(this.getMap(),"zoom_changed",(function(){var e=t.getMap(),r=e.minZoom||0,n=Math.min(e.maxZoom||100,e.mapTypes[e.getMapTypeId()].maxZoom),i=Math.min(Math.max(t.getMap().getZoom(),r),n);t.prevZoom_!=i&&(t.prevZoom_=i,t.resetViewport_(!1))})),google.maps.event.addListener(this.getMap(),"idle",(function(){t.redraw_()}))]},e.prototype.onRemove=function(){for(var t=0;t<this.markers_.length;t++)this.markers_[t].getMap()!==this.activeMap_&&this.markers_[t].setMap(this.activeMap_);for(t=0;t<this.clusters_.length;t++)this.clusters_[t].remove();this.clusters_=[];for(t=0;t<this.listeners_.length;t++)google.maps.event.removeListener(this.listeners_[t]);this.listeners_=[],this.activeMap_=null,this.ready_=!1},e.prototype.draw=function(){},e.prototype.setupStyles_=function(){if(!(this.styles_.length>0))for(var t=0;t<this.imageSizes_.length;t++){var r=this.imageSizes_[t];this.styles_.push(e.withDefaultStyle({url:this.imagePath_+(t+1)+"."+this.imageExtension_,height:r,width:r}))}},e.prototype.fitMapToMarkers=function(t){for(var e=this.getMarkers(),r=new google.maps.LatLngBounds,n=0;n<e.length;n++)!e[n].getVisible()&&this.getIgnoreHidden()||r.extend(e[n].getPosition());this.getMap().fitBounds(r,t)},e.prototype.getGridSize=function(){return this.gridSize_},e.prototype.setGridSize=function(t){this.gridSize_=t},e.prototype.getMinimumClusterSize=function(){return this.minClusterSize_},e.prototype.setMinimumClusterSize=function(t){this.minClusterSize_=t},e.prototype.getMaxZoom=function(){return this.maxZoom_},e.prototype.setMaxZoom=function(t){this.maxZoom_=t},e.prototype.getZIndex=function(){return this.zIndex_},e.prototype.setZIndex=function(t){this.zIndex_=t},e.prototype.getStyles=function(){return this.styles_},e.prototype.setStyles=function(t){this.styles_=t},e.prototype.getTitle=function(){return this.title_},e.prototype.setTitle=function(t){this.title_=t},e.prototype.getZoomOnClick=function(){return this.zoomOnClick_},e.prototype.setZoomOnClick=function(t){this.zoomOnClick_=t},e.prototype.getAverageCenter=function(){return this.averageCenter_},e.prototype.setAverageCenter=function(t){this.averageCenter_=t},e.prototype.getIgnoreHidden=function(){return this.ignoreHidden_},e.prototype.setIgnoreHidden=function(t){this.ignoreHidden_=t},e.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons_},e.prototype.setEnableRetinaIcons=function(t){this.enableRetinaIcons_=t},e.prototype.getImageExtension=function(){return this.imageExtension_},e.prototype.setImageExtension=function(t){this.imageExtension_=t},e.prototype.getImagePath=function(){return this.imagePath_},e.prototype.setImagePath=function(t){this.imagePath_=t},e.prototype.getImageSizes=function(){return this.imageSizes_},e.prototype.setImageSizes=function(t){this.imageSizes_=t},e.prototype.getCalculator=function(){return this.calculator_},e.prototype.setCalculator=function(t){this.calculator_=t},e.prototype.getBatchSizeIE=function(){return this.batchSizeIE_},e.prototype.setBatchSizeIE=function(t){this.batchSizeIE_=t},e.prototype.getClusterClass=function(){return this.clusterClass_},e.prototype.setClusterClass=function(t){this.clusterClass_=t},e.prototype.getMarkers=function(){return this.markers_},e.prototype.getTotalMarkers=function(){return this.markers_.length},e.prototype.getClusters=function(){return this.clusters_},e.prototype.getTotalClusters=function(){return this.clusters_.length},e.prototype.addMarker=function(t,e){this.pushMarkerTo_(t),e||this.redraw_()},e.prototype.addMarkers=function(t,e){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&this.pushMarkerTo_(t[r]);e||this.redraw_()},e.prototype.pushMarkerTo_=function(t){var e=this;t.getDraggable()&&google.maps.event.addListener(t,"dragend",(function(){e.ready_&&(t.isAdded=!1,e.repaint())})),t.isAdded=!1,this.markers_.push(t)},e.prototype.removeMarker=function(t,e){var r=this.removeMarker_(t);return!e&&r&&this.repaint(),r},e.prototype.removeMarkers=function(t,e){for(var r=!1,n=0;n<t.length;n++){var i=this.removeMarker_(t[n]);r=r||i}return!e&&r&&this.repaint(),r},e.prototype.removeMarker_=function(t){var e=-1;if(this.markers_.indexOf)e=this.markers_.indexOf(t);else for(var r=0;r<this.markers_.length;r++)if(t===this.markers_[r]){e=r;break}return-1!==e&&(t.setMap(null),this.markers_.splice(e,1),!0)},e.prototype.clearMarkers=function(){this.resetViewport_(!0),this.markers_=[]},e.prototype.repaint=function(){var t=this.clusters_.slice();this.clusters_=[],this.resetViewport_(!1),this.redraw_(),setTimeout((function(){for(var e=0;e<t.length;e++)t[e].remove()}),0)},e.prototype.getExtendedBounds=function(t){var e=this.getProjection(),r=new google.maps.LatLng(t.getNorthEast().lat(),t.getNorthEast().lng()),n=new google.maps.LatLng(t.getSouthWest().lat(),t.getSouthWest().lng()),i=e.fromLatLngToDivPixel(r);i.x+=this.gridSize_,i.y-=this.gridSize_;var o=e.fromLatLngToDivPixel(n);o.x-=this.gridSize_,o.y+=this.gridSize_;var s=e.fromDivPixelToLatLng(i),a=e.fromDivPixelToLatLng(o);return t.extend(s),t.extend(a),t},e.prototype.redraw_=function(){this.createClusters_(0)},e.prototype.resetViewport_=function(t){for(var e=0;e<this.clusters_.length;e++)this.clusters_[e].remove();this.clusters_=[];for(e=0;e<this.markers_.length;e++){var r=this.markers_[e];r.isAdded=!1,t&&r.setMap(null)}},e.prototype.distanceBetweenPoints_=function(t,e){var r=(e.lat()-t.lat())*Math.PI/180,n=(e.lng()-t.lng())*Math.PI/180,i=Math.sin(r/2)*Math.sin(r/2)+Math.cos(t.lat()*Math.PI/180)*Math.cos(e.lat()*Math.PI/180)*Math.sin(n/2)*Math.sin(n/2);return 6371*(2*Math.atan2(Math.sqrt(i),Math.sqrt(1-i)))},e.prototype.isMarkerInBounds_=function(t,e){return e.contains(t.getPosition())},e.prototype.addToClosestCluster_=function(t){for(var e=4e4,r=null,n=0;n<this.clusters_.length;n++){var i,o=(i=this.clusters_[n]).getCenter();if(o){var s=this.distanceBetweenPoints_(o,t.getPosition());s<e&&(e=s,r=i)}}r&&r.isMarkerInClusterBounds(t)?r.addMarker(t):((i=new kr(this)).addMarker(t),this.clusters_.push(i))},e.prototype.createClusters_=function(t){var e=this;if(this.ready_){var r;0===t&&(google.maps.event.trigger(this,"clusteringbegin",this),void 0!==this.timerRefStatic&&(clearTimeout(this.timerRefStatic),delete this.timerRefStatic)),r=this.getMap().getZoom()>3?new google.maps.LatLngBounds(this.getMap().getBounds().getSouthWest(),this.getMap().getBounds().getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625));for(var n=this.getExtendedBounds(r),i=Math.min(t+this.batchSize_,this.markers_.length),o=t;o<i;o++){var s=this.markers_[o];!s.isAdded&&this.isMarkerInBounds_(s,n)&&(!this.ignoreHidden_||this.ignoreHidden_&&s.getVisible())&&this.addToClosestCluster_(s)}if(i<this.markers_.length)this.timerRefStatic=window.setTimeout((function(){e.createClusters_(i)}),0);else{delete this.timerRefStatic,google.maps.event.trigger(this,"clusteringend",this);for(o=0;o<this.clusters_.length;o++)this.clusters_[o].updateIcon()}}},e.CALCULATOR=function(t,e){for(var r=0,n=t.length,i=n;0!==i;)i=Math.floor(i/10),r++;return r=Math.min(r,e),{text:n.toString(),index:r,title:""}},e.withDefaultStyle=function(t){return Le({textColor:"black",textSize:11,textDecoration:"none",textLineHeight:t.height,fontWeight:"bold",fontStyle:"normal",fontFamily:"Arial,sans-serif",backgroundPosition:"0 0"},t)},e.BATCH_SIZE=2e3,e.BATCH_SIZE_IE=500,e.IMAGE_PATH="../images/m",e.IMAGE_EXTENSION="png",e.IMAGE_SIZES=[53,56,66,78,90],e}(Mr)}();

var asl_jQuery = jQuery;

/*! JsRender v1.0.13: http://jsviews.com/#jsrender */
!function(t,e){var n=e.jQuery;"object"==typeof exports?module.exports=n?t(e,n):function(n){if(n&&!n.fn)throw"Provide jQuery or null";return t(e,n)}:"function"==typeof define&&define.amd?define(function(){return t(e)}):t(e,!1)}(function(t,e){"use strict";function n(t,e){return function(){var n,r=this,i=r.base;return r.base=t,n=e.apply(r,arguments),r.base=i,n}}function r(t,e){return st(e)&&(e=n(t?t._d?t:n(a,t):a,e),e._d=(t&&t._d||0)+1),e}function i(t,e){var n,i=e.props;for(n in i)!Vt.test(n)||t[n]&&t[n].fix||(t[n]="convert"!==n?r(t.constructor.prototype[n],i[n]):i[n])}function o(t){return t}function a(){return""}function s(t){try{throw console.log("JsRender dbg breakpoint: "+t),"dbg breakpoint"}catch(e){}return this.base?this.baseApply(arguments):t}function p(t){this.name=(e.link?"JsViews":"JsRender")+" Error",this.message=t||this.name}function l(t,e){if(t){for(var n in e)t[n]=e[n];return t}}function d(t,e,n){return t?pt(t)?d.apply(ot,t):(wt=n?n[0]:wt,/^(\W|_){5}$/.test(t+e+wt)||S("Invalid delimiters"),mt=t[0],_t=t[1],xt=e[0],bt=e[1],gt.delimiters=[mt+_t,xt+bt,wt],t="\\"+mt+"(\\"+wt+")?\\"+_t,e="\\"+xt+"\\"+bt,rt="(?:(\\w+(?=[\\/\\s\\"+xt+"]))|(\\w+)?(:)|(>)|(\\*))\\s*((?:[^\\"+xt+"]|\\"+xt+"(?!\\"+bt+"))*?)",ft.rTag="(?:"+rt+")",rt=new RegExp("(?:"+t+rt+"(\\/)?|\\"+mt+"(\\"+wt+")?\\"+_t+"(?:(?:\\/(\\w+))\\s*|!--[\\s\\S]*?--))"+e,"g"),ft.rTmpl=new RegExp("^\\s|\\s$|<.*>|([^\\\\]|^)[{}]|"+t+".*"+e),ht):gt.delimiters}function c(t,e){e||t===!0||(e=t,t=void 0);var n,r,i,o,a=this,s="root"===e;if(t){if(o=e&&a.type===e&&a,!o)if(n=a.views,a._.useKey){for(r in n)if(o=e?n[r].get(t,e):n[r])break}else for(r=0,i=n.length;!o&&r<i;r++)o=e?n[r].get(t,e):n[r]}else if(s)o=a.root;else if(e)for(;a&&!o;)o=a.type===e?a:void 0,a=a.parent;else o=a.parent;return o||void 0}function u(){var t=this.get("item");return t?t.index:void 0}function f(){return this.index}function g(t,e,n,r){var i,o,s,p=0;if(1===n&&(r=1,n=void 0),e)for(o=e.split("."),s=o.length;t&&p<s;p++)i=t,t=o[p]?t[o[p]]:t;return n&&(n.lt=n.lt||p<s),void 0===t?r?a:"":r?function(){return t.apply(i,arguments)}:t}function v(n,r,i){var o,a,s,p,d,c,u,f=this,g=!Ct&&arguments.length>1,v=f.ctx;if(n){if(f._||(d=f.index,f=f.tag),c=f,v&&v.hasOwnProperty(n)||(v=ct).hasOwnProperty(n)){if(s=v[n],"tag"===n||"tagCtx"===n||"root"===n||"parentTags"===n)return s}else v=void 0;if((!Ct&&f.tagCtx||f.linked)&&(s&&s._cxp||(f=f.tagCtx||st(s)?f:(f=f.scope||f,!f.isTop&&f.ctx.tag||f),void 0!==s&&f.tagCtx&&(f=f.tagCtx.view.scope),v=f._ocps,s=v&&v.hasOwnProperty(n)&&v[n]||s,s&&s._cxp||!i&&!g||((v||(f._ocps=f._ocps||{}))[n]=s=[{_ocp:s,_vw:c,_key:n}],s._cxp={path:jt,ind:0,updateValue:function(t,n){return e.observable(s[0]).setProperty(jt,t),this}})),p=s&&s._cxp)){if(arguments.length>2)return a=s[1]?ft._ceo(s[1].deps):[jt],a.unshift(s[0]),a._cxp=p,a;if(d=p.tagElse,u=s[1]?p.tag&&p.tag.cvtArgs?p.tag.cvtArgs(d,1)[p.ind]:s[1](s[0].data,s[0],ft):s[0]._ocp,g)return ft._ucp(n,r,f,p),f;s=u}return s&&st(s)&&(o=function(){return s.apply(this&&this!==t?this:c,arguments)},l(o,s)),o||s}}function h(t){return t&&(t.fn?t:this.getRsc("templates",t)||lt(t))}function m(t,e,n,r){var o,a,s,p,d,c="number"==typeof n&&e.tmpl.bnds[n-1];if(void 0===r&&c&&c._lr&&(r=""),void 0!==r?n=r={props:{},args:[r]}:c&&(n=c(e.data,e,ft)),c=c._bd&&c,t||c){if(a=e._lc,o=a&&a.tag,n.view=e,!o){if(o=l(new ft._tg,{_:{bnd:c,unlinked:!0,lt:n.lt},inline:!a,tagName:":",convert:t,onArrayChange:!0,flow:!0,tagCtx:n,tagCtxs:[n],_is:"tag"}),p=n.args.length,p>1)for(d=o.bindTo=[];p--;)d.unshift(p);a&&(a.tag=o,o.linkCtx=a),n.ctx=Q(n.ctx,(a?a.view:e).ctx),i(o,n)}o._er=r&&s,o.ctx=n.ctx||o.ctx||{},n.ctx=void 0,s=o.cvtArgs()[0],o._er=r&&s}else s=n.args[0];return s=c&&e._.onRender?e._.onRender(s,e,o):s,void 0!=s?s:""}function _(t,e){var n,r,i,o,a,s,p,l=this;if(l.tagName){if(s=l,l=(s.tagCtxs||[l])[t||0],!l)return}else s=l.tag;if(a=s.bindFrom,o=l.args,(p=s.convert)&&typeof p===Bt&&(p="true"===p?void 0:l.view.getRsc("converters",p)||S("Unknown converter: '"+p+"'")),p&&!e&&(o=o.slice()),a){for(i=[],n=a.length;n--;)r=a[n],i.unshift(x(l,r));e&&(o=i)}if(p){if(p=p.apply(s,i||o),void 0===p)return o;if(a=a||[0],n=a.length,pt(p)&&(p.arg0===!1||1!==n&&p.length===n&&!p.arg0)||(p=[p],a=[0],n=1),e)o=p;else for(;n--;)r=a[n],+r===r&&(o[r]=p[n])}return o}function x(t,e){return t=t[+e===e?"args":"props"],t&&t[e]}function b(t){return this.cvtArgs(t,1)}function w(t,e){var n,r,i=this;if(typeof e===Bt){for(;void 0===n&&i;)r=i.tmpl&&i.tmpl[t],n=r&&r[e],i=i.parent;return n||ot[t][e]}}function y(t,e,n,r,o,a){function s(t){var e=p[t];if(void 0!==e)for(e=pt(e)?e:[e],h=e.length;h--;)q=e[h],isNaN(parseInt(q))||(e[h]=parseInt(q));return e||[0]}e=e||it;var p,l,d,c,u,f,g,h,m,w,y,C,k,j,T,A,P,F,N,R,M,$,V,I,D,q,U,K,J,B,L=0,H="",W=e._lc||!1,Z=e.ctx,z=n||e.tmpl,G="number"==typeof r&&e.tmpl.bnds[r-1];for("tag"===t._is?(p=t,t=p.tagName,r=p.tagCtxs,d=p.template):(l=e.getRsc("tags",t)||S("Unknown tag: {{"+t+"}} "),d=l.template),void 0===a&&G&&(G._lr=l.lateRender&&G._lr!==!1||G._lr)&&(a=""),void 0!==a?(H+=a,r=a=[{props:{},args:[],params:{props:{}}}]):G&&(r=G(e.data,e,ft)),g=r.length;L<g;L++)y=r[L],P=y.tmpl,(!W||!W.tag||L&&!W.tag.inline||p._er||P&&+P===P)&&(P&&z.tmpls&&(y.tmpl=y.content=z.tmpls[P-1]),y.index=L,y.ctxPrm=v,y.render=E,y.cvtArgs=_,y.bndArgs=b,y.view=e,y.ctx=Q(Q(y.ctx,l&&l.ctx),Z)),(n=y.props.tmpl)&&(y.tmpl=e._getTmpl(n),y.content=y.content||y.tmpl),p?W&&W.fn._lr&&(F=!!p.init):(p=new l._ctr,F=!!p.init,p.parent=f=Z&&Z.tag,p.tagCtxs=r,W&&(p.inline=!1,W.tag=p),p.linkCtx=W,(p._.bnd=G||W.fn)?(p._.ths=y.params.props["this"],p._.lt=r.lt,p._.arrVws={}):p.dataBoundOnly&&S(t+" must be data-bound:\n{^{"+t+"}}")),I=p.dataMap,y.tag=p,I&&r&&(y.map=r[L].map),p.flow||(C=y.ctx=y.ctx||{},c=p.parents=C.parentTags=Z&&Q(C.parentTags,Z.parentTags)||{},f&&(c[f.tagName]=f),c[p.tagName]=C.tag=p,C.tagCtx=y);if(!(p._er=a)){for(i(p,r[0]),p.rendering={rndr:p.rendering},L=0;L<g;L++){if(y=p.tagCtx=r[L],V=y.props,p.ctx=y.ctx,!L){if(F&&(p.init(y,W,p.ctx),F=void 0),y.args.length||y.argDefault===!1||p.argDefault===!1||(y.args=M=[y.view.data],y.params.args=["#data"]),j=s("bindTo"),void 0!==p.bindTo&&(p.bindTo=j),void 0!==p.bindFrom?p.bindFrom=s("bindFrom"):p.bindTo&&(p.bindFrom=p.bindTo=j),T=p.bindFrom||j,K=j.length,U=T.length,p._.bnd&&(J=p.linkedElement)&&(p.linkedElement=J=pt(J)?J:[J],K!==J.length&&S("linkedElement not same length as bindTo")),(J=p.linkedCtxParam)&&(p.linkedCtxParam=J=pt(J)?J:[J],U!==J.length&&S("linkedCtxParam not same length as bindFrom/bindTo")),T)for(p._.fromIndex={},p._.toIndex={},m=U;m--;)for(q=T[m],h=K;h--;)q===j[h]&&(p._.fromIndex[h]=m,p._.toIndex[m]=h);W&&(W.attr=p.attr=W.attr||p.attr||W._dfAt),u=p.attr,p._.noVws=u&&u!==Jt}if(M=p.cvtArgs(L),p.linkedCtxParam)for($=p.cvtArgs(L,1),h=U,B=p.constructor.prototype.ctx;h--;)(k=p.linkedCtxParam[h])&&(q=T[h],A=$[h],y.ctx[k]=ft._cp(B&&void 0===A?B[k]:A,void 0!==A&&x(y.params,q),y.view,p._.bnd&&{tag:p,cvt:p.convert,ind:h,tagElse:L}));(N=V.dataMap||I)&&(M.length||V.dataMap)&&(R=y.map,R&&R.src===M[0]&&!o||(R&&R.src&&R.unmap(),N.map(M[0],y,R,!p._.bnd),R=y.map),M=[R.tgt]),w=void 0,p.render&&(w=p.render.apply(p,M),e.linked&&w&&!Et.test(w)&&(n={links:[]},n.render=n.fn=function(){return w},w=O(n,e.data,void 0,!0,e,void 0,void 0,p))),M.length||(M=[e]),void 0===w&&(D=M[0],p.contentCtx&&(D=p.contentCtx===!0?e:p.contentCtx(D)),w=y.render(D,!0)||(o?void 0:"")),H=H?H+(w||""):void 0!==w?""+w:void 0}p.rendering=p.rendering.rndr}return p.tagCtx=r[0],p.ctx=p.tagCtx.ctx,p._.noVws&&p.inline&&(H="text"===u?dt.html(H):""),G&&e._.onRender?e._.onRender(H,e,p):H}function C(t,e,n,r,i,o,a,s){var p,l,d,c=this,f="array"===e;c.content=s,c.views=f?[]:{},c.data=r,c.tmpl=i,d=c._={key:0,useKey:f?0:1,id:""+qt++,onRender:a,bnds:{}},c.linked=!!a,c.type=e||"top",e&&(c.cache={_ct:gt._cchCt}),n&&"top"!==n.type||((c.ctx=t||{}).root=c.data),(c.parent=n)?(c.root=n.root||c,p=n.views,l=n._,c.isTop=l.scp,c.scope=(!t.tag||t.tag===n.ctx.tag)&&!c.isTop&&n.scope||c,l.useKey?(p[d.key="_"+l.useKey++]=c,c.index=Wt,c.getIndex=u):p.length===(d.key=c.index=o)?p.push(c):p.splice(o,0,c),c.ctx=t||n.ctx):e&&(c.root=c)}function k(t){var e,n,r;for(e in Yt)n=e+"s",t[n]&&(r=t[n],t[n]={},ot[n](r,t))}function j(t,e,n){function i(){var e=this;e._={unlinked:!0},e.inline=!0,e.tagName=t}var o,a,s,p=new ft._tg;if(st(e)?e={depends:e.depends,render:e}:typeof e===Bt&&(e={template:e}),a=e.baseTag){e.flow=!!e.flow,a=typeof a===Bt?n&&n.tags[a]||ut[a]:a,a||S('baseTag: "'+e.baseTag+'" not found'),p=l(p,a);for(s in e)p[s]=r(a[s],e[s])}else p=l(p,e);return void 0!==(o=p.template)&&(p.template=typeof o===Bt?lt[o]||lt(o):o),(i.prototype=p).constructor=p._ctr=i,n&&(p._parentTmpl=n),p}function T(t){return this.base.apply(this,t)}function A(t,n,r,i){function o(n){var o,s;if(typeof n===Bt||n.nodeType>0&&(a=n)){if(!a&&(/^\.?\/[^\\:*?"<>]*$/.test(n)?(s=lt[t=t||n])?n=s:a=document.getElementById(n):"#"===n.charAt(0)&&(a=document.getElementById(n.slice(1))),!a&&e.fn&&!ft.rTmpl.test(n)))try{a=e(n,document)[0]}catch(p){}a&&("SCRIPT"!==a.tagName&&S(n+": Use script block, not "+a.tagName),i?n=a.innerHTML:(o=a.getAttribute(Qt),o&&(o!==Ht?(n=lt[o],delete lt[o]):e.fn&&(n=e.data(a)[Ht])),o&&n||(t=t||(e.fn?Ht:n),n=A(t,a.innerHTML,r,i)),n.tmplName=t=t||o,t!==Ht&&(lt[t]=n),a.setAttribute(Qt,t),e.fn&&e.data(a,Ht,n))),a=void 0}else n.fn||(n=void 0);return n}var a,s,p=n=n||"";if(ft._html=dt.html,0===i&&(i=void 0,p=o(p)),i=i||(n.markup?n.bnds?l({},n):n:{}),i.tmplName=i.tmplName||t||"unnamed",r&&(i._parentTmpl=r),!p&&n.markup&&(p=o(n.markup))&&p.fn&&(p=p.markup),void 0!==p)return p.render||n.render?p.tmpls&&(s=p):(n=R(p,i),q(p.replace(Pt,"\\$&"),n)),s||(s=l(function(){return s.render.apply(s,arguments)},n),k(s)),s}function P(t,e){return st(t)?t.call(e):t}function F(t,e,n){Object.defineProperty(t,e,{value:n,configurable:!0})}function N(t,n){function r(t){d.apply(this,t)}function i(){return new r(arguments)}function o(t,e){for(var n,r,i,o,a,s=0;s<x;s++)i=f[s],n=void 0,typeof i!==Bt&&(n=i,i=n.getter,a=n.parentRef),void 0===(o=t[i])&&n&&void 0!==(r=n.defaultVal)&&(o=P(r,t)),e(o,n&&u[n.type],i,a)}function a(e){e=typeof e===Bt?JSON.parse(e):e;var n,r,i,a,l=0,d=e,c=[];if(pt(e)){for(e=e||[],n=e.length;l<n;l++)c.push(this.map(e[l]));return c._is=t,c.unmap=p,c.merge=s,c}if(e){for(o(e,function(t,e){e&&(t=e.map(t)),c.push(t)}),d=this.apply(this,c),l=x;l--;)if(i=c[l],a=f[l].parentRef,a&&i&&i.unmap)if(pt(i))for(n=i.length;n--;)F(i[n],a,d);else F(i,a,d);for(r in e)r===at||w[r]||(d[r]=e[r])}return d}function s(t,e,n){t=typeof t===Bt?JSON.parse(t):t;var r,a,s,p,l,d,c,u,f,g,h=0,m=this;if(pt(m)){for(c={},f=[],a=t.length,s=m.length;h<a;h++){for(u=t[h],d=!1,r=0;r<s&&!d;r++)c[r]||(l=m[r],v&&(c[r]=d=typeof v===Bt?u[v]&&(w[v]?l[v]():l[v])===u[v]:v(l,u)));d?(l.merge(u),f.push(l)):(f.push(g=i.map(u)),n&&F(g,n,e))}return void(b?b(m).refresh(f,!0):m.splice.apply(m,[0,m.length].concat(f)))}o(t,function(t,e,n,r){e?m[n]().merge(t,m,r):m[n]()!==t&&m[n](t)});for(p in t)p===at||w[p]||(m[p]=t[p])}function p(){function t(t){for(var e=[],n=0,r=t.length;n<r;n++)e.push(t[n].unmap());return e}var e,n,r,i,o=0,a=this;if(pt(a))return t(a);for(e={};o<x;o++)n=f[o],r=void 0,typeof n!==Bt&&(r=n,n=r.getter),i=a[n](),e[n]=r&&i&&u[r.type]?pt(i)?t(i):i.unmap():i;for(n in a)!a.hasOwnProperty(n)||"_"===n.charAt(0)&&w[n.slice(1)]||n===at||st(a[n])||(e[n]=a[n]);return e}var l,d,c,u=this,f=n.getters,g=n.extend,v=n.id,h=e.extend({_is:t||"unnamed",unmap:p,merge:s},g),m="",_="",x=f?f.length:0,b=e.observable,w={};for(r.prototype=h,l=0;l<x;l++)!function(t){t=t.getter||t,w[t]=l+1;var e="_"+t;m+=(m?",":"")+t,_+="this."+e+" = "+t+";\n",h[t]=h[t]||function(n){return arguments.length?void(b?b(this).setProperty(t,n):this[e]=n):this[e]},b&&(h[t].set=h[t].set||function(t){this[e]=t})}(f[l]);return _=new Function(m,_),d=function(){_.apply(this,arguments),(c=arguments[x+1])&&F(this,arguments[x],c)},d.prototype=h,h.constructor=d,i.map=a,i.getters=f,i.extend=g,i.id=v,i}function R(t,n){var r,i=vt._wm||{},o={tmpls:[],links:{},bnds:[],_is:"template",render:E};return n&&(o=l(o,n)),o.markup=t,o.htmlTag||(r=Rt.exec(t),o.htmlTag=r?r[1].toLowerCase():""),r=i[o.htmlTag],r&&r!==i.div&&(o.markup=e.trim(o.markup)),o}function M(t,e){function n(i,o,a){var s,p,l,d=ft.onStore[t];if(i&&typeof i===Lt&&!i.nodeType&&!i.markup&&!i.getTgt&&!("viewModel"===t&&i.getters||i.extend)){for(p in i)n(p,i[p],o);return o||ot}return i&&typeof i!==Bt&&(a=o,o=i,i=void 0),l=a?"viewModel"===t?a:a[r]=a[r]||{}:n,s=e.compile,void 0===o&&(o=s?i:l[i],i=void 0),null===o?i&&delete l[i]:(s&&(o=s.call(l,i,o,a,0)||{},o._is=t),i&&(l[i]=o)),d&&d(i,o,a,s),o}var r=t+"s";ot[r]=n}function $(t){ht[t]=ht[t]||function(e){return arguments.length?(gt[t]=e,ht):gt[t]}}function V(t){function e(e,n){this.tgt=t.getTgt(e,n),n.map=this}return st(t)&&(t={getTgt:t}),t.baseMap&&(t=l(l({},t.baseMap),t)),t.map=function(t,n){return new e(t,n)},t}function E(t,e,n,r,i,o){var a,s,p,l,d,c,u,f,g=r,v="";if(e===!0?(n=e,e=void 0):typeof e!==Lt&&(e=void 0),(p=this.tag)?(d=this,g=g||d.view,l=g._getTmpl(p.template||d.tmpl),arguments.length||(t=p.contentCtx&&st(p.contentCtx)?t=p.contentCtx(t):g)):l=this,l){if(!r&&t&&"view"===t._is&&(g=t),g&&t===g&&(t=g.data),c=!g,Ct=Ct||c,c&&((e=e||{}).root=t),!Ct||vt.useViews||l.useViews||g&&g!==it)v=O(l,t,e,n,g,i,o,p);else{if(g?(u=g.data,f=g.index,g.index=Wt):(g=it,u=g.data,g.data=t,g.ctx=e),pt(t)&&!n)for(a=0,s=t.length;a<s;a++)g.index=a,g.data=t[a],v+=l.fn(t[a],g,ft);else g.data=t,v+=l.fn(t,g,ft);g.data=u,g.index=f}c&&(Ct=void 0)}return v}function O(t,e,n,r,i,o,a,s){var p,d,c,u,f,g,v,h,m,_,x,b,w,y="";if(s&&(m=s.tagName,b=s.tagCtx,n=n?Q(n,s.ctx):s.ctx,t===i.content?v=t!==i.ctx._wrp?i.ctx._wrp:void 0:t!==b.content?t===s.template?(v=b.tmpl,n._wrp=b.content):v=b.content||i.content:v=i.content,b.props.link===!1&&(n=n||{},n.link=!1)),i&&(a=a||i._.onRender,w=n&&n.link===!1,w&&i._.nl&&(a=void 0),n=Q(n,i.ctx),b=!s&&i.tag?i.tag.tagCtxs[i.tagElse]:b),(_=b&&b.props.itemVar)&&("~"!==_[0]&&D("Use itemVar='~myItem'"),_=_.slice(1)),o===!0&&(g=!0,o=0),a&&s&&s._.noVws&&(a=void 0),h=a,a===!0&&(h=void 0,a=i._.onRender),n=t.helpers?Q(t.helpers,n):n,x=n,pt(e)&&!r)for(c=g?i:void 0!==o&&i||new C(n,"array",i,e,t,o,a,v),c._.nl=w,i&&i._.useKey&&(c._.bnd=!s||s._.bnd&&s,c.tag=s),p=0,d=e.length;p<d;p++)u=new C(x,"item",c,e[p],t,(o||0)+p,a,c.content),_&&((u.ctx=l({},x))[_]=ft._cp(e[p],"#data",u)),f=t.fn(e[p],u,ft),y+=c._.onRender?c._.onRender(f,u):f;else c=g?i:new C(x,m||"data",i,e,t,o,a,v),_&&((c.ctx=l({},x))[_]=ft._cp(e,"#data",c)),c.tag=s,c._.nl=w,y+=t.fn(e,c,ft);return s&&(c.tagElse=b.index,b.contentView=c),h?h(y,c):y}function I(t,e,n){var r=void 0!==n?st(n)?n.call(e.data,t,e):n||"":"{Error: "+(t.message||t)+"}";return gt.onError&&void 0!==(n=gt.onError.call(e.data,t,n&&r,e))&&(r=n),e&&!e._lc?dt.html(r):r}function S(t){throw new ft.Err(t)}function D(t){S("Syntax error\n"+t)}function q(t,e,n,r,i){function o(e){e-=v,e&&m.push(t.substr(v,e).replace(Tt,"\\n"))}function a(e,n){e&&(e+="}}",D((n?"{{"+n+"}} block has {{/"+e+" without {{"+e:"Unmatched or missing {{/"+e)+", in template:\n"+t))}function s(s,p,l,u,g,x,b,w,y,C,k,j){(b&&p||y&&!l||w&&":"===w.slice(-1)||C)&&D(s),x&&(g=":",u=Jt),y=y||n&&!i;var T,A,P,F=(p||n)&&[[]],N="",R="",M="",$="",V="",E="",O="",I="",S=!y&&!g;l=l||(w=w||"#data",g),o(j),v=j+s.length,b?f&&m.push(["*","\n"+w.replace(/^:/,"ret+= ").replace(At,"$1")+";\n"]):l?("else"===l&&(Nt.test(w)&&D('For "{{else if expr}}" use "{{else expr}}"'),F=_[9]&&[[]],_[10]=t.substring(_[10],j),A=_[11]||_[0]||D("Mismatched: "+s),_=h.pop(),m=_[2],S=!0),w&&B(w.replace(Tt," "),F,e,n).replace(Ft,function(t,e,n,r,i,o,a,s){return"this:"===r&&(o="undefined"),s&&(P=P||"@"===s[0]),r="'"+i+"':",a?(R+=n+o+",",$+="'"+s+"',"):n?(M+=r+"j._cp("+o+',"'+s+'",view),',E+=r+"'"+s+"',"):e?O+=o:("trigger"===i&&(I+=o),"lateRender"===i&&(T="false"!==s),N+=r+o+",",V+=r+"'"+s+"',",c=c||Vt.test(i)),""}).slice(0,-1),F&&F[0]&&F.pop(),d=[l,u||!!r||c||"",S&&[],K($||(":"===l?"'#data',":""),V,E),K(R||(":"===l?"data,":""),N,M),O,I,T,P,F||0],m.push(d),S&&(h.push(_),_=d,_[10]=v,_[11]=A)):k&&(a(k!==_[0]&&k!==_[11]&&k,_[0]),_[10]=t.substring(_[10],j),_=h.pop()),a(!_&&k),m=_[2]}var p,l,d,c,u,f=gt.allowCode||e&&e.allowCode||ht.allowCode===!0,g=[],v=0,h=[],m=g,_=[,,g];if(f&&e._is&&(e.allowCode=f),n&&(void 0!==r&&(t=t.slice(0,-r.length-2)+xt),t=mt+t+bt),a(h[0]&&h[0][2].pop()[0]),t.replace(rt,s),o(t.length),(v=g[g.length-1])&&a(typeof v!==Bt&&+v[10]===v[10]&&v[0]),n){for(l=L(g,t,n),u=[],p=g.length;p--;)u.unshift(g[p][9]);U(l,u)}else l=L(g,e);return l}function U(t,e){var n,r,i=0,o=e.length;for(t.deps=[],t.paths=[];i<o;i++){t.paths.push(r=e[i]);for(n in r)"_jsvto"!==n&&r.hasOwnProperty(n)&&r[n].length&&!r[n].skp&&(t.deps=t.deps.concat(r[n]))}}function K(t,e,n){return[t.slice(0,-1),e.slice(0,-1),n.slice(0,-1)]}function J(t,e){return"\n\tparams:{args:["+t[0]+"],\n\tprops:{"+t[1]+"}"+(t[2]?",\n\tctx:{"+t[2]+"}":"")+"},\n\targs:["+e[0]+"],\n\tprops:{"+e[1]+"}"+(e[2]?",\n\tctx:{"+e[2]+"}":"")}function B(t,n,r,i){function o(r,o,l,T,A,P,F,N,R,M,$,V,E,O,I,S,q,U,K,J,B){function L(t,e,r,o,p,l,d,c){if(z="."===r,r&&(A=A.slice(e.length),/^\.?constructor$/.test(c||A)&&D(t),z||(t=(M?(i?"":"(ltOb.lt=ltOb.lt||")+"(ob=":"")+(o?'view.ctxPrm("'+o+'")':p?"view":"data")+(M?")===undefined"+(i?"":")")+'?"":view._getOb(ob,"':"")+(c?(l?"."+l:o?"":p?"":"."+r)+(d||""):(c=o?"":p?l||"":r,"")),t+=c?"."+c:"",t=e+("view.data"===t.slice(0,9)?t.slice(5):t)+(M?(i?'"':'",ltOb')+($?",1)":")"):"")),u)){if(H="_linkTo"===a?s=n._jsvto=n._jsvto||[]:f.bd,W=z&&H[H.length-1]){if(W._cpfn){for(;W.sb;)W=W.sb;W.prm&&(W.bnd&&(A="^"+A.slice(1)),W.sb=A,W.bnd=W.bnd||"^"===A[0])}}else H.push(A);$&&!z&&(y[m]=Y,C[m]=k[m].length)}return t}T&&!N&&(A=T+A),P=P||"",E=E||"",l=l||o||E,A=A||R,M&&(M=!/\)|]/.test(B[J-1]))&&(A=A.slice(1).split(".").join("^")),$=$||U||"";var Q,H,W,Z,z,G,X,Y=J;if(!c&&!d){if(F&&D(t),q&&u){if(Q=y[m-1],B.length-1>Y-(Q||0)){if(Q=e.trim(B.slice(Q,Y+r.length)),H=s||g[m-1].bd,W=H[H.length-1],W&&W.prm){for(;W.sb&&W.sb.prm;)W=W.sb;Z=W.sb={path:W.sb,bnd:W.bnd}}else H.push(Z={path:H.pop()});W&&W.sb===Z&&(k[m]=k[m-1].slice(W._cpPthSt)+k[m],k[m-1]=k[m-1].slice(0,W._cpPthSt)),Z._cpPthSt=C[m-1],Z._cpKey=Q,k[m]+=B.slice(j,J),j=J,Z._cpfn=Zt[Q]=Zt[Q]||new Function("data,view,j","//"+Q+"\nvar v;\nreturn ((v="+k[m]+("]"===S?")]":S)+")!=null?v:null);"),k[m-1]+=w[h]&&vt.cache?'view.getCache("'+Q.replace(Pt,"\\$&")+'"':k[m],Z.prm=f.bd,Z.bnd=Z.bnd||Z.path&&Z.path.indexOf("^")>=0}k[m]=""}"["===$&&($="[j._sq("),"["===l&&(l="[j._sq(")}return X=c?(c=!O,c?r:E+'"'):d?(d=!I,d?r:E+'"'):(l?(b[++h]=!0,_[h]=0,u&&(y[m++]=Y++,f=g[m]={bd:[]},k[m]="",C[m]=1),l):"")+(K?h?"":(v=B.slice(v,Y),(a?(a=p=s=!1,"\b"):"\b,")+v+(v=Y+r.length,u&&n.push(f.bd=[]),"\b")):N?(m&&D(t),u&&n.pop(),a="_"+A,p=T,v=Y+r.length,u&&(u=f.bd=n[a]=[],u.skp=!T),A+":"):A?A.split("^").join(".").replace(ft.rPath,L)+($||P):P?P:S?"]"===S?")]":")":V?(w[h]||D(t),","):o?"":(c=O,d=I,'"')),c||d||S&&(w[h]=!1,h--),u&&(c||d||(S&&(b[h+1]&&(f=g[--m],b[h+1]=!1),x=_[h+1]),$&&(_[h+1]=k[m].length+(l?1:0),(A||S)&&(f=g[++m]={bd:[]},b[h+1]=!0))),k[m]=(k[m]||"")+B.slice(j,J),j=J+r.length,c||d||((G=l&&b[h+1])&&(k[m-1]+=l,C[m-1]++),"("===$&&z&&!Z&&(k[m]=k[m-1].slice(x)+k[m],k[m-1]=k[m-1].slice(0,x))),k[m]+=G?X.slice(1):X),c||d||!$||(h++,A&&"("===$&&(w[h]=!0)),c||d||!U||(u&&(k[m]+=$),X+=$),X}var a,s,p,l,d,c,u=n&&n[0],f={bd:u},g={0:f},v=0,h=0,m=0,_={},x=0,b={},w={},y={},C={0:0},k={0:""},j=0;return"@"===t[0]&&(t=t.replace(Dt,".")),l=(t+(r?" ":"")).replace(ft.rPrm,o),u&&(l=k[0]),!h&&l||D(t)}function L(t,e,n){var r,i,o,a,s,p,l,d,c,u,f,g,v,h,m,_,x,b,w,y,C,k,j,T,A,P,F,N,M,$,V,E,O,I=0,S=vt.useViews||e.useViews||e.tags||e.templates||e.helpers||e.converters,q="",K={},B=t.length;for(typeof e===Bt?(b=n?'data-link="'+e.replace(Tt," ").slice(1,-1)+'"':e,e=0):(b=e.tmplName||"unnamed",e.allowCode&&(K.allowCode=!0),e.debug&&(K.debug=!0),f=e.bnds,x=e.tmpls),r=0;r<B;r++)if(i=t[r],typeof i===Bt)q+='+"'+i+'"';else if(o=i[0],"*"===o)q+=";\n"+i[1]+"\nret=ret";else{if(a=i[1],C=!n&&i[2],s=J(i[3],v=i[4]),$=i[6],V=i[7],i[8]?(E="\nvar ob,ltOb={},ctxs=",O=";\nctxs.lt=ltOb.lt;\nreturn ctxs;"):(E="\nreturn ",O=""),k=i[10]&&i[10].replace(At,"$1"),(A="else"===o)?g&&g.push(i[9]):(N=i[5]||gt.debugMode!==!1&&"undefined",f&&(g=i[9])&&(g=[g],I=f.push(1))),S=S||v[1]||v[2]||g||/view.(?!index)/.test(v[0]),(P=":"===o)?a&&(o=a===Jt?">":a+o):(C&&(w=R(k,K),w.tmplName=b+"/"+o,w.useViews=w.useViews||S,L(C,w),S=w.useViews,x.push(w)),A||(y=o,S=S||o&&(!ut[o]||!ut[o].flow),T=q,q=""),j=t[r+1],j=j&&"else"===j[0]),M=N?";\ntry{\nret+=":"\n+",h="",m="",P&&(g||$||a&&a!==Jt||V)){if(F=new Function("data,view,j","// "+b+" "+ ++I+" "+o+E+"{"+s+"};"+O),F._er=N,F._tag=o,F._bd=!!g,F._lr=V,n)return F;U(F,g),_='c("'+a+'",view,',u=!0,h=_+I+",",m=")"}if(q+=P?(n?(N?"try{\n":"")+"return ":M)+(u?(u=void 0,S=c=!0,_+(F?(f[I-1]=F,I):"{"+s+"}")+")"):">"===o?(l=!0,"h("+v[0]+")"):(d=!0,"((v="+v[0]+")!=null?v:"+(n?"null)":'"")'))):(p=!0,"\n{view:view,content:false,tmpl:"+(C?x.length:"false")+","+s+"},"),y&&!j){if(q="["+q.slice(0,-1)+"]",_='t("'+y+'",view,this,',n||g){if(q=new Function("data,view,j"," // "+b+" "+I+" "+y+E+q+O),q._er=N,q._tag=y,g&&U(f[I-1]=q,g),q._lr=V,n)return q;h=_+I+",undefined,",m=")"}q=T+M+_+(g&&I||q)+")",g=0,y=0}N&&!j&&(S=!0,q+=";\n}catch(e){ret"+(n?"urn ":"+=")+h+"j._err(e,view,"+N+")"+m+";}"+(n?"":"\nret=ret"))}q="// "+b+(K.debug?"\ndebugger;":"")+"\nvar v"+(p?",t=j._tag":"")+(c?",c=j._cnvt":"")+(l?",h=j._html":"")+(n?(i[8]?", ob":"")+";\n":',ret=""')+q+(n?"\n":";\nreturn ret;");try{q=new Function("data,view,j",q)}catch(Q){D("Compiled template code:\n\n"+q+'\n: "'+(Q.message||Q)+'"')}return e&&(e.fn=q,e.useViews=!!S),q}function Q(t,e){return t&&t!==e?e?l(l({},e),t):t:e&&l({},e)}function H(t,n){var r,i,o=n.map,a=o&&o.propsArr;if(!a){if(a=[],typeof t===Lt||st(t))for(r in t)i=t[r],r===at||!t.hasOwnProperty(r)||n.props.noFunctions&&e.isFunction(i)||a.push({key:r,prop:i});o&&(o.propsArr=o.options&&a)}return W(a,n)}function W(t,n){var r,i,o,a=n.tag,s=n.props,p=n.params.props,l=s.filter,d=s.sort,c=d===!0,u=parseInt(s.step),f=s.reverse?-1:1;if(!pt(t))return t;if(c||d&&typeof d===Bt?(r=t.map(function(t,e){return t=c?t:g(t,d),{i:e,v:typeof t===Bt?t.toLowerCase():t}}),r.sort(function(t,e){return t.v>e.v?f:t.v<e.v?-f:0}),t=r.map(function(e){return t[e.i]})):(d||f<0)&&!a.dataMap&&(t=t.slice()),st(d)&&(t=t.sort(function(){return d.apply(n,arguments)})),f<0&&(!d||st(d))&&(t=t.reverse()),t.filter&&l&&(t=t.filter(l,n),n.tag.onFilter&&n.tag.onFilter(n)),p.sorted&&(r=d||f<0?t:t.slice(),a.sorted?e.observable(a.sorted).refresh(r):n.map.sorted=r),i=s.start,o=s.end,(p.start&&void 0===i||p.end&&void 0===o)&&(i=o=0),isNaN(i)&&isNaN(o)||(i=+i||0,o=void 0===o||o>t.length?t.length:+o,t=t.slice(i,o)),u>1){for(i=0,o=t.length,r=[];i<o;i+=u)r.push(t[i]);t=r}return p.paged&&a.paged&&$observable(a.paged).refresh(t),t}function Z(t,n,r){var i=this.jquery&&(this[0]||S("Unknown template")),o=i.getAttribute(Qt);return E.call(o&&e.data(i)[Ht]||lt(i),t,n,r)}function z(t){return Ut[t]||(Ut[t]="&#"+t.charCodeAt(0)+";")}function G(t,e){return Kt[e]||""}function X(t){return void 0!=t?$t.test(t)&&(""+t).replace(Ot,z)||t:""}function Y(t){return typeof t===Bt?t.replace(It,z):t}function tt(t){return typeof t===Bt?t.replace(St,G):t}var et=e===!1;e=e&&e.fn?e:t.jQuery;var nt,rt,it,ot,at,st,pt,lt,dt,ct,ut,ft,gt,vt,ht,mt,_t,xt,bt,wt,yt,Ct,kt="v1.0.13",jt="_ocp",Tt=/[ \t]*(\r\n|\n|\r)/g,At=/\\(['"\\])/g,Pt=/['"\\]/g,Ft=/(?:\x08|^)(onerror:)?(?:(~?)(([\w$.]+):)?([^\x08]+))\x08(,)?([^\x08]+)/gi,Nt=/^if\s/,Rt=/<(\w+)[>\s]/,Mt=/[\x00`><"'&=]/g,$t=/[\x00`><\"'&=]/,Vt=/^on[A-Z]|^convert(Back)?$/,Et=/^\#\d+_`[\s\S]*\/\d+_`$/,Ot=Mt,It=/[&<>]/g,St=/&(amp|gt|lt);/g,Dt=/\[['"]?|['"]?\]/g,qt=0,Ut={"&":"&","<":"<",">":">","\0":"&#0;","'":"'",'"':"&#34;","`":"&#96;","=":"&#61;"},Kt={amp:"&",gt:">",lt:"<"},Jt="html",Bt="string",Lt="object",Qt="data-jsv-tmpl",Ht="jsvTmpl",Wt="For #index in nested block use #getIndex().",Zt={},zt={},Gt=t.jsrender,Xt=Gt&&e&&!e.render,Yt={template:{compile:A},tag:{compile:j},viewModel:{compile:N},helper:{},converter:{}};if(ot={jsviews:kt,sub:{rPath:/^(!*?)(?:null|true|false|\d[\d.]*|([\w$]+|\.|~([\w$]+)|#(view|([\w$]+))?)([\w$.^]*?)(?:[.[^]([\w$]+)\]?)?)$/g,rPrm:/(\()(?=\s*\()|(?:([([])\s*)?(?:(\^?)(~?[\w$.^]+)?\s*((\+\+|--)|\+|-|~(?![\w$])|&&|\|\||===|!==|==|!=|<=|>=|[<>%*:?\/]|(=))\s*|(!*?(@)?[#~]?[\w$.^]+)([([])?)|(,\s*)|(?:(\()\s*)?\\?(?:(')|("))|(?:\s*(([)\]])(?=[.^]|\s*$|[^([])|[)\]])([([]?))|(\s+)/g,View:C,Err:p,tmplFn:q,parse:B,extend:l,extendCtx:Q,syntaxErr:D,onStore:{template:function(t,e){null===e?delete zt[t]:t&&(zt[t]=e)}},addSetting:$,settings:{allowCode:!1},advSet:a,_thp:i,_gm:r,_tg:function(){},_cnvt:m,_tag:y,_er:S,_err:I,_cp:o,_sq:function(t){return"constructor"===t&&D(""),t}},settings:{delimiters:d,advanced:function(t){return t?(l(vt,t),ft.advSet(),ht):vt}},map:V},(p.prototype=new Error).constructor=p,u.depends=function(){return[this.get("item"),"index"]},f.depends="index",C.prototype={get:c,getIndex:f,ctxPrm:v,getRsc:w,_getTmpl:h,_getOb:g,getCache:function(t){return gt._cchCt>this.cache._ct&&(this.cache={_ct:gt._cchCt}),void 0!==this.cache[t]?this.cache[t]:this.cache[t]=Zt[t](this.data,this,ft)},_is:"view"},ft=ot.sub,ht=ot.settings,!(Gt||e&&e.render)){for(nt in Yt)M(nt,Yt[nt]);if(dt=ot.converters,ct=ot.helpers,ut=ot.tags,ft._tg.prototype={baseApply:T,cvtArgs:_,bndArgs:b,ctxPrm:v},it=ft.topView=new C,e){if(e.fn.render=Z,at=e.expando,e.observable){if(kt!==(kt=e.views.jsviews))throw"jquery.observable.js requires jsrender.js "+kt;l(ft,e.views.sub),ot.map=e.views.map}}else e={},et&&(t.jsrender=e),e.renderFile=e.__express=e.compile=function(){throw"Node.js: use npm jsrender, or jsrender-node.js"},e.isFunction=function(t){return"function"==typeof t},e.isArray=Array.isArray||function(t){return"[object Array]"==={}.toString.call(t)},ft._jq=function(t){t!==e&&(l(t,e),e=t,e.fn.render=Z,delete e.jsrender,at=e.expando)},e.jsrender=kt;gt=ft.settings,gt.allowCode=!1,st=e.isFunction,e.render=zt,e.views=ot,e.templates=lt=ot.templates;for(yt in gt)$(yt);(ht.debugMode=function(t){return void 0===t?gt.debugMode:(gt._clFns&&gt._clFns(),gt.debugMode=t,gt.onError=typeof t===Bt?function(){return t}:st(t)?t:void 0,ht)})(!1),vt=gt.advanced={cache:!0,useViews:!1,_jsv:!1},ut({"if":{render:function(t){var e=this,n=e.tagCtx,r=e.rendering.done||!t&&(n.args.length||!n.index)?"":(e.rendering.done=!0,void(e.selected=n.index));return r},contentCtx:!0,flow:!0},"for":{sortDataMap:V(W),init:function(t,e){this.setDataMap(this.tagCtxs)},render:function(t){var e,n,r,i,o,a=this,s=a.tagCtx,p=s.argDefault===!1,l=s.props,d=p||s.args.length,c="",u=0;if(!a.rendering.done){if(e=d?t:s.view.data,p)for(p=l.reverse?"unshift":"push",i=+l.end,o=+l.step||1,e=[],r=+l.start||0;(i-r)*o>0;r+=o)e[p](r);void 0!==e&&(n=pt(e),c+=s.render(e,!d||l.noIteration),u+=n?e.length:1),(a.rendering.done=u)&&(a.selected=s.index)}return c},setDataMap:function(t){for(var e,n,r,i=this,o=t.length;o--;)e=t[o],n=e.props,r=e.params.props,e.argDefault=void 0===n.end||e.args.length>0,n.dataMap=e.argDefault!==!1&&pt(e.args[0])&&(r.sort||r.start||r.end||r.step||r.filter||r.reverse||n.sort||n.start||n.end||n.step||n.filter||n.reverse)&&i.sortDataMap},flow:!0},props:{baseTag:"for",dataMap:V(H),init:a,flow:!0},include:{flow:!0},"*":{render:o,flow:!0},":*":{render:o,flow:!0},dbg:ct.dbg=dt.dbg=s}),dt({html:X,attr:X,encode:Y,unencode:tt,url:function(t){return void 0!=t?encodeURI(""+t):null===t?t:""}})}return gt=ft.settings,pt=(e||Gt).isArray,ht.delimiters("{{","}}","^"),Xt&&Gt.views.sub._jq(e),e||Gt},window);

/*!
 * typeahead.js 0.11.1
 * https://github.com/twitter/typeahead.js
 * Copyright 2013-2015 Twitter, Inc. and other contributors; Licensed MIT
 */
!function(a,b){"function"==typeof define&&define.amd?define("bloodhound",["jquery"],function(c){return a.Bloodhound=b(c)}):"object"==typeof exports?module.exports=b(require("jquery")):a.Bloodhound=b(jQuery)}(this,function(a){var b=function(){"use strict";return{isMsie:function(){return/(msie|trident)/i.test(navigator.userAgent)?navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]:!1},isBlankString:function(a){return!a||/^\s*$/.test(a)},escapeRegExChars:function(a){return a.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(a){return"string"==typeof a},isNumber:function(a){return"number"==typeof a},isArray:a.isArray,isFunction:a.isFunction,isObject:a.isPlainObject,isUndefined:function(a){return"undefined"==typeof a},isElement:function(a){return!(!a||1!==a.nodeType)},isJQuery:function(b){return b instanceof a},toStr:function(a){return b.isUndefined(a)||null===a?"":a+""},bind:a.proxy,each:function(b,c){function d(a,b){return c(b,a)}a.each(b,d)},map:a.map,filter:a.grep,every:function(b,c){var d=!0;return b?(a.each(b,function(a,e){return(d=c.call(null,e,a,b))?void 0:!1}),!!d):d},some:function(b,c){var d=!1;return b?(a.each(b,function(a,e){return(d=c.call(null,e,a,b))?!1:void 0}),!!d):d},mixin:a.extend,identity:function(a){return a},clone:function(b){return a.extend(!0,{},b)},getIdGenerator:function(){var a=0;return function(){return a++}},templatify:function(b){function c(){return String(b)}return a.isFunction(b)?b:c},defer:function(a){setTimeout(a,0)},debounce:function(a,b,c){var d,e;return function(){var f,g,h=this,i=arguments;return f=function(){d=null,c||(e=a.apply(h,i))},g=c&&!d,clearTimeout(d),d=setTimeout(f,b),g&&(e=a.apply(h,i)),e}},throttle:function(a,b){var c,d,e,f,g,h;return g=0,h=function(){g=new Date,e=null,f=a.apply(c,d)},function(){var i=new Date,j=b-(i-g);return c=this,d=arguments,0>=j?(clearTimeout(e),e=null,g=i,f=a.apply(c,d)):e||(e=setTimeout(h,j)),f}},stringify:function(a){return b.isString(a)?a:JSON.stringify(a)},noop:function(){}}}(),c="0.11.1",d=function(){"use strict";function a(a){return a=b.toStr(a),a?a.split(/\s+/):[]}function c(a){return a=b.toStr(a),a?a.split(/\W+/):[]}function d(a){return function(c){return c=b.isArray(c)?c:[].slice.call(arguments,0),function(d){var e=[];return b.each(c,function(c){e=e.concat(a(b.toStr(d[c])))}),e}}}return{nonword:c,whitespace:a,obj:{nonword:d(c),whitespace:d(a)}}}(),e=function(){"use strict";function c(c){this.maxSize=b.isNumber(c)?c:100,this.reset(),this.maxSize<=0&&(this.set=this.get=a.noop)}function d(){this.head=this.tail=null}function e(a,b){this.key=a,this.val=b,this.prev=this.next=null}return b.mixin(c.prototype,{set:function(a,b){var c,d=this.list.tail;this.size>=this.maxSize&&(this.list.remove(d),delete this.hash[d.key],this.size--),(c=this.hash[a])?(c.val=b,this.list.moveToFront(c)):(c=new e(a,b),this.list.add(c),this.hash[a]=c,this.size++)},get:function(a){var b=this.hash[a];return b?(this.list.moveToFront(b),b.val):void 0},reset:function(){this.size=0,this.hash={},this.list=new d}}),b.mixin(d.prototype,{add:function(a){this.head&&(a.next=this.head,this.head.prev=a),this.head=a,this.tail=this.tail||a},remove:function(a){a.prev?a.prev.next=a.next:this.head=a.next,a.next?a.next.prev=a.prev:this.tail=a.prev},moveToFront:function(a){this.remove(a),this.add(a)}}),c}(),f=function(){"use strict";function c(a,c){this.prefix=["__",a,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+b.escapeRegExChars(this.prefix)),this.ls=c||h,!this.ls&&this._noop()}function d(){return(new Date).getTime()}function e(a){return JSON.stringify(b.isUndefined(a)?null:a)}function f(b){return a.parseJSON(b)}function g(a){var b,c,d=[],e=h.length;for(b=0;e>b;b++)(c=h.key(b)).match(a)&&d.push(c.replace(a,""));return d}var h;try{h=window.localStorage,h.setItem("~~~","!"),h.removeItem("~~~")}catch(i){h=null}return b.mixin(c.prototype,{_prefix:function(a){return this.prefix+a},_ttlKey:function(a){return this._prefix(a)+this.ttlKey},_noop:function(){this.get=this.set=this.remove=this.clear=this.isExpired=b.noop},_safeSet:function(a,b){try{this.ls.setItem(a,b)}catch(c){"QuotaExceededError"===c.name&&(this.clear(),this._noop())}},get:function(a){return this.isExpired(a)&&this.remove(a),f(this.ls.getItem(this._prefix(a)))},set:function(a,c,f){return b.isNumber(f)?this._safeSet(this._ttlKey(a),e(d()+f)):this.ls.removeItem(this._ttlKey(a)),this._safeSet(this._prefix(a),e(c))},remove:function(a){return this.ls.removeItem(this._ttlKey(a)),this.ls.removeItem(this._prefix(a)),this},clear:function(){var a,b=g(this.keyMatcher);for(a=b.length;a--;)this.remove(b[a]);return this},isExpired:function(a){var c=f(this.ls.getItem(this._ttlKey(a)));return b.isNumber(c)&&d()>c?!0:!1}}),c}(),g=function(){"use strict";function c(a){a=a||{},this.cancelled=!1,this.lastReq=null,this._send=a.transport,this._get=a.limiter?a.limiter(this._get):this._get,this._cache=a.cache===!1?new e(0):h}var d=0,f={},g=6,h=new e(10);return c.setMaxPendingRequests=function(a){g=a},c.resetCache=function(){h.reset()},b.mixin(c.prototype,{_fingerprint:function(b){return b=b||{},b.url+b.type+a.param(b.data||{})},_get:function(a,b){function c(a){b(null,a),k._cache.set(i,a)}function e(){b(!0)}function h(){d--,delete f[i],k.onDeckRequestArgs&&(k._get.apply(k,k.onDeckRequestArgs),k.onDeckRequestArgs=null)}var i,j,k=this;i=this._fingerprint(a),this.cancelled||i!==this.lastReq||((j=f[i])?j.done(c).fail(e):g>d?(d++,f[i]=this._send(a).done(c).fail(e).always(h)):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(c,d){var e,f;d=d||a.noop,c=b.isString(c)?{url:c}:c||{},f=this._fingerprint(c),this.cancelled=!1,this.lastReq=f,(e=this._cache.get(f))?d(null,e):this._get(c,d)},cancel:function(){this.cancelled=!0}}),c}(),h=window.SearchIndex=function(){"use strict";function c(c){c=c||{},c.datumTokenizer&&c.queryTokenizer||a.error("datumTokenizer and queryTokenizer are both required"),this.identify=c.identify||b.stringify,this.datumTokenizer=c.datumTokenizer,this.queryTokenizer=c.queryTokenizer,this.reset()}function d(a){return a=b.filter(a,function(a){return!!a}),a=b.map(a,function(a){return a.toLowerCase()})}function e(){var a={};return a[i]=[],a[h]={},a}function f(a){for(var b={},c=[],d=0,e=a.length;e>d;d++)b[a[d]]||(b[a[d]]=!0,c.push(a[d]));return c}function g(a,b){var c=0,d=0,e=[];a=a.sort(),b=b.sort();for(var f=a.length,g=b.length;f>c&&g>d;)a[c]<b[d]?c++:a[c]>b[d]?d++:(e.push(a[c]),c++,d++);return e}var h="c",i="i";return b.mixin(c.prototype,{bootstrap:function(a){this.datums=a.datums,this.trie=a.trie},add:function(a){var c=this;a=b.isArray(a)?a:[a],b.each(a,function(a){var f,g;c.datums[f=c.identify(a)]=a,g=d(c.datumTokenizer(a)),b.each(g,function(a){var b,d,g;for(b=c.trie,d=a.split("");g=d.shift();)b=b[h][g]||(b[h][g]=e()),b[i].push(f)})})},get:function(a){var c=this;return b.map(a,function(a){return c.datums[a]})},search:function(a){var c,e,j=this;return c=d(this.queryTokenizer(a)),b.each(c,function(a){var b,c,d,f;if(e&&0===e.length)return!1;for(b=j.trie,c=a.split("");b&&(d=c.shift());)b=b[h][d];return b&&0===c.length?(f=b[i].slice(0),void(e=e?g(e,f):f)):(e=[],!1)}),e?b.map(f(e),function(a){return j.datums[a]}):[]},all:function(){var a=[];for(var b in this.datums)a.push(this.datums[b]);return a},reset:function(){this.datums={},this.trie=e()},serialize:function(){return{datums:this.datums,trie:this.trie}}}),c}(),i=function(){"use strict";function a(a){this.url=a.url,this.ttl=a.ttl,this.cache=a.cache,this.prepare=a.prepare,this.transform=a.transform,this.transport=a.transport,this.thumbprint=a.thumbprint,this.storage=new f(a.cacheKey)}var c;return c={data:"data",protocol:"protocol",thumbprint:"thumbprint"},b.mixin(a.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},store:function(a){this.cache&&(this.storage.set(c.data,a,this.ttl),this.storage.set(c.protocol,location.protocol,this.ttl),this.storage.set(c.thumbprint,this.thumbprint,this.ttl))},fromCache:function(){var a,b={};return this.cache?(b.data=this.storage.get(c.data),b.protocol=this.storage.get(c.protocol),b.thumbprint=this.storage.get(c.thumbprint),a=b.thumbprint!==this.thumbprint||b.protocol!==location.protocol,b.data&&!a?b.data:null):null},fromNetwork:function(a){function b(){a(!0)}function c(b){a(null,e.transform(b))}var d,e=this;a&&(d=this.prepare(this._settings()),this.transport(d).fail(b).done(c))},clear:function(){return this.storage.clear(),this}}),a}(),j=function(){"use strict";function a(a){this.url=a.url,this.prepare=a.prepare,this.transform=a.transform,this.transport=new g({cache:a.cache,limiter:a.limiter,transport:a.transport})}return b.mixin(a.prototype,{_settings:function(){return{url:this.url,type:"GET",dataType:"json"}},get:function(a,b){function c(a,c){b(a?[]:e.transform(c))}var d,e=this;if(b)return a=a||"",d=this.prepare(a,this._settings()),this.transport.get(d,c)},cancelLastRequest:function(){this.transport.cancel()}}),a}(),k=function(){"use strict";function d(d){var e;return d?(e={url:null,ttl:864e5,cache:!0,cacheKey:null,thumbprint:"",prepare:b.identity,transform:b.identity,transport:null},d=b.isString(d)?{url:d}:d,d=b.mixin(e,d),!d.url&&a.error("prefetch requires url to be set"),d.transform=d.filter||d.transform,d.cacheKey=d.cacheKey||d.url,d.thumbprint=c+d.thumbprint,d.transport=d.transport?h(d.transport):a.ajax,d):null}function e(c){var d;if(c)return d={url:null,cache:!0,prepare:null,replace:null,wildcard:null,limiter:null,rateLimitBy:"debounce",rateLimitWait:300,transform:b.identity,transport:null},c=b.isString(c)?{url:c}:c,c=b.mixin(d,c),!c.url&&a.error("remote requires url to be set"),c.transform=c.filter||c.transform,c.prepare=f(c),c.limiter=g(c),c.transport=c.transport?h(c.transport):a.ajax,delete c.replace,delete c.wildcard,delete c.rateLimitBy,delete c.rateLimitWait,c}function f(a){function b(a,b){return b.url=f(b.url,a),b}function c(a,b){return b.url=b.url.replace(g,encodeURIComponent(a)),b}function d(a,b){return b}var e,f,g;return e=a.prepare,f=a.replace,g=a.wildcard,e?e:e=f?b:a.wildcard?c:d}function g(a){function c(a){return function(c){return b.debounce(c,a)}}function d(a){return function(c){return b.throttle(c,a)}}var e,f,g;return e=a.limiter,f=a.rateLimitBy,g=a.rateLimitWait,e||(e=/^throttle$/i.test(f)?d(g):c(g)),e}function h(c){return function(d){function e(a){b.defer(function(){g.resolve(a)})}function f(a){b.defer(function(){g.reject(a)})}var g=a.Deferred();return c(d,e,f),g}}return function(c){var f,g;return f={initialize:!0,identify:b.stringify,datumTokenizer:null,queryTokenizer:null,sufficient:5,sorter:null,local:[],prefetch:null,remote:null},c=b.mixin(f,c||{}),!c.datumTokenizer&&a.error("datumTokenizer is required"),!c.queryTokenizer&&a.error("queryTokenizer is required"),g=c.sorter,c.sorter=g?function(a){return a.sort(g)}:b.identity,c.local=b.isFunction(c.local)?c.local():c.local,c.prefetch=d(c.prefetch),c.remote=e(c.remote),c}}(),l=function(){"use strict";function c(a){a=k(a),this.sorter=a.sorter,this.identify=a.identify,this.sufficient=a.sufficient,this.local=a.local,this.remote=a.remote?new j(a.remote):null,this.prefetch=a.prefetch?new i(a.prefetch):null,this.index=new h({identify:this.identify,datumTokenizer:a.datumTokenizer,queryTokenizer:a.queryTokenizer}),a.initialize!==!1&&this.initialize()}var e;return e=window&&window.Bloodhound,c.noConflict=function(){return window&&(window.Bloodhound=e),c},c.tokenizers=d,b.mixin(c.prototype,{__ttAdapter:function(){function a(a,b,d){return c.search(a,b,d)}function b(a,b){return c.search(a,b)}var c=this;return this.remote?a:b},_loadPrefetch:function(){function b(a,b){return a?c.reject():(e.add(b),e.prefetch.store(e.index.serialize()),void c.resolve())}var c,d,e=this;return c=a.Deferred(),this.prefetch?(d=this.prefetch.fromCache())?(this.index.bootstrap(d),c.resolve()):this.prefetch.fromNetwork(b):c.resolve(),c.promise()},_initialize:function(){function a(){b.add(b.local)}var b=this;return this.clear(),(this.initPromise=this._loadPrefetch()).done(a),this.initPromise},initialize:function(a){return!this.initPromise||a?this._initialize():this.initPromise},add:function(a){return this.index.add(a),this},get:function(a){return a=b.isArray(a)?a:[].slice.call(arguments),this.index.get(a)},search:function(a,c,d){function e(a){var c=[];b.each(a,function(a){!b.some(f,function(b){return g.identify(a)===g.identify(b)})&&c.push(a)}),d&&d(c)}var f,g=this;return f=this.sorter(this.index.search(a)),c(this.remote?f.slice():f),this.remote&&f.length<this.sufficient?this.remote.get(a,e):this.remote&&this.remote.cancelLastRequest(),this},all:function(){return this.index.all()},clear:function(){return this.index.reset(),this},clearPrefetchCache:function(){return this.prefetch&&this.prefetch.clear(),this},clearRemoteCache:function(){return g.resetCache(),this},ttAdapter:function(){return this.__ttAdapter()}}),c}();return l}),function(a,b){"function"==typeof define&&define.amd?define("typeahead.js",["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(a){var b=function(){"use strict";return{isMsie:function(){return/(msie|trident)/i.test(navigator.userAgent)?navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]:!1},isBlankString:function(a){return!a||/^\s*$/.test(a)},escapeRegExChars:function(a){return a.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(a){return"string"==typeof a},isNumber:function(a){return"number"==typeof a},isArray:a.isArray,isFunction:a.isFunction,isObject:a.isPlainObject,isUndefined:function(a){return"undefined"==typeof a},isElement:function(a){return!(!a||1!==a.nodeType)},isJQuery:function(b){return b instanceof a},toStr:function(a){return b.isUndefined(a)||null===a?"":a+""},bind:a.proxy,each:function(b,c){function d(a,b){return c(b,a)}a.each(b,d)},map:a.map,filter:a.grep,every:function(b,c){var d=!0;return b?(a.each(b,function(a,e){return(d=c.call(null,e,a,b))?void 0:!1}),!!d):d},some:function(b,c){var d=!1;return b?(a.each(b,function(a,e){return(d=c.call(null,e,a,b))?!1:void 0}),!!d):d},mixin:a.extend,identity:function(a){return a},clone:function(b){return a.extend(!0,{},b)},getIdGenerator:function(){var a=0;return function(){return a++}},templatify:function(b){function c(){return String(b)}return a.isFunction(b)?b:c},defer:function(a){setTimeout(a,0)},debounce:function(a,b,c){var d,e;return function(){var f,g,h=this,i=arguments;return f=function(){d=null,c||(e=a.apply(h,i))},g=c&&!d,clearTimeout(d),d=setTimeout(f,b),g&&(e=a.apply(h,i)),e}},throttle:function(a,b){var c,d,e,f,g,h;return g=0,h=function(){g=new Date,e=null,f=a.apply(c,d)},function(){var i=new Date,j=b-(i-g);return c=this,d=arguments,0>=j?(clearTimeout(e),e=null,g=i,f=a.apply(c,d)):e||(e=setTimeout(h,j)),f}},stringify:function(a){return b.isString(a)?a:JSON.stringify(a)},noop:function(){}}}(),c=function(){"use strict";function a(a){var g,h;return h=b.mixin({},f,a),g={css:e(),classes:h,html:c(h),selectors:d(h)},{css:g.css,html:g.html,classes:g.classes,selectors:g.selectors,mixin:function(a){b.mixin(a,g)}}}function c(a){return{wrapper:'<span class="'+a.wrapper+'"></span>',menu:'<div class="'+a.menu+'"></div>'}}function d(a){var c={};return b.each(a,function(a,b){c[b]="."+a}),c}function e(){var a={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},menu:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return b.isMsie()&&b.mixin(a.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),a}var f={wrapper:"twitter-typeahead",input:"tt-input",hint:"tt-hint",menu:"tt-menu",dataset:"tt-dataset",suggestion:"tt-suggestion",selectable:"tt-selectable",empty:"tt-empty",open:"tt-open",cursor:"tt-cursor",highlight:"tt-highlight"};return a}(),d=function(){"use strict";function c(b){b&&b.el||a.error("EventBus initialized without el"),this.$el=a(b.el)}var d,e;return d="typeahead:",e={render:"rendered",cursorchange:"cursorchanged",select:"selected",autocomplete:"autocompleted"},b.mixin(c.prototype,{_trigger:function(b,c){var e;return e=a.Event(d+b),(c=c||[]).unshift(e),this.$el.trigger.apply(this.$el,c),e},before:function(a){var b,c;return b=[].slice.call(arguments,1),c=this._trigger("before"+a,b),c.isDefaultPrevented()},trigger:function(a){var b;this._trigger(a,[].slice.call(arguments,1)),(b=e[a])&&this._trigger(b,[].slice.call(arguments,1))}}),c}(),e=function(){"use strict";function a(a,b,c,d){var e;if(!c)return this;for(b=b.split(i),c=d?h(c,d):c,this._callbacks=this._callbacks||{};e=b.shift();)this._callbacks[e]=this._callbacks[e]||{sync:[],async:[]},this._callbacks[e][a].push(c);return this}function b(b,c,d){return a.call(this,"async",b,c,d)}function c(b,c,d){return a.call(this,"sync",b,c,d)}function d(a){var b;if(!this._callbacks)return this;for(a=a.split(i);b=a.shift();)delete this._callbacks[b];return this}function e(a){var b,c,d,e,g;if(!this._callbacks)return this;for(a=a.split(i),d=[].slice.call(arguments,1);(b=a.shift())&&(c=this._callbacks[b]);)e=f(c.sync,this,[b].concat(d)),g=f(c.async,this,[b].concat(d)),e()&&j(g);return this}function f(a,b,c){function d(){for(var d,e=0,f=a.length;!d&&f>e;e+=1)d=a[e].apply(b,c)===!1;return!d}return d}function g(){var a;return a=window.setImmediate?function(a){setImmediate(function(){a()})}:function(a){setTimeout(function(){a()},0)}}function h(a,b){return a.bind?a.bind(b):function(){a.apply(b,[].slice.call(arguments,0))}}var i=/\s+/,j=g();return{onSync:c,onAsync:b,off:d,trigger:e}}(),f=function(a){"use strict";function c(a,c,d){for(var e,f=[],g=0,h=a.length;h>g;g++)f.push(b.escapeRegExChars(a[g]));return e=d?"\\b("+f.join("|")+")\\b":"("+f.join("|")+")",c?new RegExp(e):new RegExp(e,"i")}var d={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(e){function f(b){var c,d,f;return(c=h.exec(b.data))&&(f=a.createElement(e.tagName),e.className&&(f.className=e.className),d=b.splitText(c.index),d.splitText(c[0].length),f.appendChild(d.cloneNode(!0)),b.parentNode.replaceChild(f,d)),!!c}function g(a,b){for(var c,d=3,e=0;e<a.childNodes.length;e++)c=a.childNodes[e],c.nodeType===d?e+=b(c)?1:0:g(c,b)}var h;e=b.mixin({},d,e),e.node&&e.pattern&&(e.pattern=b.isArray(e.pattern)?e.pattern:[e.pattern],h=c(e.pattern,e.caseSensitive,e.wordsOnly),g(e.node,f))}}(window.document),g=function(){"use strict";function c(c,e){c=c||{},c.input||a.error("input is missing"),e.mixin(this),this.$hint=a(c.hint),this.$input=a(c.input),this.query=this.$input.val(),this.queryWhenFocused=this.hasFocus()?this.query:null,this.$overflowHelper=d(this.$input),this._checkLanguageDirection(),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=b.noop)}function d(b){return a('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:b.css("font-family"),fontSize:b.css("font-size"),fontStyle:b.css("font-style"),fontVariant:b.css("font-variant"),fontWeight:b.css("font-weight"),wordSpacing:b.css("word-spacing"),letterSpacing:b.css("letter-spacing"),textIndent:b.css("text-indent"),textRendering:b.css("text-rendering"),textTransform:b.css("text-transform")}).insertAfter(b)}function f(a,b){return c.normalizeQuery(a)===c.normalizeQuery(b)}function g(a){return a.altKey||a.ctrlKey||a.metaKey||a.shiftKey}var h;return h={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},c.normalizeQuery=function(a){return b.toStr(a).replace(/^\s*/g,"").replace(/\s{2,}/g," ")},b.mixin(c.prototype,e,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.queryWhenFocused=this.query,this.trigger("focused")},_onKeydown:function(a){var b=h[a.which||a.keyCode];this._managePreventDefault(b,a),b&&this._shouldTrigger(b,a)&&this.trigger(b+"Keyed",a)},_onInput:function(){this._setQuery(this.getInputValue()),this.clearHintIfInvalid(),this._checkLanguageDirection()},_managePreventDefault:function(a,b){var c;switch(a){case"up":case"down":c=!g(b);break;default:c=!1}c&&b.preventDefault()},_shouldTrigger:function(a,b){var c;switch(a){case"tab":c=!g(b);break;default:c=!0}return c},_checkLanguageDirection:function(){var a=(this.$input.css("direction")||"ltr").toLowerCase();this.dir!==a&&(this.dir=a,this.$hint.attr("dir",a),this.trigger("langDirChanged",a))},_setQuery:function(a,b){var c,d;c=f(a,this.query),d=c?this.query.length!==a.length:!1,this.query=a,b||c?!b&&d&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},bind:function(){var a,c,d,e,f=this;return a=b.bind(this._onBlur,this),c=b.bind(this._onFocus,this),d=b.bind(this._onKeydown,this),e=b.bind(this._onInput,this),this.$input.on("blur.tt",a).on("focus.tt",c).on("keydown.tt",d),!b.isMsie()||b.isMsie()>9?this.$input.on("input.tt",e):this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",function(a){h[a.which||a.keyCode]||b.defer(b.bind(f._onInput,f,a))}),this},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getLangDir:function(){return this.dir},getQuery:function(){return this.query||""},setQuery:function(a,b){this.setInputValue(a),this._setQuery(a,b)},hasQueryChangedSinceLastFocus:function(){return this.query!==this.queryWhenFocused},getInputValue:function(){return this.$input.val()},setInputValue:function(a){this.$input.val(a),this.clearHintIfInvalid(),this._checkLanguageDirection()},resetInputValue:function(){this.setInputValue(this.query)},getHint:function(){return this.$hint.val()},setHint:function(a){this.$hint.val(a)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var a,b,c,d;a=this.getInputValue(),b=this.getHint(),c=a!==b&&0===b.indexOf(a),d=""!==a&&c&&!this.hasOverflow(),!d&&this.clearHint()},hasFocus:function(){return this.$input.is(":focus")},hasOverflow:function(){var a=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=a},isCursorAtEnd:function(){var a,c,d;return a=this.$input.val().length,c=this.$input[0].selectionStart,b.isNumber(c)?c===a:document.selection?(d=document.selection.createRange(),d.moveStart("character",-a),a===d.text.length):!0},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$overflowHelper.remove(),this.$hint=this.$input=this.$overflowHelper=a("<div>")}}),c}(),h=function(){"use strict";function c(c,e){c=c||{},c.templates=c.templates||{},c.templates.notFound=c.templates.notFound||c.templates.empty,c.source||a.error("missing source"),c.node||a.error("missing node"),c.name&&!h(c.name)&&a.error("invalid dataset name: "+c.name),e.mixin(this),this.highlight=!!c.highlight,this.name=c.name||j(),this.limit=c.limit||5,this.displayFn=d(c.display||c.displayKey),this.templates=g(c.templates,this.displayFn),this.source=c.source.__ttAdapter?c.source.__ttAdapter():c.source,this.async=b.isUndefined(c.async)?this.source.length>2:!!c.async,this._resetLastSuggestion(),this.$el=a(c.node).addClass(this.classes.dataset).addClass(this.classes.dataset+"-"+this.name)}function d(a){function c(b){return b[a]}return a=a||b.stringify,b.isFunction(a)?a:c}function g(c,d){function e(b){return a("<div>").text(d(b))}return{notFound:c.notFound&&b.templatify(c.notFound),pending:c.pending&&b.templatify(c.pending),header:c.header&&b.templatify(c.header),footer:c.footer&&b.templatify(c.footer),suggestion:c.suggestion||e}}function h(a){return/^[_a-zA-Z0-9-]+$/.test(a)}var i,j;return i={val:"tt-selectable-display",obj:"tt-selectable-object"},j=b.getIdGenerator(),c.extractData=function(b){var c=a(b);return c.data(i.obj)?{val:c.data(i.val)||"",obj:c.data(i.obj)||null}:null},b.mixin(c.prototype,e,{_overwrite:function(a,b){b=b||[],b.length?this._renderSuggestions(a,b):this.async&&this.templates.pending?this._renderPending(a):!this.async&&this.templates.notFound?this._renderNotFound(a):this._empty(),this.trigger("rendered",this.name,b,!1)},_append:function(a,b){b=b||[],b.length&&this.$lastSuggestion.length?this._appendSuggestions(a,b):b.length?this._renderSuggestions(a,b):!this.$lastSuggestion.length&&this.templates.notFound&&this._renderNotFound(a),this.trigger("rendered",this.name,b,!0)},_renderSuggestions:function(a,b){var c;c=this._getSuggestionsFragment(a,b),this.$lastSuggestion=c.children().last(),this.$el.html(c).prepend(this._getHeader(a,b)).append(this._getFooter(a,b))},_appendSuggestions:function(a,b){var c,d;c=this._getSuggestionsFragment(a,b),d=c.children().last(),this.$lastSuggestion.after(c),this.$lastSuggestion=d},_renderPending:function(a){var b=this.templates.pending;this._resetLastSuggestion(),b&&this.$el.html(b({query:a,dataset:this.name}))},_renderNotFound:function(a){var b=this.templates.notFound;this._resetLastSuggestion(),b&&this.$el.html(b({query:a,dataset:this.name}))},_empty:function(){this.$el.empty(),this._resetLastSuggestion()},_getSuggestionsFragment:function(c,d){var e,g=this;return e=document.createDocumentFragment(),b.each(d,function(b){var d,f;f=g._injectQuery(c,b),d=a(g.templates.suggestion(f)).data(i.obj,b).data(i.val,g.displayFn(b)).addClass(g.classes.suggestion+" "+g.classes.selectable),e.appendChild(d[0])}),this.highlight&&f({className:this.classes.highlight,node:e,pattern:c}),a(e)},_getFooter:function(a,b){return this.templates.footer?this.templates.footer({query:a,suggestions:b,dataset:this.name}):null},_getHeader:function(a,b){return this.templates.header?this.templates.header({query:a,suggestions:b,dataset:this.name}):null},_resetLastSuggestion:function(){this.$lastSuggestion=a()},_injectQuery:function(a,c){return b.isObject(c)?b.mixin({_query:a},c):c},update:function(b){function c(a){g||(g=!0,a=(a||[]).slice(0,e.limit),h=a.length,e._overwrite(b,a),h<e.limit&&e.async&&e.trigger("asyncRequested",b))}function d(c){c=c||[],!f&&h<e.limit&&(e.cancel=a.noop,h+=c.length,e._append(b,c.slice(0,e.limit-h)),e.async&&e.trigger("asyncReceived",b))}var e=this,f=!1,g=!1,h=0;this.cancel(),this.cancel=function(){f=!0,e.cancel=a.noop,e.async&&e.trigger("asyncCanceled",b)},this.source(b,c,d),!g&&c([])},cancel:a.noop,clear:function(){this._empty(),this.cancel(),this.trigger("cleared")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=a("<div>")}}),c}(),i=function(){"use strict";function c(c,d){function e(b){var c=f.$node.find(b.node).first();return b.node=c.length?c:a("<div>").appendTo(f.$node),new h(b,d)}var f=this;c=c||{},c.node||a.error("node is required"),d.mixin(this),this.$node=a(c.node),this.query=null,this.datasets=b.map(c.datasets,e)}return b.mixin(c.prototype,e,{_onSelectableClick:function(b){this.trigger("selectableClicked",a(b.currentTarget))},_onRendered:function(a,b,c,d){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetRendered",b,c,d)},_onCleared:function(){this.$node.toggleClass(this.classes.empty,this._allDatasetsEmpty()),this.trigger("datasetCleared")},_propagate:function(){this.trigger.apply(this,arguments)},_allDatasetsEmpty:function(){function a(a){return a.isEmpty()}return b.every(this.datasets,a)},_getSelectables:function(){return this.$node.find(this.selectors.selectable)},_removeCursor:function(){var a=this.getActiveSelectable();a&&a.removeClass(this.classes.cursor)},_ensureVisible:function(a){var b,c,d,e;b=a.position().top,c=b+a.outerHeight(!0),d=this.$node.scrollTop(),e=this.$node.height()+parseInt(this.$node.css("paddingTop"),10)+parseInt(this.$node.css("paddingBottom"),10),0>b?this.$node.scrollTop(d+b):c>e&&this.$node.scrollTop(d+(c-e))},bind:function(){var a,c=this;return a=b.bind(this._onSelectableClick,this),this.$node.on("click.tt",this.selectors.selectable,a),b.each(this.datasets,function(a){a.onSync("asyncRequested",c._propagate,c).onSync("asyncCanceled",c._propagate,c).onSync("asyncReceived",c._propagate,c).onSync("rendered",c._onRendered,c).onSync("cleared",c._onCleared,c)}),this},isOpen:function(){return this.$node.hasClass(this.classes.open)},open:function(){this.$node.addClass(this.classes.open)},close:function(){this.$node.removeClass(this.classes.open),this._removeCursor()},setLanguageDirection:function(a){this.$node.attr("dir",a)},selectableRelativeToCursor:function(a){var b,c,d,e;return c=this.getActiveSelectable(),b=this._getSelectables(),d=c?b.index(c):-1,e=d+a,e=(e+1)%(b.length+1)-1,e=-1>e?b.length-1:e,-1===e?null:b.eq(e)},setCursor:function(a){this._removeCursor(),(a=a&&a.first())&&(a.addClass(this.classes.cursor),this._ensureVisible(a))},getSelectableData:function(a){return a&&a.length?h.extractData(a):null},getActiveSelectable:function(){var a=this._getSelectables().filter(this.selectors.cursor).first();return a.length?a:null},getTopSelectable:function(){var a=this._getSelectables().first();return a.length?a:null},update:function(a){function c(b){b.update(a)}var d=a!==this.query;return d&&(this.query=a,b.each(this.datasets,c)),d},empty:function(){function a(a){a.clear()}b.each(this.datasets,a),this.query=null,this.$node.addClass(this.classes.empty)},destroy:function(){function c(a){a.destroy()}this.$node.off(".tt"),this.$node=a("<div>"),b.each(this.datasets,c)}}),c}(),j=function(){"use strict";function a(){i.apply(this,[].slice.call(arguments,0))}var c=i.prototype;return b.mixin(a.prototype,i.prototype,{open:function(){return!this._allDatasetsEmpty()&&this._show(),c.open.apply(this,[].slice.call(arguments,0))},close:function(){return this._hide(),c.close.apply(this,[].slice.call(arguments,0))},_onRendered:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),c._onRendered.apply(this,[].slice.call(arguments,0))},_onCleared:function(){return this._allDatasetsEmpty()?this._hide():this.isOpen()&&this._show(),c._onCleared.apply(this,[].slice.call(arguments,0))},setLanguageDirection:function(a){return this.$node.css("ltr"===a?this.css.ltr:this.css.rtl),c.setLanguageDirection.apply(this,[].slice.call(arguments,0))},_hide:function(){this.$node.hide()},_show:function(){this.$node.css("display","block")}}),a}(),k=function(){"use strict";function c(c,e){var f,g,h,i,j,k,l,m,n,o,p;c=c||{},c.input||a.error("missing input"),c.menu||a.error("missing menu"),c.eventBus||a.error("missing event bus"),e.mixin(this),this.eventBus=c.eventBus,this.minLength=b.isNumber(c.minLength)?c.minLength:1,this.input=c.input,this.menu=c.menu,this.enabled=!0,this.active=!1,this.input.hasFocus()&&this.activate(),this.dir=this.input.getLangDir(),this._hacks(),this.menu.bind().onSync("selectableClicked",this._onSelectableClicked,this).onSync("asyncRequested",this._onAsyncRequested,this).onSync("asyncCanceled",this._onAsyncCanceled,this).onSync("asyncReceived",this._onAsyncReceived,this).onSync("datasetRendered",this._onDatasetRendered,this).onSync("datasetCleared",this._onDatasetCleared,this),f=d(this,"activate","open","_onFocused"),g=d(this,"deactivate","_onBlurred"),h=d(this,"isActive","isOpen","_onEnterKeyed"),i=d(this,"isActive","isOpen","_onTabKeyed"),j=d(this,"isActive","_onEscKeyed"),k=d(this,"isActive","open","_onUpKeyed"),l=d(this,"isActive","open","_onDownKeyed"),m=d(this,"isActive","isOpen","_onLeftKeyed"),n=d(this,"isActive","isOpen","_onRightKeyed"),o=d(this,"_openIfActive","_onQueryChanged"),p=d(this,"_openIfActive","_onWhitespaceChanged"),this.input.bind().onSync("focused",f,this).onSync("blurred",g,this).onSync("enterKeyed",h,this).onSync("tabKeyed",i,this).onSync("escKeyed",j,this).onSync("upKeyed",k,this).onSync("downKeyed",l,this).onSync("leftKeyed",m,this).onSync("rightKeyed",n,this).onSync("queryChanged",o,this).onSync("whitespaceChanged",p,this).onSync("langDirChanged",this._onLangDirChanged,this)}function d(a){var c=[].slice.call(arguments,1);return function(){var d=[].slice.call(arguments);b.each(c,function(b){return a[b].apply(a,d)})}}return b.mixin(c.prototype,{_hacks:function(){var c,d;c=this.input.$input||a("<div>"),d=this.menu.$node||a("<div>"),c.on("blur.tt",function(a){var e,f,g;
e=document.activeElement,f=d.is(e),g=d.has(e).length>0,b.isMsie()&&(f||g)&&(a.preventDefault(),a.stopImmediatePropagation(),b.defer(function(){c.focus()}))}),d.on("mousedown.tt",function(a){a.preventDefault()})},_onSelectableClicked:function(a,b){this.select(b)},_onDatasetCleared:function(){this._updateHint()},_onDatasetRendered:function(a,b,c,d){this._updateHint(),this.eventBus.trigger("render",c,d,b)},_onAsyncRequested:function(a,b,c){this.eventBus.trigger("asyncrequest",c,b)},_onAsyncCanceled:function(a,b,c){this.eventBus.trigger("asynccancel",c,b)},_onAsyncReceived:function(a,b,c){this.eventBus.trigger("asyncreceive",c,b)},_onFocused:function(){this._minLengthMet()&&this.menu.update(this.input.getQuery())},_onBlurred:function(){this.input.hasQueryChangedSinceLastFocus()&&this.eventBus.trigger("change",this.input.getQuery())},_onEnterKeyed:function(a,b){var c;(c=this.menu.getActiveSelectable())&&this.select(c)&&b.preventDefault()},_onTabKeyed:function(a,b){var c;(c=this.menu.getActiveSelectable())?this.select(c)&&b.preventDefault():(c=this.menu.getTopSelectable())&&this.autocomplete(c)&&b.preventDefault()},_onEscKeyed:function(){this.close()},_onUpKeyed:function(){this.moveCursor(-1)},_onDownKeyed:function(){this.moveCursor(1)},_onLeftKeyed:function(){"rtl"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onRightKeyed:function(){"ltr"===this.dir&&this.input.isCursorAtEnd()&&this.autocomplete(this.menu.getTopSelectable())},_onQueryChanged:function(a,b){this._minLengthMet(b)?this.menu.update(b):this.menu.empty()},_onWhitespaceChanged:function(){this._updateHint()},_onLangDirChanged:function(a,b){this.dir!==b&&(this.dir=b,this.menu.setLanguageDirection(b))},_openIfActive:function(){this.isActive()&&this.open()},_minLengthMet:function(a){return a=b.isString(a)?a:this.input.getQuery()||"",a.length>=this.minLength},_updateHint:function(){var a,c,d,e,f,h,i;a=this.menu.getTopSelectable(),c=this.menu.getSelectableData(a),d=this.input.getInputValue(),!c||b.isBlankString(d)||this.input.hasOverflow()?this.input.clearHint():(e=g.normalizeQuery(d),f=b.escapeRegExChars(e),h=new RegExp("^(?:"+f+")(.+$)","i"),i=h.exec(c.val),i&&this.input.setHint(d+i[1]))},isEnabled:function(){return this.enabled},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},isActive:function(){return this.active},activate:function(){return this.isActive()?!0:!this.isEnabled()||this.eventBus.before("active")?!1:(this.active=!0,this.eventBus.trigger("active"),!0)},deactivate:function(){return this.isActive()?this.eventBus.before("idle")?!1:(this.active=!1,this.close(),this.eventBus.trigger("idle"),!0):!0},isOpen:function(){return this.menu.isOpen()},open:function(){return this.isOpen()||this.eventBus.before("open")||(this.menu.open(),this._updateHint(),this.eventBus.trigger("open")),this.isOpen()},close:function(){return this.isOpen()&&!this.eventBus.before("close")&&(this.menu.close(),this.input.clearHint(),this.input.resetInputValue(),this.eventBus.trigger("close")),!this.isOpen()},setVal:function(a){this.input.setQuery(b.toStr(a))},getVal:function(){return this.input.getQuery()},select:function(a){var b=this.menu.getSelectableData(a);return b&&!this.eventBus.before("select",b.obj)?(this.input.setQuery(b.val,!0),this.eventBus.trigger("select",b.obj),this.close(),!0):!1},autocomplete:function(a){var b,c,d;return b=this.input.getQuery(),c=this.menu.getSelectableData(a),d=c&&b!==c.val,d&&!this.eventBus.before("autocomplete",c.obj)?(this.input.setQuery(c.val),this.eventBus.trigger("autocomplete",c.obj),!0):!1},moveCursor:function(a){var b,c,d,e,f;return b=this.input.getQuery(),c=this.menu.selectableRelativeToCursor(a),d=this.menu.getSelectableData(c),e=d?d.obj:null,f=this._minLengthMet()&&this.menu.update(b),f||this.eventBus.before("cursorchange",e)?!1:(this.menu.setCursor(c),d?this.input.setInputValue(d.val):(this.input.resetInputValue(),this._updateHint()),this.eventBus.trigger("cursorchange",e),!0)},destroy:function(){this.input.destroy(),this.menu.destroy()}}),c}();!function(){"use strict";function e(b,c){b.each(function(){var b,d=a(this);(b=d.data(p.typeahead))&&c(b,d)})}function f(a,b){return a.clone().addClass(b.classes.hint).removeData().css(b.css.hint).css(l(a)).prop("readonly",!0).removeAttr("id name placeholder required").attr({autocomplete:"off",spellcheck:"false",tabindex:-1})}function h(a,b){a.data(p.attrs,{dir:a.attr("dir"),autocomplete:a.attr("autocomplete"),spellcheck:a.attr("spellcheck"),style:a.attr("style")}),a.addClass(b.classes.input).attr({autocomplete:"off",spellcheck:!1});try{!a.attr("dir")&&a.attr("dir","auto")}catch(c){}return a}function l(a){return{backgroundAttachment:a.css("background-attachment"),backgroundClip:a.css("background-clip"),backgroundColor:a.css("background-color"),backgroundImage:a.css("background-image"),backgroundOrigin:a.css("background-origin"),backgroundPosition:a.css("background-position"),backgroundRepeat:a.css("background-repeat"),backgroundSize:a.css("background-size")}}function m(a){var c,d;c=a.data(p.www),d=a.parent().filter(c.selectors.wrapper),b.each(a.data(p.attrs),function(c,d){b.isUndefined(c)?a.removeAttr(d):a.attr(d,c)}),a.removeData(p.typeahead).removeData(p.www).removeData(p.attr).removeClass(c.classes.input),d.length&&(a.detach().insertAfter(d),d.remove())}function n(c){var d,e;return d=b.isJQuery(c)||b.isElement(c),e=d?a(c).first():[],e.length?e:null}var o,p,q;o=a.fn.typeahead,p={www:"tt-www",attrs:"tt-attrs",typeahead:"tt-typeahead"},q={initialize:function(e,l){function m(){var c,m,q,r,s,t,u,v,w,x,y;b.each(l,function(a){a.highlight=!!e.highlight}),c=a(this),m=a(o.html.wrapper),q=n(e.hint),r=n(e.menu),s=e.hint!==!1&&!q,t=e.menu!==!1&&!r,s&&(q=f(c,o)),t&&(r=a(o.html.menu).css(o.css.menu)),q&&q.val(""),c=h(c,o),(s||t)&&(m.css(o.css.wrapper),c.css(s?o.css.input:o.css.inputWithNoHint),c.wrap(m).parent().prepend(s?q:null).append(t?r:null)),y=t?j:i,u=new d({el:c}),v=new g({hint:q,input:c},o),w=new y({node:r,datasets:l},o),x=new k({input:v,menu:w,eventBus:u,minLength:e.minLength},o),c.data(p.www,o),c.data(p.typeahead,x)}var o;return l=b.isArray(l)?l:[].slice.call(arguments,1),e=e||{},o=c(e.classNames),this.each(m)},isEnabled:function(){var a;return e(this.first(),function(b){a=b.isEnabled()}),a},enable:function(){return e(this,function(a){a.enable()}),this},disable:function(){return e(this,function(a){a.disable()}),this},isActive:function(){var a;return e(this.first(),function(b){a=b.isActive()}),a},activate:function(){return e(this,function(a){a.activate()}),this},deactivate:function(){return e(this,function(a){a.deactivate()}),this},isOpen:function(){var a;return e(this.first(),function(b){a=b.isOpen()}),a},open:function(){return e(this,function(a){a.open()}),this},close:function(){return e(this,function(a){a.close()}),this},select:function(b){var c=!1,d=a(b);return e(this.first(),function(a){c=a.select(d)}),c},autocomplete:function(b){var c=!1,d=a(b);return e(this.first(),function(a){c=a.autocomplete(d)}),c},moveCursor:function(a){var b=!1;return e(this.first(),function(c){b=c.moveCursor(a)}),b},val:function(a){var b;return arguments.length?(e(this,function(b){b.setVal(a)}),this):(e(this.first(),function(a){b=a.getVal()}),b)},destroy:function(){return e(this,function(a,b){m(b),a.destroy()}),this}},a.fn.typeahead=function(a){return q[a]?q[a].apply(this,[].slice.call(arguments,1)):q.initialize.apply(this,arguments)},a.fn.typeahead.noConflict=function(){return a.fn.typeahead=o,this}}()});

/*Slider*/
var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},windowIsDefined="object"==("undefined"==typeof window?"undefined":_typeof(window));!function(t){if("function"==typeof define&&define.amd)define(["jquery"],t);else if("object"==("undefined"==typeof module?"undefined":_typeof(module))&&module.exports){var e;try{e=require("jquery")}catch(t){e=null}module.exports=t(e)}else window&&(window.Slider=t(window.jQuery))}(function(t){var e;return windowIsDefined&&!window.console&&(window.console={}),windowIsDefined&&!window.console.log&&(window.console.log=function(){}),windowIsDefined&&!window.console.warn&&(window.console.warn=function(){}),function(t){function e(){}var i=Array.prototype.slice;!function(t){if(t){var s="undefined"==typeof console?e:function(t){console.error(t)};t.bridget=function(e,o){!function(e){e.prototype.option||(e.prototype.option=function(e){t.isPlainObject(e)&&(this.options=t.extend(!0,this.options,e))})}(o),function(e,o){t.fn[e]=function(n){if("string"==typeof n){for(var a=i.call(arguments,1),h=0,l=this.length;h<l;h++){var r=this[h],d=t.data(r,e);if(d)if(t.isFunction(d[n])&&"_"!==n.charAt(0)){var p=d[n].apply(d,a);if(void 0!==p&&p!==d)return p}else s("no such method '"+n+"' for "+e+" instance");else s("cannot call methods on "+e+" prior to initialization; attempted to call '"+n+"'")}return this}var c=this.map(function(){var i=t.data(this,e);return i?(i.option(n),i._init()):(i=new o(this,n),t.data(this,e,i)),t(this)});return!c||c.length>1?c:c[0]}}(e,o)},t.bridget}}(t)}(t),function(t){function i(e,i){function s(t,e){var i="data-slider-"+e.replace(/_/g,"-"),s=t.getAttribute(i);try{return JSON.parse(s)}catch(t){return s}}this._state={value:null,enabled:null,offset:null,size:null,percentage:null,inDrag:!1,over:!1},this.ticksCallbackMap={},this.handleCallbackMap={},"string"==typeof e?this.element=document.querySelector(e):e instanceof HTMLElement&&(this.element=e),i=i||{};for(var n=Object.keys(this.defaultOptions),a=0;a<n.length;a++){var h=n[a],l=i[h];l=null!==(l=void 0!==l?l:s(this.element,h))?l:this.defaultOptions[h],this.options||(this.options={}),this.options[h]=l}"auto"===this.options.rtl&&(this.options.rtl="rtl"===window.getComputedStyle(this.element).direction),"vertical"!==this.options.orientation||"top"!==this.options.tooltip_position&&"bottom"!==this.options.tooltip_position?"horizontal"!==this.options.orientation||"left"!==this.options.tooltip_position&&"right"!==this.options.tooltip_position||(this.options.tooltip_position="top"):this.options.rtl?this.options.tooltip_position="left":this.options.tooltip_position="right";var r,d,p,c,u,m=this.element.style.width,v=!1,_=this.element.parentNode;if(this.sliderElem)v=!0;else{this.sliderElem=document.createElement("div"),this.sliderElem.className="slider";var f=document.createElement("div");f.className="slider-track",(d=document.createElement("div")).className="slider-track-low",(r=document.createElement("div")).className="slider-selection",(p=document.createElement("div")).className="slider-track-high",(c=document.createElement("div")).className="slider-handle min-slider-handle",c.setAttribute("role","slider"),c.setAttribute("aria-valuemin",this.options.min),c.setAttribute("aria-valuemax",this.options.max),(u=document.createElement("div")).className="slider-handle max-slider-handle",u.setAttribute("role","slider"),u.setAttribute("aria-valuemin",this.options.min),u.setAttribute("aria-valuemax",this.options.max),f.appendChild(d),f.appendChild(r),f.appendChild(p),this.rangeHighlightElements=[];var g=this.options.rangeHighlights;if(Array.isArray(g)&&g.length>0)for(var y=0;y<g.length;y++){var b=document.createElement("div"),k=g[y].class||"";b.className="slider-rangeHighlight slider-selection "+k,this.rangeHighlightElements.push(b),f.appendChild(b)}var E=Array.isArray(this.options.labelledby);if(E&&this.options.labelledby[0]&&c.setAttribute("aria-labelledby",this.options.labelledby[0]),E&&this.options.labelledby[1]&&u.setAttribute("aria-labelledby",this.options.labelledby[1]),!E&&this.options.labelledby&&(c.setAttribute("aria-labelledby",this.options.labelledby),u.setAttribute("aria-labelledby",this.options.labelledby)),this.ticks=[],Array.isArray(this.options.ticks)&&this.options.ticks.length>0){for(this.ticksContainer=document.createElement("div"),this.ticksContainer.className="slider-tick-container",a=0;a<this.options.ticks.length;a++){var C=document.createElement("div");if(C.className="slider-tick",this.options.ticks_tooltip){var w=this._addTickListener(),x=w.addMouseEnter(this,C,a),L=w.addMouseLeave(this,C);this.ticksCallbackMap[a]={mouseEnter:x,mouseLeave:L}}this.ticks.push(C),this.ticksContainer.appendChild(C)}r.className+=" tick-slider-selection"}if(this.tickLabels=[],Array.isArray(this.options.ticks_labels)&&this.options.ticks_labels.length>0)for(this.tickLabelContainer=document.createElement("div"),this.tickLabelContainer.className="slider-tick-label-container",a=0;a<this.options.ticks_labels.length;a++){var P=document.createElement("div"),T=0===this.options.ticks_positions.length,M=this.options.reversed&&T?this.options.ticks_labels.length-(a+1):a;P.className="slider-tick-label",P.innerHTML=this.options.ticks_labels[M],this.tickLabels.push(P),this.tickLabelContainer.appendChild(P)}var A=function(t){var e=document.createElement("div");e.className="tooltip-arrow";var i=document.createElement("div");i.className="tooltip-inner",t.appendChild(e),t.appendChild(i)},H=document.createElement("div");H.className="tooltip tooltip-main",H.setAttribute("role","presentation"),A(H);var N=document.createElement("div");N.className="tooltip tooltip-min",N.setAttribute("role","presentation"),A(N);var S=document.createElement("div");S.className="tooltip tooltip-max",S.setAttribute("role","presentation"),A(S),this.sliderElem.appendChild(f),this.sliderElem.appendChild(H),this.sliderElem.appendChild(N),this.sliderElem.appendChild(S),this.tickLabelContainer&&this.sliderElem.appendChild(this.tickLabelContainer),this.ticksContainer&&this.sliderElem.appendChild(this.ticksContainer),this.sliderElem.appendChild(c),this.sliderElem.appendChild(u),_.insertBefore(this.sliderElem,this.element),this.element.style.display="none"}if(t&&(this.$element=t(this.element),this.$sliderElem=t(this.sliderElem)),this.eventToCallbackMap={},this.sliderElem.id=this.options.id,this.touchCapable="ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch,this.touchX=0,this.touchY=0,this.tooltip=this.sliderElem.querySelector(".tooltip-main"),this.tooltipInner=this.tooltip.querySelector(".tooltip-inner"),this.tooltip_min=this.sliderElem.querySelector(".tooltip-min"),this.tooltipInner_min=this.tooltip_min.querySelector(".tooltip-inner"),this.tooltip_max=this.sliderElem.querySelector(".tooltip-max"),this.tooltipInner_max=this.tooltip_max.querySelector(".tooltip-inner"),o[this.options.scale]&&(this.options.scale=o[this.options.scale]),!0===v&&(this._removeClass(this.sliderElem,"slider-horizontal"),this._removeClass(this.sliderElem,"slider-vertical"),this._removeClass(this.sliderElem,"slider-rtl"),this._removeClass(this.tooltip,"hide"),this._removeClass(this.tooltip_min,"hide"),this._removeClass(this.tooltip_max,"hide"),["left","right","top","width","height"].forEach(function(t){this._removeProperty(this.trackLow,t),this._removeProperty(this.trackSelection,t),this._removeProperty(this.trackHigh,t)},this),[this.handle1,this.handle2].forEach(function(t){this._removeProperty(t,"left"),this._removeProperty(t,"right"),this._removeProperty(t,"top")},this),[this.tooltip,this.tooltip_min,this.tooltip_max].forEach(function(t){this._removeProperty(t,"left"),this._removeProperty(t,"right"),this._removeProperty(t,"top"),this._removeClass(t,"right"),this._removeClass(t,"left"),this._removeClass(t,"top")},this)),"vertical"===this.options.orientation?(this._addClass(this.sliderElem,"slider-vertical"),this.stylePos="top",this.mousePos="pageY",this.sizePos="offsetHeight"):(this._addClass(this.sliderElem,"slider-horizontal"),this.sliderElem.style.width=m,this.options.orientation="horizontal",this.options.rtl?this.stylePos="right":this.stylePos="left",this.mousePos="clientX",this.sizePos="offsetWidth"),this.options.rtl&&this._addClass(this.sliderElem,"slider-rtl"),this._setTooltipPosition(),Array.isArray(this.options.ticks)&&this.options.ticks.length>0&&(this.options.max=Math.max.apply(Math,this.options.ticks),this.options.min=Math.min.apply(Math,this.options.ticks)),Array.isArray(this.options.value)?(this.options.range=!0,this._state.value=this.options.value):this.options.range?this._state.value=[this.options.value,this.options.max]:this._state.value=this.options.value,this.trackLow=d||this.trackLow,this.trackSelection=r||this.trackSelection,this.trackHigh=p||this.trackHigh,"none"===this.options.selection?(this._addClass(this.trackLow,"hide"),this._addClass(this.trackSelection,"hide"),this._addClass(this.trackHigh,"hide")):"after"!==this.options.selection&&"before"!==this.options.selection||(this._removeClass(this.trackLow,"hide"),this._removeClass(this.trackSelection,"hide"),this._removeClass(this.trackHigh,"hide")),this.handle1=c||this.handle1,this.handle2=u||this.handle2,!0===v)for(this._removeClass(this.handle1,"round triangle"),this._removeClass(this.handle2,"round triangle hide"),a=0;a<this.ticks.length;a++)this._removeClass(this.ticks[a],"round triangle hide");if(-1!==["round","triangle","custom"].indexOf(this.options.handle))for(this._addClass(this.handle1,this.options.handle),this._addClass(this.handle2,this.options.handle),a=0;a<this.ticks.length;a++)this._addClass(this.ticks[a],this.options.handle);if(this._state.offset=this._offset(this.sliderElem),this._state.size=this.sliderElem[this.sizePos],this.setValue(this._state.value),this.handle1Keydown=this._keydown.bind(this,0),this.handle1.addEventListener("keydown",this.handle1Keydown,!1),this.handle2Keydown=this._keydown.bind(this,1),this.handle2.addEventListener("keydown",this.handle2Keydown,!1),this.mousedown=this._mousedown.bind(this),this.touchstart=this._touchstart.bind(this),this.touchmove=this._touchmove.bind(this),this.touchCapable){var z=!1;try{var D=Object.defineProperty({},"passive",{get:function(){z=!0}});window.addEventListener("test",null,D)}catch(t){}var V=!!z&&{passive:!0};this.sliderElem.addEventListener("touchstart",this.touchstart,V),this.sliderElem.addEventListener("touchmove",this.touchmove,V)}if(this.sliderElem.addEventListener("mousedown",this.mousedown,!1),this.resize=this._resize.bind(this),window.addEventListener("resize",this.resize,!1),"hide"===this.options.tooltip)this._addClass(this.tooltip,"hide"),this._addClass(this.tooltip_min,"hide"),this._addClass(this.tooltip_max,"hide");else if("always"===this.options.tooltip)this._showTooltip(),this._alwaysShowTooltip=!0;else{if(this.showTooltip=this._showTooltip.bind(this),this.hideTooltip=this._hideTooltip.bind(this),this.options.ticks_tooltip){var F=this._addTickListener(),R=F.addMouseEnter(this,this.handle1),I=F.addMouseLeave(this,this.handle1);this.handleCallbackMap.handle1={mouseEnter:R,mouseLeave:I},R=F.addMouseEnter(this,this.handle2),I=F.addMouseLeave(this,this.handle2),this.handleCallbackMap.handle2={mouseEnter:R,mouseLeave:I}}else this.sliderElem.addEventListener("mouseenter",this.showTooltip,!1),this.sliderElem.addEventListener("mouseleave",this.hideTooltip,!1);this.handle1.addEventListener("focus",this.showTooltip,!1),this.handle1.addEventListener("blur",this.hideTooltip,!1),this.handle2.addEventListener("focus",this.showTooltip,!1),this.handle2.addEventListener("blur",this.hideTooltip,!1)}this.options.enabled?this.enable():this.disable()}var s=function(t){return"Invalid input value '"+t+"' passed in"},o={linear:{toValue:function(t){var e=t/100*(this.options.max-this.options.min),i=!0;if(this.options.ticks_positions.length>0){for(var s,o,n,a=0,h=1;h<this.options.ticks_positions.length;h++)if(t<=this.options.ticks_positions[h]){s=this.options.ticks[h-1],n=this.options.ticks_positions[h-1],o=this.options.ticks[h],a=this.options.ticks_positions[h];break}e=s+(t-n)/(a-n)*(o-s),i=!1}var l=(i?this.options.min:0)+Math.round(e/this.options.step)*this.options.step;return l<this.options.min?this.options.min:l>this.options.max?this.options.max:l},toPercentage:function(t){if(this.options.max===this.options.min)return 0;if(this.options.ticks_positions.length>0){for(var e,i,s,o=0,n=0;n<this.options.ticks.length;n++)if(t<=this.options.ticks[n]){e=n>0?this.options.ticks[n-1]:0,s=n>0?this.options.ticks_positions[n-1]:0,i=this.options.ticks[n],o=this.options.ticks_positions[n];break}if(n>0)return s+(t-e)/(i-e)*(o-s)}return 100*(t-this.options.min)/(this.options.max-this.options.min)}},logarithmic:{toValue:function(t){var e=0===this.options.min?0:Math.log(this.options.min),i=Math.log(this.options.max),s=Math.exp(e+(i-e)*t/100);return Math.round(s)===this.options.max?this.options.max:(s=this.options.min+Math.round((s-this.options.min)/this.options.step)*this.options.step)<this.options.min?this.options.min:s>this.options.max?this.options.max:s},toPercentage:function(t){if(this.options.max===this.options.min)return 0;var e=Math.log(this.options.max),i=0===this.options.min?0:Math.log(this.options.min);return 100*((0===t?0:Math.log(t))-i)/(e-i)}}};if((e=function(t,e){return i.call(this,t,e),this}).prototype={_init:function(){},constructor:e,defaultOptions:{id:"",min:0,max:10,step:1,precision:0,orientation:"horizontal",value:5,range:!1,selection:"before",tooltip:"show",tooltip_split:!1,handle:"round",reversed:!1,rtl:"auto",enabled:!0,formatter:function(t){return Array.isArray(t)?t[0]+" : "+t[1]:t},natural_arrow_keys:!1,ticks:[],ticks_positions:[],ticks_labels:[],ticks_snap_bounds:0,ticks_tooltip:!1,scale:"linear",focus:!1,tooltip_position:null,labelledby:null,rangeHighlights:[]},getElement:function(){return this.sliderElem},getValue:function(){return this.options.range?this._state.value:this._state.value[0]},setValue:function(t,e,i){t||(t=0);var s=this.getValue();this._state.value=this._validateInputValue(t);var o=this._applyPrecision.bind(this);this.options.range?(this._state.value[0]=o(this._state.value[0]),this._state.value[1]=o(this._state.value[1]),this._state.value[0]=Math.max(this.options.min,Math.min(this.options.max,this._state.value[0])),this._state.value[1]=Math.max(this.options.min,Math.min(this.options.max,this._state.value[1]))):(this._state.value=o(this._state.value),this._state.value=[Math.max(this.options.min,Math.min(this.options.max,this._state.value))],this._addClass(this.handle2,"hide"),"after"===this.options.selection?this._state.value[1]=this.options.max:this._state.value[1]=this.options.min),this.options.max>this.options.min?this._state.percentage=[this._toPercentage(this._state.value[0]),this._toPercentage(this._state.value[1]),100*this.options.step/(this.options.max-this.options.min)]:this._state.percentage=[0,0,100],this._layout();var n=this.options.range?this._state.value:this._state.value[0];return this._setDataVal(n),!0===e&&this._trigger("slide",n),s!==n&&!0===i&&this._trigger("change",{oldValue:s,newValue:n}),this},destroy:function(){this._removeSliderEventHandlers(),this.sliderElem.parentNode.removeChild(this.sliderElem),this.element.style.display="",this._cleanUpEventCallbacksMap(),this.element.removeAttribute("data"),t&&(this._unbindJQueryEventHandlers(),this.$element.removeData("slider"))},disable:function(){return this._state.enabled=!1,this.handle1.removeAttribute("tabindex"),this.handle2.removeAttribute("tabindex"),this._addClass(this.sliderElem,"slider-disabled"),this._trigger("slideDisabled"),this},enable:function(){return this._state.enabled=!0,this.handle1.setAttribute("tabindex",0),this.handle2.setAttribute("tabindex",0),this._removeClass(this.sliderElem,"slider-disabled"),this._trigger("slideEnabled"),this},toggle:function(){return this._state.enabled?this.disable():this.enable(),this},isEnabled:function(){return this._state.enabled},on:function(t,e){return this._bindNonQueryEventHandler(t,e),this},off:function(e,i){t?(this.$element.off(e,i),this.$sliderElem.off(e,i)):this._unbindNonQueryEventHandler(e,i)},getAttribute:function(t){return t?this.options[t]:this.options},setAttribute:function(t,e){return this.options[t]=e,this},refresh:function(){return this._removeSliderEventHandlers(),i.call(this,this.element,this.options),t&&t.data(this.element,"slider",this),this},relayout:function(){return this._resize(),this._layout(),this},_removeSliderEventHandlers:function(){if(this.handle1.removeEventListener("keydown",this.handle1Keydown,!1),this.handle2.removeEventListener("keydown",this.handle2Keydown,!1),this.options.ticks_tooltip){for(var t=this.ticksContainer.getElementsByClassName("slider-tick"),e=0;e<t.length;e++)t[e].removeEventListener("mouseenter",this.ticksCallbackMap[e].mouseEnter,!1),t[e].removeEventListener("mouseleave",this.ticksCallbackMap[e].mouseLeave,!1);this.handle1.removeEventListener("mouseenter",this.handleCallbackMap.handle1.mouseEnter,!1),this.handle2.removeEventListener("mouseenter",this.handleCallbackMap.handle2.mouseEnter,!1),this.handle1.removeEventListener("mouseleave",this.handleCallbackMap.handle1.mouseLeave,!1),this.handle2.removeEventListener("mouseleave",this.handleCallbackMap.handle2.mouseLeave,!1)}this.handleCallbackMap=null,this.ticksCallbackMap=null,this.showTooltip&&(this.handle1.removeEventListener("focus",this.showTooltip,!1),this.handle2.removeEventListener("focus",this.showTooltip,!1)),this.hideTooltip&&(this.handle1.removeEventListener("blur",this.hideTooltip,!1),this.handle2.removeEventListener("blur",this.hideTooltip,!1)),this.showTooltip&&this.sliderElem.removeEventListener("mouseenter",this.showTooltip,!1),this.hideTooltip&&this.sliderElem.removeEventListener("mouseleave",this.hideTooltip,!1),this.sliderElem.removeEventListener("touchstart",this.touchstart,!1),this.sliderElem.removeEventListener("touchmove",this.touchmove,!1),this.sliderElem.removeEventListener("mousedown",this.mousedown,!1),window.removeEventListener("resize",this.resize,!1)},_bindNonQueryEventHandler:function(t,e){void 0===this.eventToCallbackMap[t]&&(this.eventToCallbackMap[t]=[]),this.eventToCallbackMap[t].push(e)},_unbindNonQueryEventHandler:function(t,e){var i=this.eventToCallbackMap[t];if(void 0!==i)for(var s=0;s<i.length;s++)if(i[s]===e){i.splice(s,1);break}},_cleanUpEventCallbacksMap:function(){for(var t=Object.keys(this.eventToCallbackMap),e=0;e<t.length;e++){var i=t[e];delete this.eventToCallbackMap[i]}},_showTooltip:function(){!1===this.options.tooltip_split?(this._addClass(this.tooltip,"in"),this.tooltip_min.style.display="none",this.tooltip_max.style.display="none"):(this._addClass(this.tooltip_min,"in"),this._addClass(this.tooltip_max,"in"),this.tooltip.style.display="none"),this._state.over=!0},_hideTooltip:function(){!1===this._state.inDrag&&!0!==this.alwaysShowTooltip&&(this._removeClass(this.tooltip,"in"),this._removeClass(this.tooltip_min,"in"),this._removeClass(this.tooltip_max,"in")),this._state.over=!1},_setToolTipOnMouseOver:function(t){function e(t,e){return e?[100-t.percentage[0],this.options.range?100-t.percentage[1]:t.percentage[1]]:[t.percentage[0],t.percentage[1]]}var i=this.options.formatter(t?t.value[0]:this._state.value[0]),s=e(t||this._state,this.options.reversed);this._setText(this.tooltipInner,i),this.tooltip.style[this.stylePos]=s[0]+"%"},_addTickListener:function(){return{addMouseEnter:function(t,e,i){var s=function e(){var s=t._state,e=i>=0?i:this.attributes["aria-valuenow"].value,o=parseInt(e,10);s.value[0]=o,s.percentage[0]=t.options.ticks_positions[o],t._setToolTipOnMouseOver(s),t._showTooltip()};return e.addEventListener("mouseenter",s,!1),s},addMouseLeave:function(t,e){var i=function(){t._hideTooltip()};return e.addEventListener("mouseleave",i,!1),i}}},_layout:function(){var t,e;if(t=this.options.reversed?[100-this._state.percentage[0],this.options.range?100-this._state.percentage[1]:this._state.percentage[1]]:[this._state.percentage[0],this._state.percentage[1]],this.handle1.style[this.stylePos]=t[0]+"%",this.handle1.setAttribute("aria-valuenow",this._state.value[0]),isNaN(this.options.formatter(this._state.value[0]))&&this.handle1.setAttribute("aria-valuetext",this.options.formatter(this._state.value[0])),this.handle2.style[this.stylePos]=t[1]+"%",this.handle2.setAttribute("aria-valuenow",this._state.value[1]),isNaN(this.options.formatter(this._state.value[1]))&&this.handle2.setAttribute("aria-valuetext",this.options.formatter(this._state.value[1])),this.rangeHighlightElements.length>0&&Array.isArray(this.options.rangeHighlights)&&this.options.rangeHighlights.length>0)for(var i=0;i<this.options.rangeHighlights.length;i++){var s=this._toPercentage(this.options.rangeHighlights[i].start),o=this._toPercentage(this.options.rangeHighlights[i].end);if(this.options.reversed){var n=100-o;o=100-s,s=n}var a=this._createHighlightRange(s,o);a?"vertical"===this.options.orientation?(this.rangeHighlightElements[i].style.top=a.start+"%",this.rangeHighlightElements[i].style.height=a.size+"%"):(this.options.rtl?this.rangeHighlightElements[i].style.right=a.start+"%":this.rangeHighlightElements[i].style.left=a.start+"%",this.rangeHighlightElements[i].style.width=a.size+"%"):this.rangeHighlightElements[i].style.display="none"}if(Array.isArray(this.options.ticks)&&this.options.ticks.length>0){var h,l="vertical"===this.options.orientation?"height":"width";h="vertical"===this.options.orientation?"marginTop":this.options.rtl?"marginRight":"marginLeft";var r=this._state.size/(this.options.ticks.length-1);if(this.tickLabelContainer){var d=0;if(0===this.options.ticks_positions.length)"vertical"!==this.options.orientation&&(this.tickLabelContainer.style[h]=-r/2+"px"),d=this.tickLabelContainer.offsetHeight;else for(p=0;p<this.tickLabelContainer.childNodes.length;p++)this.tickLabelContainer.childNodes[p].offsetHeight>d&&(d=this.tickLabelContainer.childNodes[p].offsetHeight);"horizontal"===this.options.orientation&&(this.sliderElem.style.marginBottom=d+"px")}for(var p=0;p<this.options.ticks.length;p++){var c=this.options.ticks_positions[p]||this._toPercentage(this.options.ticks[p]);this.options.reversed&&(c=100-c),this.ticks[p].style[this.stylePos]=c+"%",this._removeClass(this.ticks[p],"in-selection"),this.options.range?c>=t[0]&&c<=t[1]&&this._addClass(this.ticks[p],"in-selection"):"after"===this.options.selection&&c>=t[0]?this._addClass(this.ticks[p],"in-selection"):"before"===this.options.selection&&c<=t[0]&&this._addClass(this.ticks[p],"in-selection"),this.tickLabels[p]&&(this.tickLabels[p].style[l]=r+"px","vertical"!==this.options.orientation&&void 0!==this.options.ticks_positions[p]?(this.tickLabels[p].style.position="absolute",this.tickLabels[p].style[this.stylePos]=c+"%",this.tickLabels[p].style[h]=-r/2+"px"):"vertical"===this.options.orientation&&(this.options.rtl?this.tickLabels[p].style.marginRight=this.sliderElem.offsetWidth+"px":this.tickLabels[p].style.marginLeft=this.sliderElem.offsetWidth+"px",this.tickLabelContainer.style[h]=this.sliderElem.offsetWidth/2*-1+"px"))}}if(this.options.range){e=this.options.formatter(this._state.value),this._setText(this.tooltipInner,e),this.tooltip.style[this.stylePos]=(t[1]+t[0])/2+"%";var u=this.options.formatter(this._state.value[0]);this._setText(this.tooltipInner_min,u);var m=this.options.formatter(this._state.value[1]);this._setText(this.tooltipInner_max,m),this.tooltip_min.style[this.stylePos]=t[0]+"%",this.tooltip_max.style[this.stylePos]=t[1]+"%"}else e=this.options.formatter(this._state.value[0]),this._setText(this.tooltipInner,e),this.tooltip.style[this.stylePos]=t[0]+"%";if("vertical"===this.options.orientation)this.trackLow.style.top="0",this.trackLow.style.height=Math.min(t[0],t[1])+"%",this.trackSelection.style.top=Math.min(t[0],t[1])+"%",this.trackSelection.style.height=Math.abs(t[0]-t[1])+"%",this.trackHigh.style.bottom="0",this.trackHigh.style.height=100-Math.min(t[0],t[1])-Math.abs(t[0]-t[1])+"%";else{"right"===this.stylePos?this.trackLow.style.right="0":this.trackLow.style.left="0",this.trackLow.style.width=Math.min(t[0],t[1])+"%","right"===this.stylePos?this.trackSelection.style.right=Math.min(t[0],t[1])+"%":this.trackSelection.style.left=Math.min(t[0],t[1])+"%",this.trackSelection.style.width=Math.abs(t[0]-t[1])+"%","right"===this.stylePos?this.trackHigh.style.left="0":this.trackHigh.style.right="0",this.trackHigh.style.width=100-Math.min(t[0],t[1])-Math.abs(t[0]-t[1])+"%";var v=this.tooltip_min.getBoundingClientRect(),_=this.tooltip_max.getBoundingClientRect();"bottom"===this.options.tooltip_position?v.right>_.left?(this._removeClass(this.tooltip_max,"bottom"),this._addClass(this.tooltip_max,"top"),this.tooltip_max.style.top="",this.tooltip_max.style.bottom="22px"):(this._removeClass(this.tooltip_max,"top"),this._addClass(this.tooltip_max,"bottom"),this.tooltip_max.style.top=this.tooltip_min.style.top,this.tooltip_max.style.bottom=""):v.right>_.left?(this._removeClass(this.tooltip_max,"top"),this._addClass(this.tooltip_max,"bottom"),this.tooltip_max.style.top="18px"):(this._removeClass(this.tooltip_max,"bottom"),this._addClass(this.tooltip_max,"top"),this.tooltip_max.style.top=this.tooltip_min.style.top)}},_createHighlightRange:function(t,e){return this._isHighlightRange(t,e)?t>e?{start:e,size:t-e}:{start:t,size:e-t}:null},_isHighlightRange:function(t,e){return 0<=t&&t<=100&&0<=e&&e<=100},_resize:function(t){this._state.offset=this._offset(this.sliderElem),this._state.size=this.sliderElem[this.sizePos],this._layout()},_removeProperty:function(t,e){t.style.removeProperty?t.style.removeProperty(e):t.style.removeAttribute(e)},_mousedown:function(t){if(!this._state.enabled)return!1;t.preventDefault&&t.preventDefault(),this._state.offset=this._offset(this.sliderElem),this._state.size=this.sliderElem[this.sizePos];var e=this._getPercentage(t);if(this.options.range){var i=Math.abs(this._state.percentage[0]-e),s=Math.abs(this._state.percentage[1]-e);this._state.dragged=i<s?0:1,this._adjustPercentageForRangeSliders(e)}else this._state.dragged=0;this._state.percentage[this._state.dragged]=e,this._layout(),this.touchCapable&&(document.removeEventListener("touchmove",this.mousemove,!1),document.removeEventListener("touchend",this.mouseup,!1)),this.mousemove&&document.removeEventListener("mousemove",this.mousemove,!1),this.mouseup&&document.removeEventListener("mouseup",this.mouseup,!1),this.mousemove=this._mousemove.bind(this),this.mouseup=this._mouseup.bind(this),this.touchCapable&&(document.addEventListener("touchmove",this.mousemove,!1),document.addEventListener("touchend",this.mouseup,!1)),document.addEventListener("mousemove",this.mousemove,!1),document.addEventListener("mouseup",this.mouseup,!1),this._state.inDrag=!0;var o=this._calculateValue();return this._trigger("slideStart",o),this._setDataVal(o),this.setValue(o,!1,!0),t.returnValue=!1,this.options.focus&&this._triggerFocusOnHandle(this._state.dragged),!0},_touchstart:function(t){if(void 0!==t.changedTouches){var e=t.changedTouches[0];this.touchX=e.pageX,this.touchY=e.pageY}else this._mousedown(t)},_triggerFocusOnHandle:function(t){0===t&&this.handle1.focus(),1===t&&this.handle2.focus()},_keydown:function(t,e){if(!this._state.enabled)return!1;var i;switch(e.keyCode){case 37:case 40:i=-1;break;case 39:case 38:i=1}if(i){if(this.options.natural_arrow_keys){var s="vertical"===this.options.orientation&&!this.options.reversed,o="horizontal"===this.options.orientation&&this.options.reversed;(s||o)&&(i=-i)}var n=this._state.value[t]+i*this.options.step,a=n/this.options.max*100;return this._state.keyCtrl=t,this.options.range&&(this._adjustPercentageForRangeSliders(a),n=[this._state.keyCtrl?this._state.value[0]:n,this._state.keyCtrl?n:this._state.value[1]]),this._trigger("slideStart",n),this._setDataVal(n),this.setValue(n,!0,!0),this._setDataVal(n),this._trigger("slideStop",n),this._layout(),this._pauseEvent(e),delete this._state.keyCtrl,!1}},_pauseEvent:function(t){t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),t.cancelBubble=!0,t.returnValue=!1},_mousemove:function(t){if(!this._state.enabled)return!1;var e=this._getPercentage(t);this._adjustPercentageForRangeSliders(e),this._state.percentage[this._state.dragged]=e,this._layout();var i=this._calculateValue(!0);return this.setValue(i,!0,!0),!1},_touchmove:function(t){if(void 0!==t.changedTouches){var e=t.changedTouches[0],i=e.pageX-this.touchX,s=e.pageY-this.touchY;this._state.inDrag||("vertical"===this.options.orientation&&i<=5&&i>=-5&&(s>=15||s<=-15)?this._mousedown(t):s<=5&&s>=-5&&(i>=15||i<=-15)&&this._mousedown(t))}},_adjustPercentageForRangeSliders:function(t){if(this.options.range){var e=this._getNumDigitsAfterDecimalPlace(t);e=e?e-1:0;var i=this._applyToFixedAndParseFloat(t,e);0===this._state.dragged&&this._applyToFixedAndParseFloat(this._state.percentage[1],e)<i?(this._state.percentage[0]=this._state.percentage[1],this._state.dragged=1):1===this._state.dragged&&this._applyToFixedAndParseFloat(this._state.percentage[0],e)>i?(this._state.percentage[1]=this._state.percentage[0],this._state.dragged=0):0===this._state.keyCtrl&&this._state.value[1]/this.options.max*100<t?(this._state.percentage[0]=this._state.percentage[1],this._state.keyCtrl=1,this.handle2.focus()):1===this._state.keyCtrl&&this._state.value[0]/this.options.max*100>t&&(this._state.percentage[1]=this._state.percentage[0],this._state.keyCtrl=0,this.handle1.focus())}},_mouseup:function(){if(!this._state.enabled)return!1;this.touchCapable&&(document.removeEventListener("touchmove",this.mousemove,!1),document.removeEventListener("touchend",this.mouseup,!1)),document.removeEventListener("mousemove",this.mousemove,!1),document.removeEventListener("mouseup",this.mouseup,!1),this._state.inDrag=!1,!1===this._state.over&&this._hideTooltip();var t=this._calculateValue(!0);return this._layout(),this._setDataVal(t),this._trigger("slideStop",t),!1},_calculateValue:function(t){var e;if(this.options.range?(e=[this.options.min,this.options.max],0!==this._state.percentage[0]&&(e[0]=this._toValue(this._state.percentage[0]),e[0]=this._applyPrecision(e[0])),100!==this._state.percentage[1]&&(e[1]=this._toValue(this._state.percentage[1]),e[1]=this._applyPrecision(e[1]))):(e=this._toValue(this._state.percentage[0]),e=parseFloat(e),e=this._applyPrecision(e)),t){for(var i=[e,1/0],s=0;s<this.options.ticks.length;s++){var o=Math.abs(this.options.ticks[s]-e);o<=i[1]&&(i=[this.options.ticks[s],o])}if(i[1]<=this.options.ticks_snap_bounds)return i[0]}return e},_applyPrecision:function(t){var e=this.options.precision||this._getNumDigitsAfterDecimalPlace(this.options.step);return this._applyToFixedAndParseFloat(t,e)},_getNumDigitsAfterDecimalPlace:function(t){var e=(""+t).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/);return e?Math.max(0,(e[1]?e[1].length:0)-(e[2]?+e[2]:0)):0},_applyToFixedAndParseFloat:function(t,e){var i=t.toFixed(e);return parseFloat(i)},_getPercentage:function(t){!this.touchCapable||"touchstart"!==t.type&&"touchmove"!==t.type||(t=t.touches[0]);var e=t[this.mousePos]-this._state.offset[this.stylePos];"right"===this.stylePos&&(e=-e);var i=e/this._state.size*100;return i=Math.round(i/this._state.percentage[2])*this._state.percentage[2],this.options.reversed&&(i=100-i),Math.max(0,Math.min(100,i))},_validateInputValue:function(t){if(isNaN(+t)){if(Array.isArray(t))return this._validateArray(t),t;throw new Error(s(t))}return+t},_validateArray:function(t){for(var e=0;e<t.length;e++){var i=t[e];if("number"!=typeof i)throw new Error(s(i))}},_setDataVal:function(t){this.element.setAttribute("data-value",t),this.element.setAttribute("value",t),this.element.value=t},_trigger:function(e,i){i=i||0===i?i:void 0;var s=this.eventToCallbackMap[e];if(s&&s.length)for(var o=0;o<s.length;o++)(0,s[o])(i);t&&this._triggerJQueryEvent(e,i)},_triggerJQueryEvent:function(t,e){var i={type:t,value:e};this.$element.trigger(i),this.$sliderElem.trigger(i)},_unbindJQueryEventHandlers:function(){this.$element.off(),this.$sliderElem.off()},_setText:function(t,e){void 0!==t.textContent?t.textContent=e:void 0!==t.innerText&&(t.innerText=e)},_removeClass:function(t,e){for(var i=e.split(" "),s=t.className,o=0;o<i.length;o++){var n=i[o],a=new RegExp("(?:\\s|^)"+n+"(?:\\s|$)");s=s.replace(a," ")}t.className=s.trim()},_addClass:function(t,e){for(var i=e.split(" "),s=t.className,o=0;o<i.length;o++){var n=i[o];new RegExp("(?:\\s|^)"+n+"(?:\\s|$)").test(s)||(s+=" "+n)}t.className=s.trim()},_offsetLeft:function(t){return t.getBoundingClientRect().left},_offsetRight:function(t){return t.getBoundingClientRect().right},_offsetTop:function(t){for(var e=t.offsetTop;(t=t.offsetParent)&&!isNaN(t.offsetTop);)e+=t.offsetTop,"BODY"!==t.tagName&&(e-=t.scrollTop);return e},_offset:function(t){return{left:this._offsetLeft(t),right:this._offsetRight(t),top:this._offsetTop(t)}},_css:function(e,i,s){if(t)t.style(e,i,s);else{var o=i.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(t,e){return e.toUpperCase()});e.style[o]=s}},_toValue:function(t){return this.options.scale.toValue.apply(this,[t])},_toPercentage:function(t){return this.options.scale.toPercentage.apply(this,[t])},_setTooltipPosition:function(){var t=[this.tooltip,this.tooltip_min,this.tooltip_max];if("vertical"===this.options.orientation){var e,i="left"===(e=this.options.tooltip_position?this.options.tooltip_position:this.options.rtl?"left":"right")?"right":"left";t.forEach(function(t){this._addClass(t,e),t.style[i]="100%"}.bind(this))}else"bottom"===this.options.tooltip_position?t.forEach(function(t){this._addClass(t,"bottom"),t.style.top="22px"}.bind(this)):t.forEach(function(t){this._addClass(t,"top"),t.style.top=-this.tooltip.outerHeight-14+"px"}.bind(this))}},t&&t.fn){var n=void 0;t.fn.slider?(n="aslSlider"):(t.bridget("bslider",e),n="bslider"),t.bridget("aslSlider",e),t(function(){t("input[data-provide=slider]")[n]()})}}(t),e});

/*Dropdown Selector*/
!function(t){"use strict";function e(t,e){for(var i=0;i<t.length;++i)e(t[i],i)}function i(e,i){this.$select=t(e),this.$select.attr("data-placeholder")&&(i.nonSelectedText=this.$select.data("placeholder")),this.options=this.mergeOptions(t.extend({},i,this.$select.data())),this.originalOptions=this.$select.clone()[0].options,this.query="",this.searchTimeout=null,this.lastToggledInput=null,this.options.multiple="multiple"===this.$select.attr("multiple"),this.options.onChange=t.proxy(this.options.onChange,this),this.options.onDropdownShow=t.proxy(this.options.onDropdownShow,this),this.options.onDropdownHide=t.proxy(this.options.onDropdownHide,this),this.options.onDropdownShown=t.proxy(this.options.onDropdownShown,this),this.options.onDropdownHidden=t.proxy(this.options.onDropdownHidden,this),this.options.onInitialized=t.proxy(this.options.onInitialized,this),this.buildContainer(),this.buildButton(),this.buildDropdown(),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(!0),this.options.disableIfEmpty&&t("option",this.$select).length<=0&&this.disable(),this.$select.hide().after(this.$container),this.options.onInitialized(this.$select,this.$container)}"undefined"!=typeof ko&&ko.bindingHandlers&&!ko.bindingHandlers.multiselect&&(ko.bindingHandlers.multiselect={after:["options","value","selectedOptions","enable","disable"],init:function(e,i,s){var l=t(e),o=ko.toJS(i());if(l.multiselect(o),s.has("options")){var n=s.get("options");ko.isObservable(n)&&ko.computed({read:function(){n(),setTimeout(function(){var t=l.data("multiselect");t&&t.updateOriginalOptions(),l.multiselect("rebuild")},1)},disposeWhenNodeIsRemoved:e})}if(s.has("value")){var a=s.get("value");ko.isObservable(a)&&ko.computed({read:function(){a(),setTimeout(function(){l.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}if(s.has("selectedOptions")){var p=s.get("selectedOptions");ko.isObservable(p)&&ko.computed({read:function(){p(),setTimeout(function(){l.multiselect("refresh")},1)},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0})}var h=function(t){setTimeout(function(){l.multiselect(t?"enable":"disable")})};if(s.has("enable")){var c=s.get("enable");ko.isObservable(c)?ko.computed({read:function(){h(c())},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0}):h(c)}if(s.has("disable")){var r=s.get("disable");ko.isObservable(r)?ko.computed({read:function(){h(!r())},disposeWhenNodeIsRemoved:e}).extend({rateLimit:100,notifyWhenChangesStop:!0}):h(!r)}ko.utils.domNodeDisposal.addDisposeCallback(e,function(){l.multiselect("destroy")})},update:function(e,i){var s=t(e),l=ko.toJS(i());s.multiselect("setOptions",l),s.multiselect("rebuild")}}),i.prototype={defaults:{buttonText:function(e,i){if(this.disabledText.length>0&&(this.disableIfEmpty||i.prop("disabled"))&&0==e.length)return this.disabledText;if(0===e.length)return this.nonSelectedText;if(this.allSelectedText&&e.length===t("option",t(i)).length&&1!==t("option",t(i)).length&&this.multiple)return this.selectAllNumber?this.allSelectedText+" ("+e.length+")":this.allSelectedText;if(e.length>this.numberDisplayed)return e.length+" "+this.nSelectedText;var s="",l=this.delimiterText;return e.each(function(){var e=void 0!==t(this).attr("label")?t(this).attr("label"):t(this).text();s+=e+l}),s.substr(0,s.length-2)},buttonTitle:function(e){if(0===e.length)return this.nonSelectedText;var i="",s=this.delimiterText;return e.each(function(){var e=void 0!==t(this).attr("label")?t(this).attr("label"):t(this).text();i+=e+s}),i.substr(0,i.length-2)},optionLabel:function(e){return t(e).attr("label")||t(e).text()},optionClass:function(e){return t(e).attr("class")||""},onChange:function(){},onDropdownShow:function(){},onDropdownHide:function(){},onDropdownShown:function(){},onDropdownHidden:function(){},onSelectAll:function(){},onInitialized:function(){},enableHTML:!1,buttonClass:"btn btn-default",inheritClass:!1,buttonWidth:"auto",buttonContainer:'<div class="btn-group" />',dropRight:!1,dropUp:!1,selectedClass:"active",maxHeight:!1,checkboxName:!1,includeSelectAllOption:!1,includeSelectAllIfMoreThan:0,selectAllText:" Select all",selectAllValue:"multiselect-all",selectAllName:!1,selectAllNumber:!0,selectAllJustVisible:!0,enableFiltering:0,enableCaseInsensitiveFiltering:!1,enableFullValueFiltering:!1,enableClickableOptGroups:!1,enableCollapsibelOptGroups:!1,filterPlaceholder:"Search",filterBehavior:"text",includeFilterClearBtn:!0,preventInputChangeEvent:!1,nonSelectedText:"None selected",nSelectedText:"selected",allSelectedText:"All selected",numberDisplayed:3,disableIfEmpty:!1,disabledText:"",delimiterText:", ",templates:{button:'<button type="button" class="multiselect adropdown-toggle style-btn" data-toggle="adropdown"><span class="multiselect-selected-text"></span> <b class="caret"></b></button>',ul:'<ul class="multiselect-container adropdown-menu"></ul>',filter:'<li class="multiselect-item filter"><div class="input-group"><span class="input-group-addon"><i class="icon-search-2"></i></span><input class="form-control multiselect-search" type="text"></div></li>',filterClearBtn:'<span class="input-group-btn"><button class="btn btn-default multiselect-clear-filter" type="button"><i class="icon-cancel-circled2"></i></button></span>',li:'<li><a tabindex="0"><label></label></a></li>',divider:'<li class="multiselect-item divider"></li>',liGroup:'<li class="multiselect-item multiselect-group"><label></label></li>'}},constructor:i,buildContainer:function(){this.$container=t(this.options.buttonContainer),this.$container.on("show.bs.adropdown",this.options.onDropdownShow),this.$container.on("hide.bs.adropdown",this.options.onDropdownHide),this.$container.on("shown.bs.adropdown",this.options.onDropdownShown),this.$container.on("hidden.bs.adropdown",this.options.onDropdownHidden)},buildButton:function(){this.$button=t(this.options.templates.button).addClass(this.options.buttonClass),this.$select.attr("class")&&this.options.inheritClass&&this.$button.addClass(this.$select.attr("class")),this.$select.prop("disabled")?this.disable():this.enable(),this.options.buttonWidth&&"auto"!==this.options.buttonWidth&&(this.$button.css({width:this.options.buttonWidth,overflow:"hidden","text-overflow":"ellipsis"}),this.$container.css({width:this.options.buttonWidth}));var e=this.$select.attr("tabindex");e&&this.$button.attr("tabindex",e),this.$container.prepend(this.$button)},buildDropdown:function(){if(this.$ul=t(this.options.templates.ul),this.options.dropRight&&this.$ul.addClass("pull-right"),this.options.maxHeight&&this.$ul.css({"max-height":this.options.maxHeight+"px","overflow-y":"auto","overflow-x":"hidden"}),this.options.dropUp){var e=Math.min(this.options.maxHeight,26*t('option[data-role!="divider"]',this.$select).length+19*t('option[data-role="divider"]',this.$select).length+(this.options.includeSelectAllOption?26:0)+(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering?44:0)),i=e+34;this.$ul.css({"max-height":e+"px","overflow-y":"auto","overflow-x":"hidden","margin-top":"-"+i+"px"})}this.$container.append(this.$ul)},buildDropdownOptions:function(){this.$select.children().each(t.proxy(function(e,i){var s=t(i),l=s.prop("tagName").toLowerCase();s.prop("value")!==this.options.selectAllValue&&("optgroup"===l?this.createOptgroup(i):"option"===l&&("divider"===s.data("role")?this.createDivider():this.createOptionValue(i)))},this)),t("li input",this.$ul).on("change",t.proxy(function(e){var i=t(e.target),s=i.prop("checked")||!1,l=i.val()===this.options.selectAllValue;this.options.selectedClass&&(s?i.closest("li").addClass(this.options.selectedClass):i.closest("li").removeClass(this.options.selectedClass));var o=i.val(),n=this.getOptionByValue(o),a=t("option",this.$select).not(n),p=t("input",this.$container).not(i);return l?s?this.selectAll(this.options.selectAllJustVisible):this.deselectAll(this.options.selectAllJustVisible):(s?(n.prop("selected",!0),this.options.multiple?n.prop("selected",!0):(this.options.selectedClass&&t(p).closest("li").removeClass(this.options.selectedClass),t(p).prop("checked",!1),a.prop("selected",!1),this.$button.click()),"active"===this.options.selectedClass&&a.closest("a").css("outline","")):n.prop("selected",!1),this.options.onChange(n,s)),this.$select.change(),this.updateButtonText(),this.updateSelectAll(),!this.options.preventInputChangeEvent&&void 0},this)),t("li a",this.$ul).on("mousedown",function(t){return!t.shiftKey&&void 0}),t("li a",this.$ul).on("click",t.proxy(function(e){e.stopPropagation();var i=t(e.target);if(e.shiftKey&&this.options.multiple){i.is("label")&&(e.preventDefault(),i=i.find("input"),i.prop("checked",!i.prop("checked")));var s=i.prop("checked")||!1;if(null!==this.lastToggledInput&&this.lastToggledInput!==i){var l=i.closest("li").index(),o=this.lastToggledInput.closest("li").index();if(l>o){var n=o;o=l,l=n}++o;var a=this.$ul.find("li").slice(l,o).find("input");a.prop("checked",s),this.options.selectedClass&&a.closest("li").toggleClass(this.options.selectedClass,s);for(var p=0,h=a.length;h>p;p++){var c=t(a[p]),r=this.getOptionByValue(c.val());r.prop("selected",s)}}i.trigger("change")}i.is("input")&&!i.closest("li").is(".multiselect-item")&&(this.lastToggledInput=i),i.blur()},this)),this.$container.off("keydown.multiselect").on("keydown.multiselect",t.proxy(function(e){if(!t('input[type="text"]',this.$container).is(":focus"))if(9===e.keyCode&&this.$container.hasClass("open"))this.$button.click();else{var i=t(this.$container).find("li:not(.divider):not(.disabled) a").filter(":visible");if(!i.length)return;var s=i.index(i.filter(":focus"));38===e.keyCode&&s>0?s--:40===e.keyCode&&s<i.length-1?s++:~s||(s=0);var l=i.eq(s);if(l.focus(),32===e.keyCode||13===e.keyCode){var o=l.find("input");o.prop("checked",!o.prop("checked")),o.change()}e.stopPropagation(),e.preventDefault()}},this)),this.options.enableClickableOptGroups&&this.options.multiple&&t("li.multiselect-group",this.$ul).on("click",t.proxy(function(e){e.stopPropagation();var i=t(e.target).parent(),s=i.nextUntil("li.multiselect-group"),l=s.filter(":visible:not(.disabled)"),o=!0,n=l.find("input"),a=[];n.each(function(){o=o&&t(this).prop("checked"),a.push(t(this).val())}),o?this.deselect(a,!1):this.select(a,!1),this.options.onChange(n,!o)},this)),this.options.enableCollapsibleOptGroups&&this.options.multiple&&(t("li.multiselect-group input",this.$ul).off(),t("li.multiselect-group",this.$ul).siblings().not("li.multiselect-group, li.multiselect-all",this.$ul).each(function(){t(this).toggleClass("hidden",!0)}),t("li.multiselect-group",this.$ul).on("click",t.proxy(function(t){t.stopPropagation()},this)),t("li.multiselect-group > a > b",this.$ul).on("click",t.proxy(function(e){e.stopPropagation();var i=t(e.target).closest("li"),s=i.nextUntil("li.multiselect-group"),l=!0;s.each(function(){l=l&&t(this).hasClass("hidden")}),s.toggleClass("hidden",!l)},this)),console.log(t("li.multiselect-group > a > input",this.$ul)),t("li.multiselect-group > a > input",this.$ul).on("change",t.proxy(function(e){e.stopPropagation();var i=t(e.target).closest("li"),s=i.nextUntil("li.multiselect-group",":not(.disabled)"),l=s.find("input"),o=!0;l.each(function(){o=o&&t(this).prop("checked")}),l.prop("checked",!o).trigger("change")},this)),t("li.multiselect-group",this.$ul).each(function(){var e=t(this).nextUntil("li.multiselect-group",":not(.disabled)"),i=e.find("input"),s=!0;i.each(function(){s=s&&t(this).prop("checked")}),t(this).find("input").prop("checked",s)}),t("li input",this.$ul).on("change",t.proxy(function(e){e.stopPropagation();var i=t(e.target).closest("li"),s=i.prevUntil("li.multiselect-group",":not(.disabled)"),l=i.nextUntil("li.multiselect-group",":not(.disabled)"),o=s.find("input"),n=l.find("input"),a=t(e.target).prop("checked");o.each(function(){a=a&&t(this).prop("checked")}),n.each(function(){a=a&&t(this).prop("checked")}),i.prevAll(".multiselect-group").find("input").prop("checked",a)},this)),t("li.multiselect-all",this.$ul).css("background","#f3f3f3").css("border-bottom","1px solid #eaeaea"),t("li.multiselect-group > a, li.multiselect-all > a > label.checkbox",this.$ul).css("padding","3px 20px 3px 35px"),t("li.multiselect-group > a > input",this.$ul).css("margin","4px 0px 5px -20px"))},createOptionValue:function(e){var i=t(e);i.is(":selected")&&i.prop("selected",!0);var s=this.options.optionLabel(e),l=this.options.optionClass(e),o=i.val(),n=this.options.multiple?"checkbox":"radio",a=t(this.options.templates.li),p=t("label",a);p.addClass(n),a.addClass(l),this.options.enableHTML?p.html(" "+s):p.text(" "+s);var h=t("<input/>").attr("type",n);this.options.checkboxName&&h.attr("name",this.options.checkboxName),p.prepend(h);var c=i.prop("selected")||!1;h.val(o),o===this.options.selectAllValue&&(a.addClass("multiselect-item multiselect-all"),h.parent().parent().addClass("multiselect-all")),p.attr("title",i.attr("title")),this.$ul.append(a),i.is(":disabled")&&h.attr("disabled","disabled").prop("disabled",!0).closest("a").attr("tabindex","-1").closest("li").addClass("disabled"),h.prop("checked",c),c&&this.options.selectedClass&&h.closest("li").addClass(this.options.selectedClass)},createDivider:function(){var e=t(this.options.templates.divider);this.$ul.append(e)},createOptgroup:function(e){if(this.options.enableCollapsibleOptGroups&&this.options.multiple){var i=t(e).attr("label"),s=t(e).attr("value"),l=t('<li class="multiselect-item multiselect-group"><a href="javascript:void(0);"><input type="checkbox" value="'+s+'"/><b> '+i+'<b class="caret"></b></b></a></li>');this.options.enableClickableOptGroups&&l.addClass("multiselect-group-clickable"),this.$ul.append(l),t(e).is(":disabled")&&l.addClass("disabled"),t("option",e).each(t.proxy(function(t,e){this.createOptionValue(e)},this))}else{var o=t(e).prop("label"),n=t(this.options.templates.liGroup);this.options.enableHTML?t("label",n).html(o):t("label",n).text(o),this.options.enableClickableOptGroups&&n.addClass("multiselect-group-clickable"),this.$ul.append(n),t(e).is(":disabled")&&n.addClass("disabled"),t("option",e).each(t.proxy(function(t,e){this.createOptionValue(e)},this))}},buildSelectAll:function(){"number"==typeof this.options.selectAllValue&&(this.options.selectAllValue=this.options.selectAllValue.toString());var e=this.hasSelectAll();if(!e&&this.options.includeSelectAllOption&&this.options.multiple&&t("option",this.$select).length>this.options.includeSelectAllIfMoreThan){this.options.includeSelectAllDivider&&this.$ul.prepend(t(this.options.templates.divider));var i=t(this.options.templates.li);t("label",i).addClass("checkbox"),this.options.enableHTML?t("label",i).html(" "+this.options.selectAllText):t("label",i).text(" "+this.options.selectAllText),t("label",i).prepend(this.options.selectAllName?'<input type="checkbox" name="'+this.options.selectAllName+'" />':'<input type="checkbox" />');var s=t("input",i);s.val(this.options.selectAllValue),i.addClass("multiselect-item multiselect-all"),s.parent().parent().addClass("multiselect-all"),this.$ul.prepend(i),s.prop("checked",!1)}},buildFilter:function(){if(this.options.enableFiltering||this.options.enableCaseInsensitiveFiltering){var e=Math.max(this.options.enableFiltering,this.options.enableCaseInsensitiveFiltering);if(this.$select.find("option").length>=e){if(this.$filter=t(this.options.templates.filter),t("input",this.$filter).attr("placeholder",this.options.filterPlaceholder),this.options.includeFilterClearBtn){var i=t(this.options.templates.filterClearBtn);i.on("click",t.proxy(function(){clearTimeout(this.searchTimeout),this.$filter.find(".multiselect-search").val(""),t("li",this.$ul).show().removeClass("filter-hidden"),this.updateSelectAll()},this)),this.$filter.find(".input-group").append(i)}this.$ul.prepend(this.$filter),this.$filter.val(this.query).on("click",function(t){t.stopPropagation()}).on("input keydown",t.proxy(function(e){13===e.which&&e.preventDefault(),clearTimeout(this.searchTimeout),this.searchTimeout=this.asyncFunction(t.proxy(function(){if(this.query!==e.target.value){this.query=e.target.value;var i,s;t.each(t("li",this.$ul),t.proxy(function(e,l){var o=t("input",l).length>0?t("input",l).val():"",n=t("label",l).text(),a="";if("text"===this.options.filterBehavior?a=n:"value"===this.options.filterBehavior?a=o:"both"===this.options.filterBehavior&&(a=n+"\n"+o),o!==this.options.selectAllValue&&n){var p=!1;if(this.options.enableCaseInsensitiveFiltering&&(a=a.toLowerCase(),this.query=this.query.toLowerCase()),this.options.enableFullValueFiltering&&"both"!==this.options.filterBehavior){var h=a.trim().substring(0,this.query.length);this.query.indexOf(h)>-1&&(p=!0)}else a.indexOf(this.query)>-1&&(p=!0);t(l).toggle(p).toggleClass("filter-hidden",!p),t(l).hasClass("multiselect-group")?(i=l,s=p):(p&&t(i).show().removeClass("filter-hidden"),!p&&s&&t(l).show().removeClass("filter-hidden"))}},this))}this.updateSelectAll()},this),300,this)},this))}}},destroy:function(){this.$container.remove(),this.$select.show(),this.$select.data("multiselect",null)},refresh:function(){var e=t.map(t("li input",this.$ul),t);t("option",this.$select).each(t.proxy(function(i,s){for(var l,o=t(s),n=o.val(),a=e.length;0<a--;)if(n===(l=e[a]).val()){o.is(":selected")?(l.prop("checked",!0),this.options.selectedClass&&l.closest("li").addClass(this.options.selectedClass)):(l.prop("checked",!1),this.options.selectedClass&&l.closest("li").removeClass(this.options.selectedClass)),o.is(":disabled")?l.attr("disabled","disabled").prop("disabled",!0).closest("li").addClass("disabled"):l.prop("disabled",!1).closest("li").removeClass("disabled");break}},this)),this.updateButtonText(),this.updateSelectAll()},select:function(e,i){t.isArray(e)||(e=[e]);for(var s=0;s<e.length;s++){var l=e[s];if(null!==l&&void 0!==l){var o=this.getOptionByValue(l),n=this.getInputByValue(l);void 0!==o&&void 0!==n&&(this.options.multiple||this.deselectAll(!1),this.options.selectedClass&&n.closest("li").addClass(this.options.selectedClass),n.prop("checked",!0),o.prop("selected",!0),i&&this.options.onChange(o,!0))}}this.updateButtonText(),this.updateSelectAll()},clearSelection:function(){this.deselectAll(!1),this.updateButtonText(),this.updateSelectAll()},deselect:function(e,i){t.isArray(e)||(e=[e]);for(var s=0;s<e.length;s++){var l=e[s];if(null!==l&&void 0!==l){var o=this.getOptionByValue(l),n=this.getInputByValue(l);void 0!==o&&void 0!==n&&(this.options.selectedClass&&n.closest("li").removeClass(this.options.selectedClass),n.prop("checked",!1),o.prop("selected",!1),i&&this.options.onChange(o,!1))}}this.updateButtonText(),this.updateSelectAll()},selectAll:function(e,i){e=(!this.options.enableCollapsibleOptGroups||!this.options.multiple)&&e;var e="undefined"==typeof e||e,s=t("li input[type='checkbox']:enabled",this.$ul),l=s.filter(":visible"),o=s.length,n=l.length;if(e?(l.prop("checked",!0),t("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").addClass(this.options.selectedClass)):(s.prop("checked",!0),t("li:not(.divider):not(.disabled)",this.$ul).addClass(this.options.selectedClass)),o===n||e===!1)t("option:not([data-role='divider']):enabled",this.$select).prop("selected",!0);else{var a=l.map(function(){return t(this).val()}).get();t("option:enabled",this.$select).filter(function(){return-1!==t.inArray(t(this).val(),a)}).prop("selected",!0)}i&&this.options.onSelectAll()},deselectAll:function(e){if(e=(!this.options.enableCollapsibleOptGroups||!this.options.multiple)&&e,e="undefined"==typeof e||e){var i=t("li input[type='checkbox']:not(:disabled)",this.$ul).filter(":visible");i.prop("checked",!1);var s=i.map(function(){return t(this).val()}).get();t("option:enabled",this.$select).filter(function(){return-1!==t.inArray(t(this).val(),s)}).prop("selected",!1),this.options.selectedClass&&t("li:not(.divider):not(.disabled)",this.$ul).filter(":visible").removeClass(this.options.selectedClass)}else t("li input[type='checkbox']:enabled",this.$ul).prop("checked",!1),t("option:enabled",this.$select).prop("selected",!1),this.options.selectedClass&&t("li:not(.divider):not(.disabled)",this.$ul).removeClass(this.options.selectedClass)},rebuild:function(){this.$ul.html(""),this.options.multiple="multiple"===this.$select.attr("multiple"),this.buildSelectAll(),this.buildDropdownOptions(),this.buildFilter(),this.updateButtonText(),this.updateSelectAll(!0),this.options.disableIfEmpty&&t("option",this.$select).length<=0?this.disable():this.enable(),this.options.dropRight&&this.$ul.addClass("pull-right")},dataprovider:function(i){var s=0,l=this.$select.empty();t.each(i,function(i,o){var n;t.isArray(o.children)?(s++,n=t("<optgroup/>").attr({label:o.label||"Group "+s,disabled:!!o.disabled}),e(o.children,function(e){n.append(t("<option/>").attr({value:e.value,label:e.label||e.value,title:e.title,selected:!!e.selected,disabled:!!e.disabled}))})):(n=t("<option/>").attr({value:o.value,label:o.label||o.value,title:o.title,class:o.class,selected:!!o.selected,disabled:!!o.disabled}),n.text(o.label||o.value)),l.append(n)}),this.rebuild()},enable:function(){this.$select.prop("disabled",!1),this.$button.prop("disabled",!1).removeClass("disabled")},disable:function(){this.$select.prop("disabled",!0),this.$button.prop("disabled",!0).addClass("disabled")},setOptions:function(t){this.options=this.mergeOptions(t)},mergeOptions:function(e){return t.extend(!0,{},this.defaults,this.options,e)},hasSelectAll:function(){return t("li.multiselect-all",this.$ul).length>0},updateSelectAll:function(e){if(this.hasSelectAll()){var i=t("li:not(.multiselect-item):not(.filter-hidden) input:enabled",this.$ul),s=i.length,l=i.filter(":checked").length,o=t("li.multiselect-all",this.$ul),n=o.find("input");l>0&&l===s?(n.prop("checked",!0),o.addClass(this.options.selectedClass),this.options.onSelectAll(!0)):(n.prop("checked",!1),o.removeClass(this.options.selectedClass),0===l&&(e||this.options.onSelectAll(!1)))}},updateButtonText:function(){var e=this.getSelected();this.options.enableHTML?t(".multiselect .multiselect-selected-text",this.$container).html(this.options.buttonText(e,this.$select)):t(".multiselect .multiselect-selected-text",this.$container).text(this.options.buttonText(e,this.$select)),t(".multiselect",this.$container).attr("title",this.options.buttonTitle(e,this.$select))},getSelected:function(){return t("option",this.$select).filter(":selected")},getOptionByValue:function(e){for(var i=t("option",this.$select),s=e.toString(),l=0;l<i.length;l+=1){var o=i[l];if(o.value===s)return t(o)}},getInputByValue:function(e){for(var i=t("li input",this.$ul),s=e.toString(),l=0;l<i.length;l+=1){var o=i[l];if(o.value===s)return t(o)}},updateOriginalOptions:function(){this.originalOptions=this.$select.clone()[0].options},asyncFunction:function(t,e,i){var s=Array.prototype.slice.call(arguments,3);return setTimeout(function(){t.apply(i||window,s)},e)},setAllSelectedText:function(t){this.options.allSelectedText=t,this.updateButtonText()}},t.fn.multiselect=function(e,s,l){return this.each(function(){var o=t(this).data("multiselect"),n="object"==typeof e&&e;o||(o=new i(this,n),t(this).data("multiselect",o)),"string"==typeof e&&(o[e](s,l),"destroy"===e&&t(this).data("multiselect",!1))})},t.fn.multiselect.Constructor=i,t(function(){t("select[data-role=multiselect]").multiselect()})}(jQuery);

/*PRINTJS*/
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.printJS=t():e.printJS=t()}(window,function(){return function(n){var r={};function o(e){if(r[e])return r[e].exports;var t=r[e]={i:e,l:!1,exports:{}};return n[e].call(t.exports,t,t.exports,o),t.l=!0,t.exports}return o.m=n,o.c=r,o.d=function(e,t,n){o.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},o.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)o.d(n,r,function(e){return t[e]}.bind(null,r));return n},o.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return o.d(t,"a",t),t},o.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},o.p="",o(o.s=4)}([function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=n(2),i=(r=o)&&r.__esModule?r:{default:r},a=n(1);var l={send:function(r,e){document.getElementsByTagName("body")[0].appendChild(e);var o=document.getElementById(r.frameId);o.onload=function(){if("pdf"!==r.type){var e=o.contentWindow||o.contentDocument;if(e.document&&(e=e.document),e.body.appendChild(r.printableElement),"pdf"!==r.type&&r.style){var t=document.createElement("style");t.innerHTML=r.style,e.head.appendChild(t)}var n=e.getElementsByTagName("img");0<n.length?function(e){var t=[],n=!0,r=!1,o=void 0;try{for(var i,a=e[Symbol.iterator]();!(n=(i=a.next()).done);n=!0){var l=i.value;t.push(u(l))}}catch(e){r=!0,o=e}finally{try{!n&&a.return&&a.return()}finally{if(r)throw o}}return Promise.all(t)}(n).then(function(){return d(o,r)}):d(o,r)}else d(o,r)}}};function d(t,n){try{if(t.focus(),i.default.isEdge()||i.default.isIE())try{t.contentWindow.document.execCommand("print",!1,null)}catch(e){t.contentWindow.print()}else t.contentWindow.print()}catch(e){n.onError(e)}finally{(0,a.cleanUp)(n)}}function u(n){return new Promise(function(t){!function e(){n&&void 0!==n.naturalWidth&&0!==n.naturalWidth&&n.complete?t():setTimeout(e,500)}()})}t.default=l},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.addWrapper=function(e,t){return'<div style="font-family:'+t.font+" !important; font-size: "+t.font_size+' !important; width:100%;">'+e+"</div>"},t.capitalizePrint=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},t.collectStyles=f,t.loopNodesCollectStyles=function e(t,n){for(var r=0;r<t.length;r++){var o=t[r];if(-1===n.ignoreElements.indexOf(o.getAttribute("id"))){var i=o.tagName;if("INPUT"===i||"TEXTAREA"===i||"SELECT"===i){var a=f(o,n),l=o.parentNode,d="SELECT"===i?document.createTextNode(o.options[o.selectedIndex].text):document.createTextNode(o.value),u=document.createElement("div");u.appendChild(d),u.setAttribute("style",a),l.appendChild(u),l.removeChild(o)}else o.setAttribute("style",f(o,n));var c=o.children;c&&c.length&&e(c,n)}else o.parentNode.removeChild(o)}},t.addHeader=function(e,t){var n=document.createElement("div");if(l(t.header))n.innerHTML=t.header;else{var r=document.createElement("h1"),o=document.createTextNode(t.header);r.appendChild(o),r.setAttribute("style",t.headerStyle),n.appendChild(r)}e.insertBefore(n,e.childNodes[0])},t.cleanUp=function(t){t.showModal&&r.default.close();t.onLoadingEnd&&t.onLoadingEnd();(t.showModal||t.onLoadingStart)&&window.URL.revokeObjectURL(t.printable);if(t.onPrintDialogClose){var n="mouseover";(o.default.isChrome()||o.default.isFirefox())&&(n="focus");window.addEventListener(n,function e(){window.removeEventListener(n,e),t.onPrintDialogClose()})}},t.isRawHTML=l;var r=a(n(3)),o=a(n(2));function a(e){return e&&e.__esModule?e:{default:e}}function f(e,t){var n=document.defaultView||window,r="",o=n.getComputedStyle(e,"");return Object.keys(o).map(function(e){(-1!==t.targetStyles.indexOf("*")||-1!==t.targetStyle.indexOf(o[e])||function(e,t){for(var n=0;n<e.length;n++)if("object"===(void 0===t?"undefined":i(t))&&-1!==t.indexOf(e[n]))return!0;return!1}(t.targetStyles,o[e]))&&o.getPropertyValue(o[e])&&(r+=o[e]+":"+o.getPropertyValue(o[e])+";")}),r+="max-width: "+t.maxWidth+"px !important;"+t.font_size+" !important;"}function l(e){return new RegExp("<([A-Za-z][A-Za-z0-9]*)\\b[^>]*>(.*?)</\\1>").test(e)}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r={isFirefox:function(){return"undefined"!=typeof InstallTrigger},isIE:function(){return-1!==navigator.userAgent.indexOf("MSIE")||!!document.documentMode},isEdge:function(){return!r.isIE()&&!!window.StyleMedia},isChrome:function(){return!!(0<arguments.length&&void 0!==arguments[0]?arguments[0]:window).chrome},isSafari:function(){return 0<Object.prototype.toString.call(window.HTMLElement).indexOf("Constructor")||-1!==navigator.userAgent.toLowerCase().indexOf("safari")}};t.default=r},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a={show:function(e){var t=document.createElement("div");t.setAttribute("style","font-family:sans-serif; display:table; text-align:center; font-weight:300; font-size:30px; left:0; top:0;position:fixed; z-index: 9990;color: #0460B5; width: 100%; height: 100%; background-color:rgba(255,255,255,.9);transition: opacity .3s ease;"),t.setAttribute("id","printJS-Modal");var n=document.createElement("div");n.setAttribute("style","display:table-cell; vertical-align:middle; padding-bottom:100px;");var r=document.createElement("div");r.setAttribute("class","printClose"),r.setAttribute("id","printClose"),n.appendChild(r);var o=document.createElement("span");o.setAttribute("class","printSpinner"),n.appendChild(o);var i=document.createTextNode(e.modalMessage);n.appendChild(i),t.appendChild(n),document.getElementsByTagName("body")[0].appendChild(t),document.getElementById("printClose").addEventListener("click",function(){a.close()})},close:function(){var e=document.getElementById("printJS-Modal");e.parentNode.removeChild(e)}};t.default=a},function(e,t,n){e.exports=n(5)},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),n(6);var r,o=n(7);var i=((r=o)&&r.__esModule?r:{default:r}).default.init;"undefined"!=typeof window&&(window.printJS=i),t.default=i},function(e,t,n){},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a=r(n(2)),l=r(n(3)),d=r(n(8)),u=r(n(9)),c=r(n(10)),f=r(n(11)),s=r(n(12));function r(e){return e&&e.__esModule?e:{default:e}}var p=["pdf","html","image","json","raw-html"];t.default={init:function(){var t={printable:null,fallbackPrintable:null,type:"pdf",header:null,headerStyle:"font-weight: 300;",maxWidth:800,font:"TimesNewRoman",font_size:"12pt",honorMarginPadding:!0,honorColor:!1,properties:null,gridHeaderStyle:"font-weight: bold; padding: 5px; border: 1px solid #dddddd;",gridStyle:"border: 1px solid lightgray; margin-bottom: -1px;",showModal:!1,onError:function(e){throw e},onLoadingStart:null,onLoadingEnd:null,onPrintDialogClose:null,onPdfOpen:null,onBrowserIncompatible:function(){return!0},modalMessage:"Retrieving Document...",frameId:"printJS",printableElement:null,documentTitle:"Document",targetStyle:["clear","display","width","min-width","height","min-height","max-height"],targetStyles:["border","box","break","text-decoration"],ignoreElements:[],imageStyle:"max-width: 100%;",repeatTableHeader:!0,css:null,style:null,scanStyles:!0,base64:!1},e=arguments[0];if(void 0===e)throw new Error("printJS expects at least 1 attribute.");switch(void 0===e?"undefined":i(e)){case"string":t.printable=encodeURI(e),t.fallbackPrintable=t.printable,t.type=arguments[1]||t.type;break;case"object":for(var n in t.printable=e.printable,t.base64=void 0!==e.base64,t.fallbackPrintable=void 0!==e.fallbackPrintable?e.fallbackPrintable:t.printable,t.fallbackPrintable=t.base64?"data:application/pdf;base64,"+t.fallbackPrintable:t.fallbackPrintable,t)"printable"!==n&&"fallbackPrintable"!==n&&"base64"!==n&&(t[n]=void 0!==e[n]?e[n]:t[n]);break;default:throw new Error('Unexpected argument type! Expected "string" or "object", got '+(void 0===e?"undefined":i(e)))}if(!t.printable)throw new Error("Missing printable information.");if(!t.type||"string"!=typeof t.type||-1===p.indexOf(t.type.toLowerCase()))throw new Error("Invalid print type. Available types are: pdf, html, image and json.");t.showModal&&l.default.show(t),t.onLoadingStart&&t.onLoadingStart();var r=document.getElementById(t.frameId);r&&r.parentNode.removeChild(r);var o=void 0;switch((o=document.createElement("iframe")).setAttribute("style","visibility: hidden; height: 0; width: 0; position: absolute;"),o.setAttribute("id",t.frameId),"pdf"!==t.type&&(o.srcdoc="<html><head><title>"+t.documentTitle+"</title>",null!==t.css&&(Array.isArray(t.css)||(t.css=[t.css]),t.css.forEach(function(e){o.srcdoc+='<link rel="stylesheet" href="'+e+'">'})),o.srcdoc+="</head><body></body></html>"),t.type){case"pdf":if(a.default.isFirefox()||a.default.isEdge()||a.default.isIE())try{if(console.info("PrintJS currently doesn't support PDF printing in Firefox, Internet Explorer and Edge."),!0===t.onBrowserIncompatible())window.open(t.fallbackPrintable,"_blank").focus(),t.onPdfOpen&&t.onPdfOpen()}catch(e){t.onError(e)}finally{t.showModal&&l.default.close(),t.onLoadingEnd&&t.onLoadingEnd()}else d.default.print(t,o);break;case"image":f.default.print(t,o);break;case"html":u.default.print(t,o);break;case"raw-html":c.default.print(t,o);break;case"json":s.default.print(t,o)}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r},a=n(1);function l(e,t,n){var r=new window.Blob([n],{type:"application/pdf"});r=window.URL.createObjectURL(r),t.setAttribute("src",r),i.default.send(e,t)}t.default={print:function(e,t){if(e.base64){var n=Uint8Array.from(atob(e.printable),function(e){return e.charCodeAt(0)});l(e,t,n)}else{e.printable=/^(blob|http)/i.test(e.printable)?e.printable:window.location.origin+("/"!==e.printable.charAt(0)?"/"+e.printable:e.printable);var r=new window.XMLHttpRequest;r.responseType="arraybuffer",r.addEventListener("load",function(){if(-1===[200,201].indexOf(r.status))return(0,a.cleanUp)(e),void e.onError(r.statusText);l(e,t,r.response)}),r.open("GET",e.printable,!0),r.send()}}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=n(1),i=n(0),a=(r=i)&&r.__esModule?r:{default:r};t.default={print:function(e,t){var n=document.getElementById(e.printable);n?(e.printableElement=function e(t,n){var r=t.cloneNode();var o=!0;var i=!1;var a=void 0;try{for(var l,d=t.childNodes[Symbol.iterator]();!(o=(l=d.next()).done);o=!0){var u=l.value;if(-1===n.ignoreElements.indexOf(u.id)){var c=e(u,n);r.appendChild(c)}}}catch(e){i=!0,a=e}finally{try{!o&&d.return&&d.return()}finally{if(i)throw a}}switch(t.tagName){case"SELECT":r.value=t.value;break;case"CANVAS":r.getContext("2d").drawImage(t,0,0)}return r}(n,e),e.header&&(0,o.addHeader)(e.printableElement,e),a.default.send(e,t)):window.console.error("Invalid HTML element id: "+e.printable)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r};t.default={print:function(e,t){e.printableElement=document.createElement("div"),e.printableElement.setAttribute("style","width:100%"),e.printableElement.innerHTML=e.printable,i.default.send(e,t)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o=n(1),i=n(0),a=(r=i)&&r.__esModule?r:{default:r};t.default={print:function(r,e){r.printable.constructor!==Array&&(r.printable=[r.printable]),r.printableElement=document.createElement("div"),r.printable.forEach(function(e){var t=document.createElement("img");t.setAttribute("style",r.imageStyle),t.src=e;var n=document.createElement("div");n.appendChild(t),r.printableElement.appendChild(n)}),r.header&&(0,o.addHeader)(r.printableElement,r),a.default.send(r,e)}}},function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r,o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c=n(1),i=n(0),a=(r=i)&&r.__esModule?r:{default:r};t.default={print:function(t,e){if("object"!==o(t.printable))throw new Error("Invalid javascript data object (JSON).");if("boolean"!=typeof t.repeatTableHeader)throw new Error("Invalid value for repeatTableHeader attribute (JSON).");if(!t.properties||!Array.isArray(t.properties))throw new Error("Invalid properties array for your JSON data.");t.properties=t.properties.map(function(e){return{field:"object"===(void 0===e?"undefined":o(e))?e.field:e,displayName:"object"===(void 0===e?"undefined":o(e))?e.displayName:e,columnSize:"object"===(void 0===e?"undefined":o(e))&&e.columnSize?e.columnSize+";":100/t.properties.length+"%;"}}),t.printableElement=document.createElement("div"),t.header&&(0,c.addHeader)(t.printableElement,t),t.printableElement.innerHTML+=function(e){var t=e.printable,n=e.properties,r='<table style="border-collapse: collapse; width: 100%;">';e.repeatTableHeader&&(r+="<thead>");r+="<tr>";for(var o=0;o<n.length;o++)r+='<th style="width:'+n[o].columnSize+";"+e.gridHeaderStyle+'">'+(0,c.capitalizePrint)(n[o].displayName)+"</th>";r+="</tr>",e.repeatTableHeader&&(r+="</thead>");r+="<tbody>";for(var i=0;i<t.length;i++){r+="<tr>";for(var a=0;a<n.length;a++){var l=t[i],d=n[a].field.split(".");if(1<d.length)for(var u=0;u<d.length;u++)l=l[d[u]];else l=l[n[a].field];r+='<td style="width:'+n[a].columnSize+e.gridStyle+'">'+l+"</td>"}r+="</tr>"}return r+="</tbody></table>"}(t),a.default.send(t,e)}}}]).default});



// Ready State?
window.aslGoogleMapsReady = false;

// Google Maps Library CallBack
function asl_init_callback() {

    /* jQuery(document).ready(function(){

        // is ready!
        window.aslGoogleMapsReady = true;

        // Dispatch the event
        document.dispatchEvent(new Event('aslGoogleMapsLoaded'));
    }); */
}


// CMPLZ event
/*
document.addEventListener('cmplz_event_marketing', function () {
    if (typeof asl_init_callback === 'function') {
        asl_init_callback();
    }
});

// Initialization of the Google Scripts
function aslInitializeWhenGAPIReady(callback) {

    // Ensure the callback is a function
    if (typeof callback !== 'function') {
        console.error('Provided callback is not a function');
        return;
    }

    // Check the flag
    if (window.aslGoogleMapsReady) {
        // If already loaded, execute the callback immediately
        callback();
    } else {
        // Otherwise, wait for the event
        document.addEventListener('aslGoogleMapsLoaded', callback);
    }
}*/