[data-simplebar]{align-content:flex-start;align-items:flex-start;flex-direction:column;flex-wrap:wrap;justify-content:flex-start;position:relative}.simplebar-wrapper{height:inherit;max-height:inherit;max-width:inherit;overflow:hidden;width:inherit}.simplebar-mask{direction:inherit;height:auto!important;overflow:hidden;width:auto!important;z-index:0}.simplebar-mask,.simplebar-offset{bottom:0;left:0;margin:0;padding:0;position:absolute;right:0;top:0}.simplebar-offset{-webkit-overflow-scrolling:touch;box-sizing:inherit!important;direction:inherit!important;resize:none!important}.simplebar-content-wrapper{-ms-overflow-style:none;box-sizing:border-box!important;direction:inherit;display:block;height:100%;max-height:100%;max-width:100%;overflow:auto;position:relative;scrollbar-width:none;width:auto}.simplebar-content-wrapper::-webkit-scrollbar,.simplebar-hide-scrollbar::-webkit-scrollbar{display:none;height:0;width:0}.simplebar-content:after,.simplebar-content:before{content:" ";display:table}.simplebar-placeholder{max-height:100%;max-width:100%;pointer-events:none;width:100%}.simplebar-height-auto-observer-wrapper{box-sizing:inherit!important;flex-basis:0%;flex-grow:inherit;flex-shrink:0;float:left;height:100%;margin:0;max-height:1px;max-width:1px;overflow:hidden;padding:0;pointer-events:none;position:relative;width:100%;z-index:-1}.simplebar-height-auto-observer{box-sizing:inherit;display:block;height:1000%;left:0;min-height:1px;min-width:1px;opacity:0;top:0;width:1000%;z-index:-1}.simplebar-height-auto-observer,.simplebar-track{overflow:hidden;pointer-events:none;position:absolute}.simplebar-track{bottom:0;right:0;z-index:1}[data-simplebar].simplebar-dragging,[data-simplebar].simplebar-dragging .simplebar-content{-webkit-touch-callout:none;pointer-events:none;-webkit-user-select:none;-moz-user-select:none;user-select:none}[data-simplebar].simplebar-dragging .simplebar-track{pointer-events:all}.simplebar-scrollbar{left:0;min-height:10px;position:absolute;right:0}.simplebar-scrollbar:before{background:#000;border-radius:7px;content:"";opacity:0;position:absolute;transition:opacity .2s linear .5s}.simplebar-scrollbar.simplebar-visible:before{opacity:.5;transition-delay:0s;transition-duration:0s}.simplebar-track.simplebar-vertical{top:0;width:11px}.simplebar-scrollbar:before{bottom:2px;left:2px;right:2px;top:2px}.simplebar-track.simplebar-horizontal{height:11px;left:0}.simplebar-track.simplebar-horizontal .simplebar-scrollbar{bottom:0;left:0;min-height:0;min-width:10px;right:auto;top:0;width:auto}[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical{left:0;right:auto}.simplebar-dummy-scrollbar-size{-ms-overflow-style:scrollbar!important;direction:rtl;height:500px;opacity:0;overflow-x:scroll;overflow-y:hidden;position:fixed;visibility:hidden;width:500px}.simplebar-dummy-scrollbar-size>div{height:200%;margin:10px 0;width:200%}.simplebar-hide-scrollbar{-ms-overflow-style:none;left:0;overflow-y:scroll;position:fixed;scrollbar-width:none;visibility:hidden}:root{--ag-form-margin:auto auto}.age-restriction{height:calc(100vh - var(--ag-vh-offset, 0px));overflow:var(--ag-restriction-overflow,hidden);position:relative}.age-gate{background-color:var(--ag-form-background,#fff);border:var(--ag-form-border,0);border-radius:var(--ag-form-radius,0);border-width:var(--ag-form-border-width,0);box-shadow:var(--ag-form-shadow,none);color:var(--ag-text-color,#333);margin:var(--ag-form-margin,auto);max-height:var(--ag-form-max-height,calc(94vh - var(--ag-vh-offset, 0px)));max-width:var(--ag-form-max-width,500px);overflow-y:auto;padding:var(--ag-form-padding,20px);position:relative;scrollbar-width:var(--ag-scrollbar-display,none);width:100%;z-index:var(--ag-form-z-index,10000)}.age-gate::-webkit-scrollbar{display:var(--ag-scrollbar-display,none)}.age-gate *{box-sizing:border-box}.age-gate .simplebar-scrollbar:before{background-color:var(--ag-scrollbar,#000)}.age-gate h1:after,.age-gate h1:before,.age-gate h2:after,.age-gate h2:before,.age-gate h3:after,.age-gate h3:before,.age-gate h4:after,.age-gate h4:before,.age-gate h5:after,.age-gate h5:before,.age-gate h6:after,.age-gate h6:before{content:none}.age-gate-additional-information,.age-gate__additional-information{font-size:var(--ag-text-additional-size,12px);margin:var(--ag-text-additional-margin,0);padding:var(--ag-text-additional-padding,0)}.age-gate-additional-information p:last-of-type,.age-gate__additional-information p:last-of-type{margin-bottom:0}.age-gate-background-color,.age-gate__background-color{-webkit-backdrop-filter:blur(var(--ag-blur));backdrop-filter:blur(var(--ag-blur));background:var(--ag-background-color,#fff);opacity:var(--ag-background-opacity,1);z-index:1}.age-gate-background,.age-gate-background-color,.age-gate__background,.age-gate__background-color{height:calc(100vh - var(--ag-vh-offset, 0px));left:0;position:fixed;top:0;width:var(--ag-overlay-width,100%)}.age-gate-background,.age-gate__background{background-image:var(--ag-background-image);background-position:var(--ag-background-image-position);background-repeat:var(--ag-background-image-repeat);background-size:var(--ag-background-image-size,cover);opacity:var(--ag-background-image-opacity);z-index:2}.age-gate-background iframe,.age-gate-background video,.age-gate__background iframe,.age-gate__background video{bottom:0;height:100%;left:0;-o-object-fit:cover;object-fit:cover;position:absolute;right:0;top:0;width:100%}.age-gate-buttons,.age-gate__buttons{align-items:center;display:flex;gap:var(--ag-button-gap,10px);justify-content:center}.age-gate input[type=text].age-gate-input,.age-gate input[type=text].age-gate__input{background:var(--ag-input-background-color,#fff);border:var(--ag-input-border,solid);border-radius:var(--ag-input-radius,3px);border-width:var(--ag-input-border-width,1px);color:var(--ag-input-color,#414141);font-size:var(--ag-input-text-size,1rem);padding:var(--ag-input-padding,.5rem 1rem);text-align:var(--ag-text-align);transition:all .3s;width:100%}.age-gate input[type=text].age-gate-input::-moz-placeholder,.age-gate input[type=text].age-gate__input::-moz-placeholder{color:var(--ag-input-placeholder-color)}.age-gate input[type=text].age-gate-input::placeholder,.age-gate input[type=text].age-gate__input::placeholder{color:var(--ag-input-placeholder-color)}.age-gate select.age-gate-select,.age-gate select.age-gate__region,.age-gate select.age-gate__select{background:var(--ag-input-background-color,#fff);border:var(--ag-input-border,solid);border-radius:var(--ag-input-radius,3px);border-width:var(--ag-input-border-width,1px);color:var(--ag-input-color,#414141);font-size:var(--ag-input-text-size,1rem);padding:var(--ag-input-padding,.5rem 1rem);text-align:var(--ag-text-align);width:100%}.age-gate select.age-gate__region{margin-bottom:1rem}.age-gate-label,.age-gate__label{display:var(--ag-label-display,block);margin:var(--ag-label-margin,0 0 .5rem)}.age-gate-error,.age-gate__error{color:var(--ag-text-error-color,#c00);font-weight:var(--ag-text-error-weight,bold)}.age-gate-error a,.age-gate__error a{color:currentColor;text-decoration:underline}.age-gate-errors,.age-gate__errors{padding:var(--ag-text-errors-padding,0 10px)}.age-gate-errors:empty,.age-gate__errors:empty{padding:0}.age-gate-extra,.age-gate__extra{margin:var(--ag-fields-margin,0 auto 1rem);max-width:var(--ag-fields-width,100%);width:100%}.age-gate-extra:empty,.age-gate__extra:empty{display:none}.age-gate-form-elements,.age-gate__form-elements{display:grid;gap:var(--ag-input-spacing,10px);grid-template-columns:repeat(3,1fr);list-style:none;margin:var(--ag-input-margin,0);padding:0}.age-gate-fields,.age-gate__fields{border-width:0;display:block;margin:var(--ag-fields-margin,0 auto 1rem);max-width:var(--ag-fields-width,100%);padding:0;width:100%}.age-gate--shortcode .age-gate-fields,.age-gate--shortcode .age-gate__fields,.age-gate-shortcode .age-gate-fields,.age-gate-shortcode .age-gate__fields{padding:0 20px 20px}@media screen and (min-width:768px){.age-gate--shortcode .age-gate-fields,.age-gate--shortcode .age-gate__fields,.age-gate-shortcode .age-gate-fields,.age-gate-shortcode .age-gate__fields{max-width:70%}}@media screen and (min-width:1024px){.age-gate--shortcode .age-gate-fields,.age-gate--shortcode .age-gate__fields,.age-gate-shortcode .age-gate-fields,.age-gate-shortcode .age-gate__fields{max-width:50%}}.age-gate-form,.age-gate__form{align-items:center;display:flex;flex-direction:column;height:100%;justify-content:center}.age-gate-heading-title,.age-gate__heading-title{font-size:var(--ag-text-heading-size,2rem);font-weight:var(--ag-text-heading-weight,700);margin:var(--ag-text-heading-margin,0 0 1rem)}.age-gate-headline,.age-gate__headline{font-size:var(--ag-text-headline-size,1.5rem);font-weight:var(--ag-text-headline-weight,700);margin:var(--ag-text-headline-margin,0 0 1rem)}.age-gate-restricted,.age-gate__restricted{height:calc(100vh - var(--ag-vh-offset, 0px));overflow:var(--ag-html-overflow,hidden);position:relative}.age-gate-loader,.age-gate__loader{align-items:center;background:rgb(0 0 0 / .4);color:var(--ag-loader-color,#000);display:none;height:calc(100vh - var(--ag-vh-offset, 0px));justify-content:center;left:0;position:fixed;top:0;width:100%;z-index:100000}.age-restriction--working .age-gate-loader,.age-restriction--working .age-gate__loader{display:flex}.age-gate-loader img,.age-gate-loader svg,.age-gate__loader img,.age-gate__loader svg{display:block;height:70px;width:70px}.age-gate-loading-icon,.age-gate__loading-icon{display:block;height:50px;width:50px}.age-gate-heading-title-logo,.age-gate__heading-title--logo{height:var(--ag-logo-height,auto);max-width:var(--ag-logo-max-width,100%)}.age-gate-remember-text,.age-gate__remember-text{font-size:var(--ag-remember-size,.9rem);font-weight:var(--ag-remember-weight,normal);margin-left:var(--ag-remember-spacing,5px)}.age-gate-remember-wrapper,.age-gate__remember-wrapper{margin:var(--ag-remember-margin,0 0 1rem)}.age-gate-remember,.age-gate__remember{align-items:center;cursor:pointer;display:inline-flex;justify-content:var(--ag-remember-align,center)}.age-gate-subheadline,.age-gate__subheadline{font-size:var(--ag-text-subheadline-size,1.2rem);font-weight:var(--ag-text-subheadline-weight,500);margin:var(--ag-text-subheadline-margin,0 0 1rem)}.age-gate-submit,.age-gate__submit{margin:var(--ag-submit-margin,0 0 1rem)}button.age-gate-button,button.age-gate-submit-no,button.age-gate-submit-yes,button.age-gate__button,button.age-gate__submit--no,button.age-gate__submit--yes{background-color:var(--ag-button-color-background,#333);border:var(--ag-button-border,0);border-radius:var(--ag-button-radius,3px);color:var(--ag-button-color-text,#fff);cursor:var(--ag-button-cursor,pointer);font-size:var(--ag-button-size,1rem);font-weight:var(--ag-button-weight,600);padding:var(--ag-button-padding,.5rem 1rem)}button.age-gate-button:active,button.age-gate-button:focus,button.age-gate-button:hover,button.age-gate-submit-no:active,button.age-gate-submit-no:focus,button.age-gate-submit-no:hover,button.age-gate-submit-yes:active,button.age-gate-submit-yes:focus,button.age-gate-submit-yes:hover,button.age-gate__button:active,button.age-gate__button:focus,button.age-gate__button:hover,button.age-gate__submit--no:active,button.age-gate__submit--no:focus,button.age-gate__submit--no:hover,button.age-gate__submit--yes:active,button.age-gate__submit--yes:focus,button.age-gate__submit--yes:hover{background-color:var(--ag-button-color-hover,#323232);border:var(--ag-button-hover-border,0);color:var(--ag-button-color-text-hover,#fff)}button.age-gate-submit-no,button.age-gate-submit-yes,button.age-gate__submit--no,button.age-gate__submit--yes{margin:var(--ag-button-margin,0)}.age-gate-wrapper,.age-gate__wrapper{box-sizing:border-box;display:var(--ag-form-display,flex);height:calc(100vh - var(--ag-vh-offset, 0px));left:var(--ag-form-left,0);overflow-y:var(--ag-form-overlow,auto);padding:var(--ag-wrapper-padding,20px);position:var(--ag-form-position,fixed);text-align:var(--ag-text-align,center);top:var(--ag-form-top,0);transition-duration:var(--ag-transition-duration,.3s);transition-property:all;transition-timing-function:var(--ag-transition-timing,ease);width:var(--ag-overlay-width,100%);z-index:var(--ag-form-z-index,10000)}.age-gate .age-gate-shortcode-inner,.age-gate .age-gate__shortcode__inner{align-items:center;background:hsl(0 0% 100% / .5);display:flex;flex-direction:column;height:100%;justify-content:center;padding:10px;text-align:center;width:100%}.age-gate--shortcode,.age-gate-shortcode{background-color:#fff0;background-size:cover;max-width:none;padding:0}.age-gate--fade,.age-gate.fade{opacity:0}.age-gate--slide-0,.age-gate--slide-up{transform:translateY(-100%)}.age-gate--slide-down{transform:translateY(100%)}.age-gate--slide-left{transform:translateX(-100%)}.age-gate--slide-right{transform:translateX(100%)}