!function(t,n){"use strict";var o=function(t,n){var o=t.find(".bdt-ep-accordion-container"),i=o.find(".bdt-ep-accordion"),e=o.find(".bdt-ep-accordion-item.bdt-open");if(o.length){var a=i.data("settings"),d=a.activeHash,c=a.hashTopOffset,r=a.hashScrollspyTime,l=a.activeScrollspy;a.closeAllItemsOnMobile&&window.matchMedia("(max-width: 767px)").matches&&(e.removeClass("bdt-open"),e.find(".bdt-ep-accordion-content").prop("hidden",!0)),null==l&&(l="no"),"yes"==d&&(n(window).on("load",(function(){"yes"==l?s(i,r,c):bdtUIkit.accordion(i).toggle(n('[data-title="'+window.location.hash.substring(1)+'"]').data("accordion-index"),!1)})),n(i).find(".bdt-ep-accordion-title").off("click").on("click",(function(t){window.location.hash=n.trim(n(this).attr("data-title")),s(i,r=1e3,c)})),n(window).on("hashchange",(function(t){s(i,r=1e3,c)})))}function s(t,o,i){if(window.location.hash&&n(t).find('[data-title="'+window.location.hash.substring(1)+'"]').length){var e=n('[data-title="'+window.location.hash.substring(1)+'"]').closest(t).attr("id");"yes"==l?n("html, body").animate({easing:"slow",scrollTop:n("#"+e).offset().top-i},o,(function(){})).promise().then((function(){bdtUIkit.accordion(t).toggle(n('[data-title="'+window.location.hash.substring(1)+'"]').data("accordion-index"),!1)})):bdtUIkit.accordion(t).toggle(n('[data-title="'+window.location.hash.substring(1)+'"]').data("accordion-index"),!0)}}};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/bdt-accordion.default",o),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-acf-accordion.default",o)}))}(jQuery,window.elementorFrontend);