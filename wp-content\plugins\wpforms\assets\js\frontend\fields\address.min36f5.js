((e,s)=>{let o={noStateCountries:[],init(){s(e).on("load",o.onLoad),s(document).on("wpformsRepeaterFieldCloneCreated",o.setChangeHandlers)},onLoad(){o.noStateCountries=wpforms_settings?.address_field?.list_countries_without_states||[],o.noStateCountries.length&&o.setChangeHandlers()},setChangeHandlers(){s(".wpforms-field-address").each(function(){var e=s(this).find("select.wpforms-field-address-country");e.length&&(o.handleCountryChange(e),e.off("change").on("change",function(){o.handleCountryChange(this)}))})},handleCountryChange(e){var e=s(e),t=e.closest(".wpforms-field").find(".wpforms-field-address-state"),n=t.closest(".wpforms-field-row");n.length&&(e=e.val(),o.handleStateInput(t,n,e))},handleStateInput(e,t,n){o.noStateCountries.includes(n)?(e.val("").prop("disabled",!0).prop("required",!1).on("change",function(){s(this).val("")}),t.addClass("wpforms-without-state")):(e.prop("disabled",!1).prop("required",t.find(".wpforms-first input").prop("required")).off("change"),t.removeClass("wpforms-without-state"))}};o.init(),o})(window,jQuery);