!function(n,e){"use strict";var t=function(n,e){var t=n.find(".bdt-ep-advanced-icon-box"),o=e(t).find(".bdt-ep-advanced-icon-box-separator-wrap > img");(t.length||o.length)&&epObserveTarget(n[0],(function(){bdtUIkit.svg(o,{strokeAnimation:!0})}))};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/bdt-advanced-icon-box.default",t)}))}(jQuery,window.elementorFrontend);