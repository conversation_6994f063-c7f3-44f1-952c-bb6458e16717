!function(e,n){"use strict";var t=function(e,n){var t=e.find(".bdt-ep-carousel");if(!t.length)return;var o=t.find(".swiper-carousel"),r=t.data("settings");const d=elementorFrontend.utils.swiper;!async function(){await new d(o,r);r.pauseOnHover&&n(o).hover((function(){this.swiper.autoplay.stop()}),(function(){this.swiper.autoplay.start()}))}()};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/bdt-carousel.default",t),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-carousel.bdt-alice",t),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-carousel.bdt-vertical",t),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-carousel.bdt-ramble",t)}))}(jQuery,window.elementorFrontend);