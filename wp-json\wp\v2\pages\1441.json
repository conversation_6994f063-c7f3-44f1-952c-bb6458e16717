{"id":1441,"date":"2024-12-18T18:56:43","date_gmt":"2024-12-18T18:56:43","guid":{"rendered":"https:\/\/www.puffmivape.com\/?page_id=1441"},"modified":"2025-05-26T20:04:26","modified_gmt":"2025-05-26T20:04:26","slug":"store-locator","status":"publish","type":"page","link":"https:\/\/www.puffmivape.com\/store-locator\/","title":{"rendered":"Find a Store"},"content":{"rendered":"\t\t<div data-elementor-type=\"wp-page\" data-elementor-id=\"1441\" class=\"elementor elementor-1441\" data-elementor-settings=\"{"element_pack_global_tooltip_width":{"unit":"px","size":"","sizes":[]},"element_pack_global_tooltip_width_tablet":{"unit":"px","size":"","sizes":[]},"element_pack_global_tooltip_width_mobile":{"unit":"px","size":"","sizes":[]},"element_pack_global_tooltip_padding":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true},"element_pack_global_tooltip_padding_tablet":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true},"element_pack_global_tooltip_padding_mobile":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true},"element_pack_global_tooltip_border_radius":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true},"element_pack_global_tooltip_border_radius_tablet":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true},"element_pack_global_tooltip_border_radius_mobile":{"unit":"px","top":"","right":"","bottom":"","left":"","isLinked":true}}\" data-elementor-post-type=\"page\">\n\t\t\t\t<div data-bdt-sticky=\"offset: 100;overflow-flip: true;media: 768\" class=\"bdt-sticky elementor-element elementor-element-7a38f96 e-con-full e-flex e-con e-parent\" data-id=\"7a38f96\" data-element_type=\"container\" data-settings=\"{"background_background":"classic"}\">\n\t\t\t\t<div class=\"elementor-element elementor-element-80436ed elementor-widget elementor-widget-shortcode\" data-id=\"80436ed\" data-element_type=\"widget\" data-widget_type=\"shortcode.default\">\n\t\t\t\t<div class=\"elementor-widget-container\">\n\t\t\t\t\t\t\t<div class=\"elementor-shortcode\"><style type=\"text\/css\">\n  body #asl-storelocator.asl-cont.asl-template-0 {--sl-font-size: 13px;--sl-title-size: 15px;--sl-btn-size: 13px;--sl-primary: #121212;--sl-header: #f6f6f6;--sl-header-color: #32373c;--sl-infobox-color: #555d66;--sl-infobox-bg: #ffffff;--sl-infobox-a: #39D51F;--sl-action-btn-color: #ffffff;--sl-action-btn-bg: #121212;--sl-color: #555d66;--sl-list-bg: #FFFFFF;--sl-list-title: #32373c;--sl-list-sub-title: #6a6a6a;--sl-highlighted: #F6F6F6;}.asl-p-cont .asl-print-btn,.asl-cont .asl-print-btn {display: none !important;}  #asl-storelocator.asl-cont .sl-main-cont .asl-panel.pol-lg-12 {order: 0;}\n  #asl-storelocator.asl-cont .sl-main-cont .asl-panel.pol-lg-12 .asl-panel-inner{ position: relative;height: 450px;}\n  .asl-cont .onoffswitch .onoffswitch-label .onoffswitch-switch:before {content: \"OPEN\" !important;}\n  .asl-cont .onoffswitch .onoffswitch-label .onoffswitch-switch:after {content: \"ALL\" !important;}\n  @media (max-width: 767px) {\n    #asl-storelocator.asl-cont .asl-panel {order: 0;}\n  }\n  .asl-cont.sl-search-only .Filter_section + .sl-row {display: none;}\n  .asl-cont .sl-hide-branches,\n  .asl-cont .sl-hide-branches:hover {color: #FFF !important; text-decoration: none !important;cursor: pointer;}\n<\/style>\n<div id=\"asl-storelocator\" class=\"storelocator-main asl-cont asl-template-0 asl-layout-0 asl-bg-0 map-full full-width sl-category-tabs  asl-text-1\">\n  <div class=\"asl-wrapper\">\n    <div class=\"sl-container-fluid\">\n             \n      <div class=\"sl-row Filter_section\">\n          <div class=\"pol-lg-4 pol-md-5 pol-sm-12 search_filter\">\n              <label class=\"mb-2\" for=\"auto-complete-search\">Search Location<\/label>\n              <div class=\"sl-search-group d-flex\">\n                <input type=\"text\" value=\"\" data-submit=\"disable\" id=\"auto-complete-search\" placeholder=\"Enter a Location\"  class=\"asl-search-address form-control isp_ignore\">\n                <button aria-label=\"Search Location\" title=\"Search Location\" type=\"button\" class=\"span-geo\"><i class=\"icon-search\"><\/i><\/button>\n              <\/div>\n          <\/div>\n          <div class=\"pol-lg-8 pol-md-7 pol-sm-12\">\n            <div class=\"sl-row\">\n              <div class=\"pol-sm-12 asl-advance-filters hide\">\n                <div class=\"sl-row\">\n                                                      <div class=\"pol-lg-4 pol-md-6 pol-sm-12  asl-tabs-ddl pol-12 pol-lg-12 pol-md-12 pol-sm-12 asl-ddl-filters asl-ddl-filter-cats\">\n                      <div class=\"asl-filter-cntrl\">\n                        <label class=\"asl-cntrl-lbl\" for=\"asl-categories\">Category<\/label>\n                        <div class=\"sl-dropdown-cont\" id=\"categories_filter\">\n                        <\/div>\n                      <\/div>\n                  <\/div>\n                                                                        <div class=\"pol-lg-4 pol-md-6 pol-sm-12 range_filter asl-ddl-filters hide\">\n                    <div class=\"rangeFilter asl-filter-cntrl\">\n                      <label for=\"asl-radius-slide\" class=\"asl-cntrl-lbl\">Distance Range<\/label>\n                      <input id=\"asl-radius-slide\" type=\"text\" class=\"span2\" \/>\n                      <span class=\"rad-unit\">Radius: <span id=\"asl-radius-input\"><\/span> <span id=\"asl-dist-unit\">Km<\/span><\/span>\n                    <\/div>\n                  <\/div>\n                  <div class=\"pol-lg-2 pol-md-3 pol-sm-12 Status_filter\">\n                    <div class=\"asl-filter-cntrl\">\n                      <label class=\"asl-cntrl-lbl\">Status<\/label>\n                      <div class=\"onoffswitch\">\n                        <input type=\"checkbox\" name=\"onoffswitch\" class=\"onoffswitch-checkbox\" id=\"asl-open-close\" checked>\n                        <label aria-label=\"Opened Stores\" title=\"Opened Stores\" class=\"onoffswitch-label\" for=\"asl-open-close\">\n                            <span class=\"onoffswitch-inner\"><\/span>\n                            <span class=\"onoffswitch-switch\"><\/span>\n                        <\/label>\n                      <\/div>\n                    <\/div>\n                  <\/div>\n                <\/div>\n              <\/div>\n            <\/div>\n          <\/div>\n      <\/div>\n            <div class=\"sl-row\">\n        <div class=\"pol-12\">\n            <div class=\"sl-main-cont\">\n                <div class=\"sl-row no-gutters sl-main-row\">\n                    <div id=\"asl-panel\" class=\"asl-panel pol-md-5 pol-lg-4 asl_locator-panel\">\n                        <div class=\"asl-overlay\" id=\"map-loading\">\n                            <div class=\"white\"><\/div>\n                            <div class=\"sl-loading\">\n                              <i class=\"animate-sl-spin icon-spin3\"><\/i>\n                              Loading...                            <\/div>\n                        <\/div>\n                                                <!-- list -->\n                        <div class=\"asl-panel-inner\">\n                            <div class=\"top-title Num_of_store\">\n                              <span><span class=\"sl-head-title\">Number Of Shops<\/span>: <span class=\"count-result\">0<\/span><\/span>\n                                                          <\/div>\n                            <div class=\"sl-main-cont-box\">\n                              <div id=\"asl-list\" class=\"sl-list-wrapper\">\n                                <ul id=\"p-statelist\" class=\"sl-list\">\n                                <\/ul>\n                              <\/div>\n                            <\/div>\n                        <\/div>\n\n                        <div class=\"directions-cont hide\">\n                            <div class=\"agile-modal-header\">\n                                <button type=\"button\" class=\"close\"><span aria-hidden=\"true\">\u00d7<\/span><\/button>\n                                <h4>Store Direction<\/h4>\n                            <\/div>\n                            <div class=\"rendered-directions\" id=\"asl-rendered-dir\" style=\"direction: ltr;\"><\/div>\n                        <\/div>\n                    <\/div>\n                    <div class=\"pol-md-7 pol-lg-8 asl-map\">\n                        <div class=\"map-image\">\n                            <div id=\"asl-map-canv\" class=\"asl-map-canv\"><\/div>\n                            <div id=\"agile-modal-direction\" class=\"agile-modal fade\">\n    <div class=\"agile-modal-backdrop-in\"><\/div>\n    <div class=\"agile-modal-dialog in\">\n        <div class=\"agile-modal-content\">\n            <div class=\"sl-form-group d-flex justify-content-between\">\n                <h4 >GET DIRECTIONS<\/h4>\n                <button type=\"button\" class=\"close-directions sl-close\" data-dismiss=\"agile-modal\" aria-label=\"Close\">&times;<\/button>\n            <\/div>\n            <div class=\"sl-form-group\">\n                <label for=\"frm-lbl\">From:<\/label>\n                <input type=\"text\" class=\"form-control frm-place\" id=\"frm-lbl\" placeholder=\"Enter a Location\">\n            <\/div>\n            <div class=\"sl-form-group\">\n                <label for=\"to-lbl\">To:<\/label>\n                <input readonly=\"true\" type=\"text\"  class=\"directions-to form-control\" id=\"to-lbl\" placeholder=\"Prepopulated Destination Address\">\n            <\/div>\n            <div class=\"sl-form-group mb-0\">\n                <label for=\"rbtn-km\" class=\"checkbox-inline\">\n                    <input type=\"radio\" name=\"dist-type\"  id=\"rbtn-km\" value=\"0\"> Km                <\/label>\n                <label for=\"rbtn-mile\" class=\"checkbox-inline\">\n                    <input type=\"radio\" name=\"dist-type\" checked id=\"rbtn-mile\" value=\"1\"> Miles                <\/label>\n            <\/div>\n            <div class=\"sl-form-group mb-0\">\n                <button type=\"submit\" class=\"btn btn-default btn-submit\">GET DIRECTIONS<\/button>\n            <\/div>\n        <\/div>\n    <\/div>\n<\/div>\n\n<div id=\"asl-geolocation-agile-modal\" class=\"agile-modal fade\">\n  <div class=\"agile-modal-backdrop-in\"><\/div>\n  <div class=\"agile-modal-dialog in\">\n    <div class=\"agile-modal-content\">\n            <div class=\"sl-form-group d-flex justify-content-between\">\n        <h4>LOCATE YOUR GEOPOSITION<\/h4>\n        <button type=\"button\" class=\"close-directions sl-close\" data-dismiss=\"agile-modal\" aria-label=\"Close\">&times;<\/button>\n      <\/div>\n      <div class=\"sl-form-group\">\n        <div class=\"sl-row\">\n        <div class=\"pol-lg-12 mb-2\">\n          <input type=\"text\" class=\"form-control\" id=\"asl-current-loc\" placeholder=\"Your Address\">\n        <\/div>\n        <div class=\"pol-lg-12\">\n          <button type=\"button\" id=\"asl-btn-locate\" class=\"btn btn-block btn-default\">LOCATE<\/button>\n        <\/div>\n        <\/div>\n      <\/div>\n          <\/div>\n  <\/div>\n<\/div>\n\n<div id=\"asl-desc-agile-modal\" class=\"agile-modal fade\">\n  <div class=\"agile-modal-backdrop-in\"><\/div>\n  <div class=\"agile-modal-dialog in\">\n    <div class=\"agile-modal-content\">\n      <div class=\"sl-row\">\n        <div class=\"pol-md-12\">\n          <div class=\"sl-form-group d-flex justify-content-between\">\n            <h4 class=\"sl-title\">Description<\/h4>\n            <button type=\"button\" class=\"close-directions sl-close\" data-dismiss=\"agile-modal\" aria-label=\"Close\">&times;<\/button>\n          <\/div>\n          <div class=\"sl-desc\"><\/div>\n        <\/div>\n      <\/div>\n    <\/div>\n  <\/div>\n<\/div>                        <\/div>\n                    <\/div>\n                <\/div>\n            <\/div>\n        <\/div>\n      <\/div>\n    <\/div>\n      <\/div>\n<\/div>\n<!-- This plugin is developed by \"Agile Store Locator for WordPress\" https:\/\/agilestorelocator.com --><\/div>\n\t\t\t\t\t\t<\/div>\n\t\t\t\t<\/div>\n\t\t\t\t<\/div>\n\t\t\t\t<\/div>\n\t\t","protected":false},"excerpt":{"rendered":"","protected":false},"author":1,"featured_media":0,"parent":0,"menu_order":0,"comment_status":"closed","ping_status":"closed","template":"","meta":{"_seopress_robots_primary_cat":"","_seopress_titles_title":"","_seopress_titles_desc":"","_seopress_robots_index":"","_searchwp_excluded":"","footnotes":""},"class_list":["post-1441","page","type-page","status-publish","hentry"],"_links":{"self":[{"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/pages\/1441","targetHints":{"allow":["GET"]}}],"collection":[{"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/pages"}],"about":[{"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/types\/page"}],"author":[{"embeddable":true,"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/users\/1"}],"replies":[{"embeddable":true,"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/comments?post=1441"}],"version-history":[{"count":5,"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/pages\/1441\/revisions"}],"predecessor-version":[{"id":3623,"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/pages\/1441\/revisions\/3623"}],"wp:attachment":[{"href":"https:\/\/www.puffmivape.com\/wp-json\/wp\/v2\/media?parent=1441"}],"curies":[{"name":"wp","href":"https:\/\/api.w.org\/{rel}","templated":true}]}}