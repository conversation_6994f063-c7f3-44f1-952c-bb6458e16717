window.WPFormsPhoneField=window.WPFormsPhoneField||((r,o,a)=>{let l={init(){a(r).on("wpformsReady",l.ready)},ready(){l.loadValidation(),l.loadSmartField(),l.bindSmartField(),a(".wpforms-smart-phone-field").each(function(){l.repairSmartHiddenField(a(this))})},loadValidation(){void 0!==a.fn.validate&&(a.validator.addMethod("us-phone-field",function(t,e){return!t.match(/[^\d()\-+\s]/)&&(this.optional(e)||10===t.replace(/\D/g,"").length)},wpforms_settings.val_phone),a.validator.addMethod("int-phone-field",function(t,e){return!t.match(/[^\d()\-+\s]/)&&(this.optional(e)||0<t.replace(/\D/g,"").length)},wpforms_settings.val_phone),void 0!==o.intlTelInput)&&a.validator.addMethod("smart-phone-field",function(t,e){var n;return!t.match(/[^\d()\-+\s]/)&&(t=o.intlTelInput?.getInstance(e),n=a(e).triggerHandler("validate"),this.optional(e)||t?.isValidNumberPrecise()||n)},wpforms_settings.val_phone)},loadSmartField(t=null){void 0!==o.intlTelInput&&(l.loadJqueryIntlTelInput(),(t=t?.length?t:a(r)).find(".wpforms-smart-phone-field").each(function(t,e){e=a(e);if(e.parents(".elementor-location-popup").is(":hidden"))return!1;l.initSmartField(e,{})}))},loadJqueryIntlTelInput(){void 0===a.fn.intlTelInput&&a.fn.extend({intlTelInput(r){var t=a(this);if(void 0===r||"object"==typeof r)return r.preferredCountries&&(r.countryOrder=r.preferredCountries),t.each(function(){var t=a(this);l.initSmartField(t,r)});if("string"==typeof r||"_"!==r[0]){let n=r,i=this;return t.each(function(){var t=a(this),e=t.data("plugin_intlTelInput");"function"==typeof e[n]&&(i=e[n](),"destroy"===r)&&t.removeData("plugin_intlTelInput")}),i}}})},initSmartField(n,i){if("object"!=typeof n.data("plugin_intlTelInput")){i=0<Object.keys(i).length?i:l.getDefaultSmartFieldOptions();let t=n.closest(".wpforms-field-phone").data("field-id"),e=(n.attr("name","wpf-temp-wpforms[fields]["+t+"]"),n.addClass("wpforms-input-temp-name"),i.hiddenInput=function(){return{phone:"wpforms[fields]["+t+"]"}},o.intlTelInput(n.get(0),i));n.on("validate",function(){return e.isValidNumber(e.getNumber())}),n.data("plugin_intlTelInput",e);i=function(){var t=n.data("plugin_intlTelInput");n.siblings('input[type="hidden"]').val(t.getNumber())};n.on("blur input",i),a(r).ready(i)}},bindSmartField(){a(".wpforms-form").on("wpformsBeforeFormSubmit",function(){var t=a(this).find(".wpforms-smart-phone-field");t.each(function(){l.repairSmartHiddenField(a(this))}),t.trigger("input")})},repairSmartHiddenField(n){var i=n.closest(".wpforms-field-phone").data("field-id");if(!a('[name="wpforms[fields]['+i+']"]').length){i=n.data("plugin_intlTelInput");let t=n.val(),e={};i&&(e=i.d||i.options||{},t=i.getNumber(),i.destroy()),n.removeData("plugin_intlTelInput"),n.val(t),l.initSmartField(n,e)}},getDefaultSmartFieldOptions(){var t,e={countrySearch:!1,fixDropdownWidth:!1,countryOrder:["us","gb"],countryListAriaLabel:wpforms_settings.country_list_label,validationNumberTypes:["FIXED_LINE_OR_MOBILE"]};wpforms_settings.gdpr||(e.geoIpLookup=l.currentIpToCountry);let n;if(wpforms_settings.gdpr&&(t=l.mapLanguageToIso(l.getFirstBrowserLanguage()),n=-1<t.indexOf("-")?t.split("-").pop():t),n){let t=o.intlTelInput?.getCountryData();t=t.filter(function(t){return t.iso2===n.toLowerCase()}),n=t.length?n:""}return e.initialCountry=wpforms_settings.gdpr&&n?n.toLowerCase():"auto",e},getFirstBrowserLanguage(){var t=o.navigator,e=["language","browserLanguage","systemLanguage","userLanguage"];let n,i;if(Array.isArray(t.languages))for(n=0;n<t.languages.length;n++)if((i=t.languages[n])&&i.length)return i;for(n=0;n<e.length;n++)if((i=t[e[n]])&&i.length)return i;return""},mapLanguageToIso(t){return{ar:"ar-SA",bg:"bg-BG",ca:"ca-ES",cs:"cs-CZ",da:"da-DK",de:"de-DE",el:"el-GR",en:"en-US",es:"es-ES",fi:"fi-FI",fr:"fr-FR",he:"he-IL",hi:"hi-IN",hr:"hr-HR",hu:"hu-HU",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lt:"lt-LT",lv:"lv-LV",ms:"ms-MY",nl:"nl-NL",no:"nb-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",sr:"sr-RS",sv:"sv-SE",th:"th-TH",tr:"tr-TR",uk:"uk-UA",vi:"vi-VN",zh:"zh-CN"}[t]||t},currentIpToCountry(n){if(wpforms_settings.country)n(wpforms_settings.country);else{let e=function(){a.get("https://ipapi.co/jsonp",function(){},"jsonp").always(function(t){let e=t?.country?t.country:"";e||(t=l.getFirstBrowserLanguage(),e=-1<t.indexOf("-")?t.split("-").pop():""),n(e)})};a.get("https://geo.wpforms.com/v3/geolocate/json").done(function(t){t&&t.country_iso?n(t.country_iso):e()}).fail(function(){e()})}}};return l})(document,window,jQuery),window.WPFormsPhoneField.init();