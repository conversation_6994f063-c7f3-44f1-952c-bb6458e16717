!function(){var e={2213:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.isUnique=function(e,t){if(!Bo<PERSON>an(t))return!1;var n=e.ownerDocument.querySelectorAll(t);return 1===n.length&&n[0]===e}},2584:function(e,t){"use strict";function n(e,t,r,o,i,a,u){if(a!==u)for(var s=o;s<=i&&i-s+1>=u-a;++s)r[a]=t[s],n(e,t,r,s+1,i,a+1,u);else e.push(r.slice(0,a).join(""))}Object.defineProperty(t,"__esModule",{value:!0}),t.getCombinations=function(e,t){for(var r=[],o=e.length,i=[],a=1;a<=t;++a)n(r,e,i,0,o-1,0,a);return r}},3770:function(e,t){"use strict";function n(e){if(!e.hasAttribute("class"))return[];try{return Array.prototype.slice.call(e.classList).filter((function(e){return/^[a-z_-][a-z\d_-]*$/i.test(e)?e:null}))}catch(n){var t=e.getAttribute("class");return(t=t.trim().replace(/\s+/g," ")).split(" ")}}Object.defineProperty(t,"__esModule",{value:!0}),t.getClasses=n,t.getClassSelectors=function(e){return n(e).filter(Boolean).map((function(e){return"."+e}))}},4561:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getAttributes=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:["id","class","length"],n=e.attributes;return[].concat(function(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}return Array.from(e)}(n)).reduce((function(e,n){return t.indexOf(n.nodeName)>-1||e.push("["+n.nodeName+'="'+n.value+'"]'),e}),[])}},6064:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getNthChild=function(e){var t=0,n=void 0,o=void 0,i=e.parentNode;if(Boolean(i)){var a=i.childNodes,u=a.length;for(n=0;n<u;n++)if(o=a[n],(0,r.isElement)(o)&&(t++,o===e))return":nth-child("+t+")"}return null};var r=n(8440)},6105:function(e){e.exports=function(){function e(){return(e=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}var t={ignoreId:!1},n="undefined"!=typeof Node,r=n?Node.ELEMENT_NODE:1,o=n?Node.TEXT_NODE:3,i=n?Node.DOCUMENT_TYPE_NODE:10;return function(n,a){var u=e({},t,a),s=n;if(s&&s.id&&!u.ignoreId)return'//*[@id="'+s.id+'"]';for(var c=[];s&&(r===s.nodeType||o===s.nodeType);){for(var l=0,f=!1,d=s.previousSibling;d;)d.nodeType!==i&&d.nodeName===s.nodeName&&l++,d=d.previousSibling;for(d=s.nextSibling;d;){if(d.nodeName===s.nodeName){f=!0;break}d=d.nextSibling}var v=l||f?"["+(l+1)+"]":"";c.push(s.nodeType!=o?(s.prefix?s.prefix+":":"")+s.localName+v:"text()"+(v||"[1]")),s=s.parentNode}return c.length?"/"+c.reverse().join("/"):""}}()},7175:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getParents=function(e){var t=[],n=e;for(;(0,r.isElement)(n);)t.push(n),n=n.parentNode;return t};var r=n(8440)},7924:function(e,t,n){"use strict";t.A=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.selectorTypes,r=void 0===n?["ID","Class","Tag","NthChild"]:n,o=t.attributesToIgnore,i=void 0===o?["id","class","length"]:o,a=t.excludeRegex,u=void 0===a?null:a,s=[],f=(0,l.getParents)(e),d=!0,v=!1,m=void 0;try{for(var g,y=f[Symbol.iterator]();!(d=(g=y.next()).done);d=!0){var h=p(g.value,r,i,u);Boolean(h)&&s.push(h)}}catch(e){v=!0,m=e}finally{try{!d&&y.return&&y.return()}finally{if(v)throw m}}var b=[],T=!0,S=!1,_=void 0;try{for(var E,w=s[Symbol.iterator]();!(T=(E=w.next()).done);T=!0){var C=E.value;b.unshift(C);var N=b.join(" > ");if((0,c.isUnique)(e,N))return N}}catch(e){S=!0,_=e}finally{try{!T&&w.return&&w.return()}finally{if(S)throw _}}return null};var r=n(9169),o=n(3770),i=n(2584),a=n(4561),u=n(6064),s=n(7970),c=n(2213),l=n(7175);function f(e,t){var n=e.parentNode.querySelectorAll(t);return 1===n.length&&n[0]===e}function d(e,t){return t.find(f.bind(null,e))}function v(e,t,n){var r=(0,i.getCombinations)(t,3),o=d(e,r);return Boolean(o)||Boolean(n)&&(o=d(e,r=r.map((function(e){return n+e}))),Boolean(o))?o:null}function p(e,t,n,i){var c=void 0,l=function(e,t,n){var i={Tag:s.getTag,NthChild:u.getNthChild,Attributes:function(e){return(0,a.getAttributes)(e,n)},Class:o.getClassSelectors,ID:r.getID};return t.reduce((function(t,n){return t[n]=i[n](e),t}),{})}(e,t,n);i&&i instanceof RegExp&&(l.ID=i.test(l.ID)?null:l.ID,l.Class=l.Class.filter((function(e){return!i.test(e)})));var d=!0,p=!1,m=void 0;try{for(var g,y=t[Symbol.iterator]();!(d=(g=y.next()).done);d=!0){var h=g.value,b=l.ID,T=l.Tag,S=l.Class,_=l.Attributes,E=l.NthChild;switch(h){case"ID":if(Boolean(b)&&f(e,b))return b;break;case"Tag":if(Boolean(T)&&f(e,T))return T;break;case"Class":if(Boolean(S)&&S.length&&(c=v(e,S,T)))return c;break;case"Attributes":if(Boolean(_)&&_.length&&(c=v(e,_,T)))return c;break;case"NthChild":if(Boolean(E))return E}}}catch(e){p=!0,m=e}finally{try{!d&&y.return&&y.return()}finally{if(p)throw m}}return"*"}},7970:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getTag=function(e){return e.tagName.toLowerCase().replace(/:/g,"\\:")}},8440:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};t.isElement=function(e){var t=void 0;t="object"===("undefined"==typeof HTMLElement?"undefined":n(HTMLElement))?e instanceof HTMLElement:!!e&&"object"===(void 0===e?"undefined":n(e))&&1===e.nodeType&&"string"==typeof e.nodeName;return t}},9169:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.getID=function(e){var t=e.getAttribute("id");if(null!==t&&""!==t)return t.match(/(?:^\d|:)/)?'[id="'+t+'"]':"#"+t;return null}}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=function(){var e=self.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},t=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},r=function(e,n){var r="";try{for(;e&&9!==e.nodeType;){var o=e,i=o.id?"#"+o.id:t(o)+(o.classList&&o.classList.value&&o.classList.value.trim()&&o.classList.value.trim().length?"."+o.classList.value.trim().replace(/\s+/g,"."):"");if(r.length+i.length>(n||100)-1)return r||i;if(r=r?i+">"+r:i,o.id)break;e=o.parentNode}}catch(e){}return r},o=-1,i=function(){return o},a=function(e){addEventListener("pageshow",(function(t){t.persisted&&(o=t.timeStamp,e(t))}),!0)},u=function(){var t=e();return t&&t.activationStart||0},s=function(t,n){var r=e(),o="navigate";return i()>=0?o="back-forward-cache":r&&(document.prerendering||u()>0?o="prerender":document.wasDiscarded?o="restore":r.type&&(o=r.type.replace(/_/g,"-"))),{name:t,value:void 0===n?-1:n,rating:"good",delta:0,entries:[],id:"v4-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:o}},c=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver((function(e){Promise.resolve().then((function(){t(e.getEntries())}))}));return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},l=function(e,t,n,r){var o,i;return function(a){t.value>=0&&(a||r)&&((i=t.value-(o||0))||void 0===o)&&(o=t.value,t.delta=i,t.rating=function(e,t){return e>t[1]?"poor":e>t[0]?"needs-improvement":"good"}(t.value,n),e(t))}},f=function(e){requestAnimationFrame((function(){return requestAnimationFrame((function(){return e()}))}))},d=function(e){document.addEventListener("visibilitychange",(function(){"hidden"===document.visibilityState&&e()}))},v=function(e){var t=!1;return function(){t||(e(),t=!0)}},p=-1,m=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},g=function(e){"hidden"===document.visibilityState&&p>-1&&(p="visibilitychange"===e.type?e.timeStamp:0,h())},y=function(){addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},h=function(){removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},b=function(){return p<0&&(p=m(),y(),a((function(){setTimeout((function(){p=m(),y()}),0)}))),{get firstHiddenTime(){return p}}},T=function(e){document.prerendering?addEventListener("prerenderingchange",(function(){return e()}),!0):e()},S=[],_=new Map,E=[],w=function(e){var t=self.requestIdleCallback||self.setTimeout,n=-1;return e=v(e),"hidden"===document.visibilityState?e():(n=t(e),d(e)),n},C=[],N=[],M=0,P=new WeakMap,A=new Map,O=-1,j=function(){O<0&&(O=w(k))},k=function(){A.size>10&&A.forEach((function(e,t){_.has(t)||A.delete(t)}));var e=S.map((function(e){return P.get(e.entries[0])})),t=N.length-50;N=N.filter((function(n,r){return r>=t||e.includes(n)}));for(var n=new Set,r=0;r<N.length;r++){var o=N[r];L(o.startTime,o.processingEnd).forEach((function(e){n.add(e)}))}var i=C.length-1-50;C=C.filter((function(e,t){return e.startTime>M&&t>i||n.has(e)})),O=-1};E.push((function(e){e.interactionId&&e.target&&!A.has(e.interactionId)&&A.set(e.interactionId,e.target)}),(function(e){var t,n=e.startTime+e.duration;M=Math.max(M,e.processingEnd);for(var r=N.length-1;r>=0;r--){var o=N[r];if(Math.abs(n-o.renderTime)<=8){(t=o).startTime=Math.min(e.startTime,t.startTime),t.processingStart=Math.min(e.processingStart,t.processingStart),t.processingEnd=Math.max(e.processingEnd,t.processingEnd),t.entries.push(e);break}}t||(t={startTime:e.startTime,processingStart:e.processingStart,processingEnd:e.processingEnd,renderTime:n,entries:[e]},N.push(t)),(e.interactionId||"first-input"===e.entryType)&&P.set(e,t),j()}));var L=function(e,t){for(var n,r=[],o=0;n=C[o];o++)if(!(n.startTime+n.duration<e)){if(n.startTime>t)break;r.push(n)}return r},x=[2500,4e3],D={},I=(new Date,n(7924)),B=n(6105),q=n.n(B);function R(e){return R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},R(e)}function F(e){return function(e){if(Array.isArray(e))return H(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return H(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?H(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function H(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function U(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,V(r.key),r)}}function V(e){var t=function(e,t){if("object"!=R(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=R(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==R(t)?t:t+""}var z,J=function(){return function(e,t,n){return t&&U(e.prototype,t),n&&U(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}),[{key:"onLCP",value:function(e){var t,n,r,o=null==e||null===(t=e.entries[0])||void 0===t?void 0:t.element,i=null==e||null===(n=e.attribution)||void 0===n?void 0:n.url;if(o&&i){var a=(null==e||null===(r=e.attribution)||void 0===r?void 0:r.element)||"",u=a&&1===document.querySelectorAll(a).length?a:(0,I.A)(o),s=q()(o,{ignoreId:!0}),c={url:window.location.href,data:JSON.stringify({selector:u,selector_xpath:s,selector_id:null==o?void 0:o.id,selector_class:null==o?void 0:o.className,image_url:i,background_data:this.getBackgroundDataForElement(o)}),nonce:smush_detector.nonce,is_mobile:smush_detector.is_mobile,data_store:JSON.stringify(smush_detector.data_store),previous_data_version:smush_detector.previous_data_version,previous_data_hash:smush_detector.previous_data_hash},l=new XMLHttpRequest;l.open("POST",smush_detector.ajax_url+"?action=smush_handle_lcp_data",!0),l.setRequestHeader("Content-Type","application/x-www-form-urlencoded");var f=Object.keys(c).map((function(e){return encodeURIComponent(e)+"="+encodeURIComponent(c[e])})).join("&");l.send(f)}}},{key:"getBackgroundDataForElement",value:function(e){var t=[window.getComputedStyle(e,null).getPropertyValue("background-image"),getComputedStyle(e,":after").getPropertyValue("background-image"),getComputedStyle(e,":before").getPropertyValue("background-image")].filter((function(e){return"none"!==e}));return 0===t.length?null:this.getBackgroundDataForPropertyValue(t[0])}},{key:"getBackgroundDataForPropertyValue",value:function(e){var t="background-image";if(e.includes("image-set(")&&(t="background-image-set"),!e||""===e||e.includes("data:image"))return null;var n=F(e.matchAll(/((?:https?:\/|\.+)?\/[^'",\s()]+\.(jpe?g|png|gif|webp|svg|avif)(?:\?[^\s'",?)]+)?)\b/gi)).map((function(e){return e[1].trim()}));return n.length<=0?null:n.length>0?{type:t,property:e,urls:n}:null}}])}();null!==(z=document)&&void 0!==z&&null!==(z=z.documentElement)&&void 0!==z&&z.scrollTop||function(t,n){!function(e,t){t=t||{},T((function(){var n,r=b(),o=s("LCP"),i=function(e){t.reportAllChanges||(e=e.slice(-1)),e.forEach((function(e){e.startTime<r.firstHiddenTime&&(o.value=Math.max(e.startTime-u(),0),o.entries=[e],n())}))},p=c("largest-contentful-paint",i);if(p){n=l(e,o,x,t.reportAllChanges);var m=v((function(){D[o.id]||(i(p.takeRecords()),p.disconnect(),D[o.id]=!0,n(!0))}));["keydown","click"].forEach((function(e){addEventListener(e,(function(){return w(m)}),{once:!0,capture:!0})})),d(m),a((function(r){o=s("LCP"),n=l(e,o,x,t.reportAllChanges),f((function(){o.value=performance.now()-r.timeStamp,D[o.id]=!0,n(!0)}))}))}}))}((function(n){var o=function(t){var n={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadDuration:0,elementRenderDelay:t.value};if(t.entries.length){var o=e();if(o){var i=o.activationStart||0,a=t.entries[t.entries.length-1],u=a.url&&performance.getEntriesByType("resource").filter((function(e){return e.name===a.url}))[0],s=Math.max(0,o.responseStart-i),c=Math.max(s,u?(u.requestStart||u.startTime)-i:0),l=Math.max(c,u?u.responseEnd-i:0),f=Math.max(l,a.startTime-i);n={element:r(a.element),timeToFirstByte:s,resourceLoadDelay:c-s,resourceLoadDuration:l-c,elementRenderDelay:f-l,navigationEntry:o,lcpEntry:a},a.url&&(n.url=a.url),u&&(n.lcpResourceEntry=u)}}return Object.assign(t,{attribution:n})}(n);t(o)}),n)}((function(e){(new J).onLCP(e)}))}()}();
//# sourceMappingURL=smush-detector.min.js.map