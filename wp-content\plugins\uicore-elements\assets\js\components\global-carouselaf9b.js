"use strict";function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _regeneratorRuntime(){_regeneratorRuntime=function(){return a};var l,a={},e=Object.prototype,c=e.hasOwnProperty,u=Object.defineProperty||function(e,t,r){e[t]=r.value},t="function"==typeof Symbol?Symbol:{},n=t.iterator||"@@iterator",r=t.asyncIterator||"@@asyncIterator",o=t.toStringTag||"@@toStringTag";function i(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{i({},"")}catch(l){i=function(e,t,r){return e[t]=r}}function s(e,t,r,n){var o,i,a,s,t=t&&t.prototype instanceof g?t:g,t=Object.create(t.prototype),n=new k(n||[]);return u(t,"_invoke",{value:(o=e,i=r,a=n,s=d,function(e,t){if(s===h)throw Error("Generator is already running");if(s===y){if("throw"===e)throw t;return{value:l,done:!0}}for(a.method=e,a.arg=t;;){var r=a.delegate;if(r){r=function e(t,r){var n=r.method,o=t.iterator[n];if(o===l)return r.delegate=null,"throw"===n&&t.iterator.return&&(r.method="return",r.arg=l,e(t,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),m;n=f(o,t.iterator,r.arg);if("throw"===n.type)return r.method="throw",r.arg=n.arg,r.delegate=null,m;o=n.arg;return o?o.done?(r[t.resultName]=o.value,r.next=t.nextLoc,"return"!==r.method&&(r.method="next",r.arg=l),r.delegate=null,m):o:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,m)}(r,a);if(r){if(r===m)continue;return r}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if(s===d)throw s=y,a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);s=h;r=f(o,i,a);if("normal"===r.type){if(s=a.done?y:p,r.arg===m)continue;return{value:r.arg,done:a.done}}"throw"===r.type&&(s=y,a.method="throw",a.arg=r.arg)}})}),t}function f(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}a.wrap=s;var d="suspendedStart",p="suspendedYield",h="executing",y="completed",m={};function g(){}function v(){}function b(){}var t={},_=(i(t,n,function(){return this}),Object.getPrototypeOf),_=_&&_(_(P([]))),w=(_&&_!==e&&c.call(_,n)&&(t=_),b.prototype=g.prototype=Object.create(t));function S(e){["next","throw","return"].forEach(function(t){i(e,t,function(e){return this._invoke(t,e)})})}function E(a,s){var t;u(this,"_invoke",{value:function(r,n){function e(){return new s(function(e,t){!function t(e,r,n,o){var i,e=f(a[e],a,r);if("throw"!==e.type)return(r=(i=e.arg).value)&&"object"==_typeof(r)&&c.call(r,"__await")?s.resolve(r.__await).then(function(e){t("next",e,n,o)},function(e){t("throw",e,n,o)}):s.resolve(r).then(function(e){i.value=e,n(i)},function(e){return t("throw",e,n,o)});o(e.arg)}(r,n,e,t)})}return t=t?t.then(e,e):e()}})}function j(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function k(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(j,this),this.reset(!0)}function P(t){if(t||""===t){var r,e=t[n];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return r=-1,(e=function e(){for(;++r<t.length;)if(c.call(t,r))return e.value=t[r],e.done=!1,e;return e.value=l,e.done=!0,e}).next=e}throw new TypeError(_typeof(t)+" is not iterable")}return u(w,"constructor",{value:v.prototype=b,configurable:!0}),u(b,"constructor",{value:v,configurable:!0}),v.displayName=i(b,o,"GeneratorFunction"),a.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===v||"GeneratorFunction"===(e.displayName||e.name))},a.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,b):(e.__proto__=b,i(e,o,"GeneratorFunction")),e.prototype=Object.create(w),e},a.awrap=function(e){return{__await:e}},S(E.prototype),i(E.prototype,r,function(){return this}),a.AsyncIterator=E,a.async=function(e,t,r,n,o){void 0===o&&(o=Promise);var i=new E(s(e,t,r,n),o);return a.isGeneratorFunction(t)?i:i.next().then(function(e){return e.done?e.value:i.next()})},S(w),i(w,o,"Generator"),i(w,n,function(){return this}),i(w,"toString",function(){return"[object Generator]"}),a.keys=function(e){var t,r=Object(e),n=[];for(t in r)n.push(t);return n.reverse(),function e(){for(;n.length;){var t=n.pop();if(t in r)return e.value=t,e.done=!1,e}return e.done=!0,e}},a.values=P,k.prototype={constructor:k,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=l,this.done=!1,this.delegate=null,this.method="next",this.arg=l,this.tryEntries.forEach(O),!e)for(var t in this)"t"===t.charAt(0)&&c.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=l)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(r){if(this.done)throw r;var n=this;function e(e,t){return i.type="throw",i.arg=r,n.next=e,t&&(n.method="next",n.arg=l),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var o=this.tryEntries[t],i=o.completion;if("root"===o.tryLoc)return e("end");if(o.tryLoc<=this.prev){var a=c.call(o,"catchLoc"),s=c.call(o,"finallyLoc");if(a&&s){if(this.prev<o.catchLoc)return e(o.catchLoc,!0);if(this.prev<o.finallyLoc)return e(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return e(o.catchLoc,!0)}else{if(!s)throw Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return e(o.finallyLoc)}}}},abrupt:function(e,t){for(var r=this.tryEntries.length-1;0<=r;--r){var n=this.tryEntries[r];if(n.tryLoc<=this.prev&&c.call(n,"finallyLoc")&&this.prev<n.finallyLoc){var o=n;break}}var i=(o=o&&("break"===e||"continue"===e)&&o.tryLoc<=t&&t<=o.finallyLoc?null:o)?o.completion:{};return i.type=e,i.arg=t,o?(this.method="next",this.next=o.finallyLoc,m):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),m},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),O(r),m}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var r,n,o=this.tryEntries[t];if(o.tryLoc===e)return"throw"===(r=o.completion).type&&(n=r.arg,O(o)),n}throw Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:P(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=l),m}},a}function asyncGeneratorStep(e,t,r,n,o,i,a){try{var s=e[i](a),l=s.value}catch(e){return void r(e)}s.done?t(l):Promise.resolve(l).then(n,o)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,r){var n=s.apply(e,a);function o(e){asyncGeneratorStep(n,t,r,o,i,"next",e)}function i(e){asyncGeneratorStep(n,t,r,o,i,"throw",e)}o(void 0)})}}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,s=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(t,e){var r,n=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,r)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,_toPropertyKey(n.key),n)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}function _callSuper(e,t,r){return t=_getPrototypeOf(t),_possibleConstructorReturn(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],_getPrototypeOf(e).constructor):t.apply(e,r))}function _possibleConstructorReturn(e,t){if(t&&("object"==_typeof(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(e)}function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(_isNativeReflectConstruct=function(){return!!e})()}function _getPrototypeOf(e){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_setPrototypeOf(e,t)}function _setPrototypeOf(e,t){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}window.addEventListener("DOMContentLoaded",function(){var t=function(){function e(){return _classCallCheck(this,e),_callSuper(this,e,arguments)}return _inherits(e,elementorModules.frontend.handlers.Base),_createClass(e,[{key:"getDefaultElements",value:function(){return{$carousel:this.$element.find(".ui-e-carousel"),$dots:this.$element.find(".ui-e-dots"),$next:this.$element.find(".ui-e-next"),$prev:this.$element.find(".ui-e-previous"),$fraction:this.$element.find(".ui-e-fraction")}}},{key:"buildCarousel",value:function(){var t,r,n,o,i=this,e=this.getElementSettings("navigation"),a=this.getElementSettings("animation_style"),s=this.getElementSettings("show_hidden"),l=this.getElementSettings("carousel_gap")?elementorFrontend.getCurrentDeviceSetting(this.getElementSettings("carousel_gap").size):0,c=(!this.hasNesting()||!elementorFrontend.isEditMode())&&Boolean(this.getElementSettings("loop")),u=elementorFrontend.getCurrentDeviceSetting(this.getElementSettings(),"slides_per_view")||1,f=Boolean(this.getElementSettings("center_slides")),d=(u-1)/2,p=(document.documentElement.clientWidth<1024&&(elementorFrontend.config.experimentalFeatures.hasOwnProperty("container")?this.$element.closest('[data-element_type="container"]'):this.$element.closest('[data-element_type="column"]')).css("overflow-x","hidden"),{slidesPerView:u,spaceBetween:l,loop:c,grabCursor:"yes"==this.getElementSettings("grab_cursor"),speed:300,slideActiveClass:"is-selected",shownSide:s||"hidden"}),u=("hidden"!=s&&(p.shownSide=s),f&&(p=_objectSpread(_objectSpread({},p),{},{centeredSlides:!0,initialSlide:d})),"yes"==this.getElementSettings("autoplay")&&(p.autoplay={delay:this.getElementSettings("autoplay_speed"),pauseOnMouseEnter:Boolean(this.getElementSettings("pause_on_hover")),reverseDirection:Boolean(this.getElementSettings("reverse")),disableOnInteraction:!1}),Boolean(this.getElementSettings("vertical"))&&(p.slidesPerView="auto"),["arrows","arrows-fraction","arrows-dots"].includes(e)&&(p.navigation={nextEl:this.elements.$next[0],prevEl:this.elements.$prev[0]}),["dots","arrows-dots"].includes(e)&&(p.pagination={el:this.elements.$dots[0],type:"bullets",clickable:!0,bulletClass:"dot",bulletActiveClass:"is-selected"}),["fraction","arrows-fraction"].includes(e)&&(p.pagination={el:this.elements.$fraction[0],type:"fraction"}),this.getBreakpoints());switch(p.breakpoints={},Object.entries(u).forEach(function(e){e=_slicedToArray(e,2),e[0],e=e[1];t="desktop"==e.label?i.getElementSettings("slides_per_view")||1:i.getElementSettings("slides_per_view_".concat(e.label))||1,n="desktop"==e.label&&f,o="desktop"==e.label?d:0,i.getWidgetType().includes("slider")||(r="desktop"==e.label?i.getElementSettings("carousel_gap").size||0:i.getElementSettings("carousel_gap_".concat(e.label)).size||0),p.breakpoints[e.value]={slidesPerView:Boolean(i.getElementSettings("vertical"))?"auto":t,spaceBetween:r||0,centeredSlides:n,initialSlide:o}}),a){case"circular":p.modules=[specialEffects],p.animation={type:"circular",start:{left:{opacity:1,transform:{perspective:!0,rotateY:0,translateX:0,translateZ:0}},right:{opacity:1,transform:{perspective:!0,rotateY:0,translateX:0,translateZ:0}}},end:{left:{opacity:0,transform:{perspective:!0,rotateY:-18,translateX:-200,translateZ:-50}},right:{opacity:0,transform:{perspective:!0,rotateY:18,translateX:200,translateZ:-50}}}};break;case"fade_blur":p.modules=[specialEffects],p.animation={start:{left:{opacity:1,filter:{blur:0}},right:{opacity:1,filter:{blur:0}}},end:{left:{opacity:0,filter:{blur:10}},right:{opacity:0,filter:{blur:10}}}};break;case"stacked":p=_objectSpread(_objectSpread({},p),{},{direction:"vertical",loop:!1,rewind:c,slidesPerView:1,modules:[stackedEffect],autoHeight:!0,spaceBetween:0});break;case"cards":p=_objectSpread(_objectSpread({},p),{},{loop:!1,rewind:c,effect:"cards",cardsEffect:{slideShadows:!1}});break;case"circular_avatar":var h=this.getElementSettings("layout"),p=_objectSpread(_objectSpread({},p),{},{slidesPerView:1,modules:[circularAvatar],spaceBetween:0,speed:850,enableAvatarTranslate:["layout_5","layout_6"].includes(h),avatarClassName:".ui-e-testimonial-avatar",contentClassNames:[".ui-e-testimonial-text",".ui-e-testimonial-name",".ui-e-testimonial-job-title",".ui-e-testimonial-rating",".ui-e-testimonial-image"]});break;case"fade":p=_objectSpread(_objectSpread({},p),{},{effect:"fade",fadeEffect:{crossFade:!0}});break;case"marquee":p=_objectSpread(_objectSpread({},p),{},{loop:!0,speed:Math.abs(this.getElementSettings("marquee_speed")-1e4)||5e3,allowTouchMove:!1,direction:Boolean(this.getElementSettings("vertical"))?"vertical":"horizontal",autoHeight:Boolean(this.getElementSettings("vertical")),autoplay:{delay:0,disableOnInteraction:!1,reverseDirection:Boolean(this.getElementSettings("reverse"))}});break;case"coverflow":h=this.elements.$carousel.find(".swiper-slide").length,h=h%2==0?(h-1)/2:h/2;p=_objectSpread(_objectSpread({},p),{},{initialSlide:Math.floor(h),effect:"coverflow",coverflowEffect:{depth:500,rotate:20,slideShadows:!0}});break;default:p.watchSlidesProgress=!0,p.speed=500,p.effect=a,p.customEffect={slideShadows:!1},p.creativeEffect={slideShadows:!1,prev:{shadow:!0,translate:[0,0,-400]},next:{translate:["100%",0,0]}}}this.initSwiper(window.UiCoreSwiper,this.elements.$carousel[0],p)}},{key:"initSwiper",value:(n=_asyncToGenerator(_regeneratorRuntime().mark(function e(o,i,a){var s,l;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:"yes"!=(l=this).getElementSettings("observer")||elementorFrontend.isEditMode()?e.next=6:(new IntersectionObserver(function(){var r=_asyncToGenerator(_regeneratorRuntime().mark(function e(t,r){var n;return _regeneratorRuntime().wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=_slicedToArray(t,1),n[0].isIntersecting)return e.next=4,new o(i,a);e.next=8;break;case 4:(s=e.sent).init(),l.triggerElements(s),r.unobserve(i);case 8:case"end":return e.stop()}},e)}));return function(e,t){return r.apply(this,arguments)}}(),{threshold:.7}).observe(i),e.next=11);break;case 6:return e.next=8,new o(i,a);case 8:s=e.sent,this.triggerElements(s),s.init();case 11:case"end":return e.stop()}},e,this)})),function(e,t,r){return n.apply(this,arguments)})},{key:"getBreakpoints",value:function(){for(var e=elementorFrontend.breakpoints.getActiveBreakpointsList(),t=elementorFrontend.breakpoints.getBreakpointValues(),r=[],n=-1,o=0;o<e.length;o++){var i=e[o],a=0==o?0:t[n];o==e.length-1&&(a=("widescreen"===e[o]?r.push({label:"desktop",value:a}):(r.push({label:i,value:a}),i="desktop"),t[++n])),r.push({label:i,value:a}),n++}return r}},{key:"hasNesting",value:function(){return["uicore-custom-carousel","uicore-custom-slider"].includes(this.getWidgetType())}},{key:"wrapNestedContainers",value:function(){this.hasNesting()&&elementorFrontend.isEditMode()&&this.getDefaultElements().$carousel.find(".e-con").each(function(){jQuery(this).wrap('<div class="ui-e-wrp swiper-slide"> </div>')})}},{key:"triggerElements",value:function(r){var n=this;this.hasNesting()&&r.on("slideChange",function(){var e=r.slides[r.activeIndex],t=jQuery(e).siblings();"uicore-custom-slider"===n.getWidgetType()&&(setTimeout(function(){jQuery(e).find(".ui-e-item").removeClass("elementor-animated")},1e3),jQuery(t).find(".ui-e-item").each(function(){jQuery(this).addClass("elementor-animated")})),jQuery(e).find(".elementor-element").each(function(){elementorFrontend.elementsHandler.runReadyTrigger(jQuery(this))})})}},{key:"bindEvents",value:function(){this.buildCarousel()}},{key:"onInit",value:function(){this.wrapNestedContainers(),elementorModules.frontend.handlers.Base.prototype.onInit.apply(this,arguments)}}]);var n}();jQuery(window).on("elementor/frontend/init",function(){function e(e){elementorFrontend.elementsHandler.addHandler(t,{$element:e})}elementorFrontend.hooks.addAction("frontend/element_ready/uicore-testimonial-carousel.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-testimonial-slider.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-logo-carousel.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-advanced-post-carousel.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-custom-carousel.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-custom-slider.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-gallery-carousel.default",e),elementorFrontend.hooks.addAction("frontend/element_ready/uicore-gallery-slider.default",e)})});