!function(e,t){e(window).on("elementor/frontend/init",(function(){let t,n=elementorModules.frontend.handlers.Base;t=n.extend({bindEvents:function(){this.run()},getDefaultSettings:function(){return{allowHTML:!0}},onElementChange:debounce((function(e){-1!==e.indexOf("marquee_")&&this.run()}),400),settings:function(e){return this.getElementSettings("marquee_"+e)},run:function(){if(elementorFrontend.isEditMode())return;const t=this.$element.data("id");var n=this;this.getDefaultSettings();if(!this.$element.find(".bdt-marquee"))return;"yes"===n.settings("advanced")&&e("body").css("overflow-x","hidden");var r=".elementor-element-"+t;let s=gsap.utils.toArray(r+" .marquee-content");s=(e=>{let t=e.reduce(((e,t)=>e+t.offsetWidth),0);if(0==t)return e;let n=window.innerWidth;for(;t<n;)e.forEach((e=>{const t=e.cloneNode(!0);e.parentNode.appendChild(t)})),t=(e=gsap.utils.toArray(r+" .marquee-content")).reduce(((e,t)=>e+t.offsetWidth),0);return e})(s);const o="rtl"===document.documentElement.dir,a="right"===n.settings("direction")!==o;let i,d=n.settings("speed")?n.settings("speed")/100:1,l="yes"===n.settings("clickable"),u="yes"===n.settings("pause_on_hover"),g="yes"===n.settings("draggable"),p=n.settings("spacing")?n.settings("spacing"):0;n.settings("loop");const c=n.horizontalLoop(s,{paused:!1,draggable:g,repeat:-1,speed:d,paddingRight:p,reversed:a,center:!0,reversedDirection:a,onChange:(e,t)=>{i&&i.classList.remove("active"),e.classList.add("active"),i=e}});u&&n.pauseOnHover(c,s),l&&(e(s).on("mouseleave",(()=>{c.reversed()?(c.play(),c.reverse()):(c.play(),c.reversed())})),s.forEach(((e,t)=>{e.addEventListener("click",(()=>{c.toIndex(t,{duration:.8,ease:"power1.inOut"})}))})))},pauseOnHover:function(t,n){e(n).on("mouseenter",(()=>{t.pause()})),e(n).on("mouseleave",(()=>{t.reversed()?(t.play(),t.reverse()):(t.play(),t.reversed())}))},horizontalLoop:function(e,t,n=!1){e=gsap.utils.toArray(e);let r,s,o,a=(t=t||{}).onChange,i=gsap.timeline({repeat:t.repeat,paused:t.paused,defaults:{ease:"none"},onReverseComplete:()=>i.totalTime(i.rawTime()+100*i.duration())}),d=e.length,l=e[0].offsetLeft,u=[],g=[],p=[],c=[],f=0,m=t.center,h=100*(t.speed||1),v=!1===t.snap?e=>e:gsap.utils.snap(t.snap||1),w=0,y=!0===m?e[0].parentNode:gsap.utils.toArray(m)[0]||e[0].parentNode,b=()=>{let n,s=y.getBoundingClientRect();e.forEach(((e,t)=>{g[t]=parseFloat(gsap.getProperty(e,"width","px")),c[t]=v(parseFloat(gsap.getProperty(e,"x","px"))/g[t]*100+gsap.getProperty(e,"xPercent")),n=e.getBoundingClientRect(),p[t]=n.left-(t?s.right:s.left),s=n})),gsap.set(e,{xPercent:e=>c[e]}),r=e[d-1].offsetLeft+c[d-1]/100*g[d-1]-l+p[0]+e[d-1].offsetWidth*gsap.getProperty(e[d-1],"scaleX")+(parseFloat(t.paddingRight)||0)},x=()=>{w=m?i.duration()*(y.offsetWidth/2)/r:0,m&&u.forEach(((e,t)=>{u[t]=s(i.labels["label"+t]+i.duration()*g[t]/2/r-w)}))},P=(e,t,n)=>{let r,s=e.length,o=1e10,a=0;for(;s--;)r=Math.abs(e[s]-t),r>n/2&&(r=n-r),r<o&&(o=r,a=s);return a},E=()=>{let t,n,o,a,f;for(i.clear(),t=0;t<d;t++)n=e[t],o=c[t]/100*g[t],a=n.offsetLeft+o-l+p[0],f=a+g[t]*gsap.getProperty(n,"scaleX"),i.to(n,{xPercent:v((o-f)/g[t]*100),duration:f/h},0).fromTo(n,{xPercent:v((o-f+r)/g[t]*100)},{xPercent:c[t],duration:(o-f+r-o)/h,immediateRender:!1},f/h).add("label"+t,a/h),u[t]=a/h;s=gsap.utils.wrap(0,i.duration())},L=e=>{let t=i.progress();i.progress(0,!0),b(),e&&E(),x(),e&&i.draggable?i.time(u[f],!0):i.progress(t,!0)};function C(e,t){t=t||{},n&&(e=d-e),Math.abs(e-f)>d/2&&(e+=e>f?-d:d);let r=gsap.utils.wrap(0,d,e),a=u[r];return a>i.time()!=e>f&&(a+=i.duration()*(e>f?1:-1)),(a<0||a>i.duration())&&(t.modifiers={time:s}),f=r,t.overwrite=!0,gsap.killTweensOf(o),i.tweenTo(a,t)}if(gsap.set(e,{x:0}),b(),E(),x(),window.addEventListener("resize",(()=>L(!0))),i.next=e=>C(f+1,e),i.previous=e=>C(f-1,e),i.current=()=>f,i.toIndex=(e,t)=>C(e,t),i.closestIndex=e=>{let t=P(u,i.time(),i.duration());return e&&(f=t),t},i.times=u,i.progress(1,!0).progress(0,!0),t.reversed&&(i.vars.onReverseComplete(),i.reverse()),t.draggable&&"function"==typeof Draggable){o=document.createElement("div");let t,n,a,d=gsap.utils.wrap(0,1),l=()=>i.progress(d(n+(a.startX-a.x)*t)),g=()=>i.closestIndex(!0);"undefined"==typeof InertiaPlugin&&console.warn("InertiaPlugin required for momentum-based scrolling and snapping. https://greensock.com/club"),a=Draggable.create(o,{trigger:e[0].parentNode,type:"x",onPressInit(){gsap.killTweensOf(i),n=i.progress(),L(),t=1/r,gsap.set(o,{x:n/-t})},onDrag:l,onThrowUpdate:l,inertia:!0,snap:e=>{let n=-e*t*i.duration(),r=s(n),o=u[P(u,r,i.duration())]-r;return Math.abs(o)>i.duration()/2&&(o+=o<0?i.duration():-i.duration()),(n+o)/i.duration()/-t},onRelease:g,onThrowComplete:g})[0],i.draggable=a}return i.closestIndex(!0),a&&a(e[f],f),i}}),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-marquee.default",(function(e){elementorFrontend.elementsHandler.addHandler(t,{$element:e})}))}))}(jQuery,window.elementorFrontend);