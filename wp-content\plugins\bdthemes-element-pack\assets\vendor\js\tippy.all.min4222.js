/**!
* tippy.js v6.3.1
* (c) 2017-2021 atomiks
* MIT License
*/
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("@popperjs/core")):"function"==typeof define&&define.amd?define(["@popperjs/core"],e):(t=t||self).tippy=e(t.Popper)}(this,(function(t){"use strict";var e="undefined"!=typeof window&&"undefined"!=typeof document,n=e?navigator.userAgent:"",i=/MSIE |Trident\//.test(n),r={passive:!0,capture:!0};function o(t,e,n){if(Array.isArray(t)){var i=t[e];return null==i?Array.isArray(n)?n[e]:n:i}return t}function a(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function s(t,e){return"function"==typeof t?t.apply(void 0,e):t}function p(t,e){return 0===e?t:function(i){clearTimeout(n),n=setTimeout((function(){t(i)}),e)};var n}function c(t,e){var n=Object.assign({},t);return e.forEach((function(t){delete n[t]})),n}function u(t){return[].concat(t)}function l(t,e){-1===t.indexOf(e)&&t.push(e)}function f(t){return t.split("-")[0]}function d(t){return[].slice.call(t)}function v(){return document.createElement("div")}function m(t){return["Element","Fragment"].some((function(e){return a(t,e)}))}function h(t){return a(t,"MouseEvent")}function g(t){return!(!t||!t._tippy||t._tippy.reference!==t)}function y(t){return m(t)?[t]:function(t){return a(t,"NodeList")}(t)?d(t):Array.isArray(t)?t:d(document.querySelectorAll(t))}function b(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function w(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function x(t){var e,n=u(t)[0];return(null==n||null==(e=n.ownerDocument)?void 0:e.body)?n.ownerDocument:document}function E(t,e,n){var i=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[i](e,n)}))}var T,O={isTouch:!1},C=0;function A(){O.isTouch||(O.isTouch=!0,window.performance&&document.addEventListener("mousemove",j))}function j(){var t=performance.now();t-C<20&&(O.isTouch=!1,document.removeEventListener("mousemove",j)),C=t}function L(){var t=document.activeElement;if(g(t)){var e=t._tippy;t.blur&&!e.state.isVisible&&t.blur()}}function k(t){return[t+"() was called on a"+("destroy"===t?"n already-":" ")+"destroyed instance. This is a no-op but","indicates a potential memory leak."].join(" ")}function D(t){return t.replace(/[ \t]{2,}/g," ").replace(/^[ \t]*/gm,"").trim()}function S(t){return D("\n  %ctippy.js\n\n  %c"+D(t)+"\n\n  %c👷‍ This is a development-only message. It will be removed in production.\n  ")}function M(t){return[S(t),"color: #00C584; font-size: 1.3em; font-weight: bold;","line-height: 1.5","color: #a6a095;"]}function P(t,e){var n;t&&!T.has(e)&&(T.add(e),(n=console).warn.apply(n,M(e)))}function R(t,e){var n;t&&!T.has(e)&&(T.add(e),(n=console).error.apply(n,M(e)))}T=new Set;var I={animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},V=Object.assign({appendTo:function(){return document.body},aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},I,{},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),N=Object.keys(V);function B(t){var e=(t.plugins||[]).reduce((function(e,n){var i=n.name,r=n.defaultValue;return i&&(e[i]=void 0!==t[i]?t[i]:r),e}),{});return Object.assign({},t,{},e)}function H(t,e){var n=Object.assign({},e,{content:s(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(B(Object.assign({},V,{plugins:e}))):N).reduce((function(e,n){var i=(t.getAttribute("data-tippy-"+n)||"").trim();if(!i)return e;if("content"===n)e[n]=i;else try{e[n]=JSON.parse(i)}catch(t){e[n]=i}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},V.aria,{},n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function U(t,e){void 0===t&&(t={}),void 0===e&&(e=[]),Object.keys(t).forEach((function(t){var n,i,r=c(V,Object.keys(I)),o=(n=r,i=t,!{}.hasOwnProperty.call(n,i));o&&(o=0===e.filter((function(e){return e.name===t})).length),P(o,["`"+t+"`","is not a valid prop. You may have spelled it incorrectly, or if it's","a plugin, forgot to pass it in an array as props.plugins.","\n\n","All props: https://atomiks.github.io/tippyjs/v6/all-props/\n","Plugins: https://atomiks.github.io/tippyjs/v6/plugins/"].join(" "))}))}function F(t,e){t.innerHTML=e}function Y(t){var e=v();return!0===t?e.className="tippy-arrow":(e.className="tippy-svg-arrow",m(t)?e.appendChild(t):F(e,t)),e}function z(t,e){m(e.content)?(F(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?F(t,e.content):t.textContent=e.content)}function W(t){var e=t.firstElementChild,n=d(e.children);return{box:e,content:n.find((function(t){return t.classList.contains("tippy-content")})),arrow:n.find((function(t){return t.classList.contains("tippy-arrow")||t.classList.contains("tippy-svg-arrow")})),backdrop:n.find((function(t){return t.classList.contains("tippy-backdrop")}))}}function _(t){var e=v(),n=v();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var i=v();function r(n,i){var r=W(e),o=r.box,a=r.content,s=r.arrow;i.theme?o.setAttribute("data-theme",i.theme):o.removeAttribute("data-theme"),"string"==typeof i.animation?o.setAttribute("data-animation",i.animation):o.removeAttribute("data-animation"),i.inertia?o.setAttribute("data-inertia",""):o.removeAttribute("data-inertia"),o.style.maxWidth="number"==typeof i.maxWidth?i.maxWidth+"px":i.maxWidth,i.role?o.setAttribute("role",i.role):o.removeAttribute("role"),n.content===i.content&&n.allowHTML===i.allowHTML||z(a,t.props),i.arrow?s?n.arrow!==i.arrow&&(o.removeChild(s),o.appendChild(Y(i.arrow))):o.appendChild(Y(i.arrow)):s&&o.removeChild(s)}return i.className="tippy-content",i.setAttribute("data-state","hidden"),z(i,t.props),e.appendChild(n),n.appendChild(i),r(t.props,t.props),{popper:e,onUpdate:r}}_.$$tippy=!0;var q=1,X=[],$=[];function J(e,n){var a,c,m,g,y,T,C,A,j,L=H(e,Object.assign({},V,{},B((a=n,Object.keys(a).reduce((function(t,e){return void 0!==a[e]&&(t[e]=a[e]),t}),{}))))),D=!1,S=!1,M=!1,I=!1,N=[],U=p(xt,L.interactiveDebounce),F=q++,Y=(j=L.plugins).filter((function(t,e){return j.indexOf(t)===e})),z={id:F,reference:e,popper:v(),popperInstance:null,props:L,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:Y,clearDelayTimeouts:function(){clearTimeout(c),clearTimeout(m),cancelAnimationFrame(g)},setProps:function(t){if(P(z.state.isDestroyed,k("setProps")),z.state.isDestroyed)return;st("onBeforeUpdate",[z,t]),bt();var n=z.props,i=H(e,Object.assign({},z.props,{},t,{ignoreAttributes:!0}));z.props=i,yt(),n.interactiveDebounce!==i.interactiveDebounce&&(ut(),U=p(xt,i.interactiveDebounce));n.triggerTarget&&!i.triggerTarget?u(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):i.triggerTarget&&e.removeAttribute("aria-expanded");ct(),at(),G&&G(n,i);z.popperInstance&&(Ct(),jt().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)})));st("onAfterUpdate",[z,t])},setContent:function(t){z.setProps({content:t})},show:function(){P(z.state.isDestroyed,k("show"));var t=z.state.isVisible,e=z.state.isDestroyed,n=!z.state.isEnabled,i=O.isTouch&&!z.props.touch,r=o(z.props.duration,0,V.duration);if(t||e||n||i)return;if(nt().hasAttribute("disabled"))return;if(st("onShow",[z],!1),!1===z.props.onShow(z))return;z.state.isVisible=!0,et()&&(J.style.visibility="visible");at(),vt(),z.state.isMounted||(J.style.transition="none");if(et()){var a=rt(),p=a.box,c=a.content;b([p,c],0)}C=function(){var t;if(z.state.isVisible&&!I){if(I=!0,J.offsetHeight,J.style.transition=z.props.moveTransition,et()&&z.props.animation){var e=rt(),n=e.box,i=e.content;b([n,i],r),w([n,i],"visible")}pt(),ct(),l($,z),null==(t=z.popperInstance)||t.forceUpdate(),z.state.isMounted=!0,st("onMount",[z]),z.props.animation&&et()&&function(t,e){ht(t,e)}(r,(function(){z.state.isShown=!0,st("onShown",[z])}))}},function(){var t,e=z.props.appendTo,n=nt();t=z.props.interactive&&e===V.appendTo||"parent"===e?n.parentNode:s(e,[n]);t.contains(J)||t.appendChild(J);Ct(),P(z.props.interactive&&e===V.appendTo&&n.nextElementSibling!==J,["Interactive tippy element may not be accessible via keyboard","navigation because it is not directly after the reference element","in the DOM source order.","\n\n","Using a wrapper <div> or <span> tag around the reference element","solves this by creating a new parentNode context.","\n\n","Specifying `appendTo: document.body` silences this warning, but it","assumes you are using a focus management solution to handle","keyboard navigation.","\n\n","See: https://atomiks.github.io/tippyjs/v6/accessibility/#interactivity"].join(" "))}()},hide:function(){P(z.state.isDestroyed,k("hide"));var t=!z.state.isVisible,e=z.state.isDestroyed,n=!z.state.isEnabled,i=o(z.props.duration,1,V.duration);if(t||e||n)return;if(st("onHide",[z],!1),!1===z.props.onHide(z))return;z.state.isVisible=!1,z.state.isShown=!1,I=!1,D=!1,et()&&(J.style.visibility="hidden");if(ut(),mt(),at(),et()){var r=rt(),a=r.box,s=r.content;z.props.animation&&(b([a,s],i),w([a,s],"hidden"))}pt(),ct(),z.props.animation?et()&&function(t,e){ht(t,(function(){!z.state.isVisible&&J.parentNode&&J.parentNode.contains(J)&&e()}))}(i,z.unmount):z.unmount()},hideWithInteractivity:function(t){P(z.state.isDestroyed,k("hideWithInteractivity")),it().addEventListener("mousemove",U),l(X,U),U(t)},enable:function(){z.state.isEnabled=!0},disable:function(){z.hide(),z.state.isEnabled=!1},unmount:function(){P(z.state.isDestroyed,k("unmount")),z.state.isVisible&&z.hide();if(!z.state.isMounted)return;At(),jt().forEach((function(t){t._tippy.unmount()})),J.parentNode&&J.parentNode.removeChild(J);$=$.filter((function(t){return t!==z})),z.state.isMounted=!1,st("onHidden",[z])},destroy:function(){if(P(z.state.isDestroyed,k("destroy")),z.state.isDestroyed)return;z.clearDelayTimeouts(),z.unmount(),bt(),delete e._tippy,z.state.isDestroyed=!0,st("onDestroy",[z])}};if(!L.render)return R(!0,"render() function has not been supplied."),z;var _=L.render(z),J=_.popper,G=_.onUpdate;J.setAttribute("data-tippy-root",""),J.id="tippy-"+z.id,z.popper=J,e._tippy=z,J._tippy=z;var K=Y.map((function(t){return t.fn(z)})),Q=e.hasAttribute("aria-expanded");return yt(),ct(),at(),st("onCreate",[z]),L.showOnCreate&&Lt(),J.addEventListener("mouseenter",(function(){z.props.interactive&&z.state.isVisible&&z.clearDelayTimeouts()})),J.addEventListener("mouseleave",(function(t){z.props.interactive&&z.props.trigger.indexOf("mouseenter")>=0&&(it().addEventListener("mousemove",U),U(t))})),z;function Z(){var t=z.props.touch;return Array.isArray(t)?t:[t,0]}function tt(){return"hold"===Z()[0]}function et(){var t;return!!(null==(t=z.props.render)?void 0:t.$$tippy)}function nt(){return A||e}function it(){var t=nt().parentNode;return t?x(t):document}function rt(){return W(J)}function ot(t){return z.state.isMounted&&!z.state.isVisible||O.isTouch||y&&"focus"===y.type?0:o(z.props.delay,t?0:1,V.delay)}function at(){J.style.pointerEvents=z.props.interactive&&z.state.isVisible?"":"none",J.style.zIndex=""+z.props.zIndex}function st(t,e,n){var i;(void 0===n&&(n=!0),K.forEach((function(n){n[t]&&n[t].apply(void 0,e)})),n)&&(i=z.props)[t].apply(i,e)}function pt(){var t=z.props.aria;if(t.content){var n="aria-"+t.content,i=J.id;u(z.props.triggerTarget||e).forEach((function(t){var e=t.getAttribute(n);if(z.state.isVisible)t.setAttribute(n,e?e+" "+i:i);else{var r=e&&e.replace(i,"").trim();r?t.setAttribute(n,r):t.removeAttribute(n)}}))}}function ct(){!Q&&z.props.aria.expanded&&u(z.props.triggerTarget||e).forEach((function(t){z.props.interactive?t.setAttribute("aria-expanded",z.state.isVisible&&t===nt()?"true":"false"):t.removeAttribute("aria-expanded")}))}function ut(){it().removeEventListener("mousemove",U),X=X.filter((function(t){return t!==U}))}function lt(t){if(!(O.isTouch&&(M||"mousedown"===t.type)||z.props.interactive&&J.contains(t.target))){if(nt().contains(t.target)){if(O.isTouch)return;if(z.state.isVisible&&z.props.trigger.indexOf("click")>=0)return}else st("onClickOutside",[z,t]);!0===z.props.hideOnClick&&(z.clearDelayTimeouts(),z.hide(),S=!0,setTimeout((function(){S=!1})),z.state.isMounted||mt())}}function ft(){M=!0}function dt(){M=!1}function vt(){var t=it();t.addEventListener("mousedown",lt,!0),t.addEventListener("touchend",lt,r),t.addEventListener("touchstart",dt,r),t.addEventListener("touchmove",ft,r)}function mt(){var t=it();t.removeEventListener("mousedown",lt,!0),t.removeEventListener("touchend",lt,r),t.removeEventListener("touchstart",dt,r),t.removeEventListener("touchmove",ft,r)}function ht(t,e){var n=rt().box;function i(t){t.target===n&&(E(n,"remove",i),e())}if(0===t)return e();E(n,"remove",T),E(n,"add",i),T=i}function gt(t,n,i){void 0===i&&(i=!1),u(z.props.triggerTarget||e).forEach((function(e){e.addEventListener(t,n,i),N.push({node:e,eventType:t,handler:n,options:i})}))}function yt(){var t;tt()&&(gt("touchstart",wt,{passive:!0}),gt("touchend",Et,{passive:!0})),(t=z.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(gt(t,wt),t){case"mouseenter":gt("mouseleave",Et);break;case"focus":gt(i?"focusout":"blur",Tt);break;case"focusin":gt("focusout",Tt)}}))}function bt(){N.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,r=t.options;e.removeEventListener(n,i,r)})),N=[]}function wt(t){var e,n=!1;if(z.state.isEnabled&&!Ot(t)&&!S){var i="focus"===(null==(e=y)?void 0:e.type);y=t,A=t.currentTarget,ct(),!z.state.isVisible&&h(t)&&X.forEach((function(e){return e(t)})),"click"===t.type&&(z.props.trigger.indexOf("mouseenter")<0||D)&&!1!==z.props.hideOnClick&&z.state.isVisible?n=!0:Lt(t),"click"===t.type&&(D=!n),n&&!i&&kt(t)}}function xt(t){var e=t.target,n=nt().contains(e)||J.contains(e);"mousemove"===t.type&&n||function(t,e){var n=e.clientX,i=e.clientY;return t.every((function(t){var e=t.popperRect,r=t.popperState,o=t.props.interactiveBorder,a=f(r.placement),s=r.modifiersData.offset;if(!s)return!0;var p="bottom"===a?s.top.y:0,c="top"===a?s.bottom.y:0,u="right"===a?s.left.x:0,l="left"===a?s.right.x:0,d=e.top-i+p>o,v=i-e.bottom-c>o,m=e.left-n+u>o,h=n-e.right-l>o;return d||v||m||h}))}(jt().concat(J).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:L}:null})).filter(Boolean),t)&&(ut(),kt(t))}function Et(t){Ot(t)||z.props.trigger.indexOf("click")>=0&&D||(z.props.interactive?z.hideWithInteractivity(t):kt(t))}function Tt(t){z.props.trigger.indexOf("focusin")<0&&t.target!==nt()||z.props.interactive&&t.relatedTarget&&J.contains(t.relatedTarget)||kt(t)}function Ot(t){return!!O.isTouch&&tt()!==t.type.indexOf("touch")>=0}function Ct(){At();var n=z.props,i=n.popperOptions,r=n.placement,o=n.offset,a=n.getReferenceClientRect,s=n.moveTransition,p=et()?W(J).arrow:null,c=a?{getBoundingClientRect:a,contextElement:a.contextElement||nt()}:e,u=[{name:"offset",options:{offset:o}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(et()){var n=rt().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];et()&&p&&u.push({name:"arrow",options:{element:p,padding:3}}),u.push.apply(u,(null==i?void 0:i.modifiers)||[]),z.popperInstance=t.createPopper(c,J,Object.assign({},i,{placement:r,onFirstUpdate:C,modifiers:u}))}function At(){z.popperInstance&&(z.popperInstance.destroy(),z.popperInstance=null)}function jt(){return d(J.querySelectorAll("[data-tippy-root]"))}function Lt(t){z.clearDelayTimeouts(),t&&st("onTrigger",[z,t]),vt();var e=ot(!0),n=Z(),i=n[0],r=n[1];O.isTouch&&"hold"===i&&r&&(e=r),e?c=setTimeout((function(){z.show()}),e):z.show()}function kt(t){if(z.clearDelayTimeouts(),st("onUntrigger",[z,t]),z.state.isVisible){if(!(z.props.trigger.indexOf("mouseenter")>=0&&z.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&D)){var e=ot(!1);e?m=setTimeout((function(){z.state.isVisible&&z.hide()}),e):g=requestAnimationFrame((function(){z.hide()}))}}else mt()}}function G(t,e){void 0===e&&(e={});var n=V.plugins.concat(e.plugins||[]);!function(t){var e=!t,n="[object Object]"===Object.prototype.toString.call(t)&&!t.addEventListener;R(e,["tippy() was passed","`"+String(t)+"`","as its targets (first) argument. Valid types are: String, Element,","Element[], or NodeList."].join(" ")),R(n,["tippy() was passed a plain object which is not supported as an argument","for virtual positioning. Use props.getReferenceClientRect instead."].join(" "))}(t),U(e,n),document.addEventListener("touchstart",A,r),window.addEventListener("blur",L);var i=Object.assign({},e,{plugins:n}),o=y(t),a=m(i.content),s=o.length>1;P(a&&s,["tippy() was passed an Element as the `content` prop, but more than","one tippy instance was created by this invocation. This means the","content element will only be appended to the last tippy instance.","\n\n","Instead, pass the .innerHTML of the element, or use a function that","returns a cloned version of the element instead.","\n\n","1) content: element.innerHTML\n","2) content: () => element.cloneNode(true)"].join(" "));var p=o.reduce((function(t,e){var n=e&&J(e,i);return n&&t.push(n),t}),[]);return m(t)?p[0]:p}G.defaultProps=V,G.setDefaultProps=function(t){U(t,[]),Object.keys(t).forEach((function(e){V[e]=t[e]}))},G.currentInput=O;var K=Object.assign({},t.applyStyles,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}}),Q={mouseover:"mouseenter",focusin:"focus",click:"click"};var Z={name:"animateFill",defaultValue:!1,fn:function(t){var e;if(!(null==(e=t.props.render)?void 0:e.$$tippy))return R(t.props.animateFill,"The `animateFill` plugin requires the default render function."),{};var n=W(t.popper),i=n.box,r=n.content,o=t.props.animateFill?function(){var t=v();return t.className="tippy-backdrop",w([t],"hidden"),t}():null;return{onCreate:function(){o&&(i.insertBefore(o,i.firstElementChild),i.setAttribute("data-animatefill",""),i.style.overflow="hidden",t.setProps({arrow:!1,animation:"shift-away"}))},onMount:function(){if(o){var t=i.style.transitionDuration,e=Number(t.replace("ms",""));r.style.transitionDelay=Math.round(e/10)+"ms",o.style.transitionDuration=t,w([o],"visible")}},onShow:function(){o&&(o.style.transitionDuration="0ms")},onHide:function(){o&&w([o],"hidden")}}}};var tt={clientX:0,clientY:0},et=[];function nt(t){var e=t.clientX,n=t.clientY;tt={clientX:e,clientY:n}}var it={name:"followCursor",defaultValue:!1,fn:function(t){var e=t.reference,n=x(t.props.triggerTarget||e),i=!1,r=!1,o=!0,a=t.props;function s(){return"initial"===t.props.followCursor&&t.state.isVisible}function p(){n.addEventListener("mousemove",l)}function c(){n.removeEventListener("mousemove",l)}function u(){i=!0,t.setProps({getReferenceClientRect:null}),i=!1}function l(n){var i=!n.target||e.contains(n.target),r=t.props.followCursor,o=n.clientX,a=n.clientY,s=e.getBoundingClientRect(),p=o-s.left,c=a-s.top;!i&&t.props.interactive||t.setProps({getReferenceClientRect:function(){var t=e.getBoundingClientRect(),n=o,i=a;"initial"===r&&(n=t.left+p,i=t.top+c);var s="horizontal"===r?t.top:i,u="vertical"===r?t.right:n,l="horizontal"===r?t.bottom:i,f="vertical"===r?t.left:n;return{width:u-f,height:l-s,top:s,right:u,bottom:l,left:f}}})}function f(){t.props.followCursor&&(et.push({instance:t,doc:n}),function(t){t.addEventListener("mousemove",nt)}(n))}function d(){0===(et=et.filter((function(e){return e.instance!==t}))).filter((function(t){return t.doc===n})).length&&function(t){t.removeEventListener("mousemove",nt)}(n)}return{onCreate:f,onDestroy:d,onBeforeUpdate:function(){a=t.props},onAfterUpdate:function(e,n){var o=n.followCursor;i||void 0!==o&&a.followCursor!==o&&(d(),o?(f(),!t.state.isMounted||r||s()||p()):(c(),u()))},onMount:function(){t.props.followCursor&&!r&&(o&&(l(tt),o=!1),s()||p())},onTrigger:function(t,e){h(e)&&(tt={clientX:e.clientX,clientY:e.clientY}),r="focus"===e.type},onHidden:function(){t.props.followCursor&&(u(),c(),o=!0)}}}};var rt={name:"inlinePositioning",defaultValue:!1,fn:function(t){var e,n=t.reference;var i=-1,r=!1,o={name:"tippyInlinePositioning",enabled:!0,phase:"afterWrite",fn:function(r){var o=r.state;t.props.inlinePositioning&&(e!==o.placement&&t.setProps({getReferenceClientRect:function(){return function(t){return function(t,e,n,i){if(n.length<2||null===t)return e;if(2===n.length&&i>=0&&n[0].left>n[1].right)return n[i]||e;switch(t){case"top":case"bottom":var r=n[0],o=n[n.length-1],a="top"===t,s=r.top,p=o.bottom,c=a?r.left:o.left,u=a?r.right:o.right;return{top:s,bottom:p,left:c,right:u,width:u-c,height:p-s};case"left":case"right":var l=Math.min.apply(Math,n.map((function(t){return t.left}))),f=Math.max.apply(Math,n.map((function(t){return t.right}))),d=n.filter((function(e){return"left"===t?e.left===l:e.right===f})),v=d[0].top,m=d[d.length-1].bottom;return{top:v,bottom:m,left:l,right:f,width:f-l,height:m-v};default:return e}}(f(t),n.getBoundingClientRect(),d(n.getClientRects()),i)}(o.placement)}}),e=o.placement)}};function a(){var e;r||(e=function(t,e){var n;return{popperOptions:Object.assign({},t.popperOptions,{modifiers:[].concat(((null==(n=t.popperOptions)?void 0:n.modifiers)||[]).filter((function(t){return t.name!==e.name})),[e])})}}(t.props,o),r=!0,t.setProps(e),r=!1)}return{onCreate:a,onAfterUpdate:a,onTrigger:function(e,n){if(h(n)){var r=d(t.reference.getClientRects()),o=r.find((function(t){return t.left-2<=n.clientX&&t.right+2>=n.clientX&&t.top-2<=n.clientY&&t.bottom+2>=n.clientY}));i=r.indexOf(o)}},onUntrigger:function(){i=-1}}}};var ot={name:"sticky",defaultValue:!1,fn:function(t){var e=t.reference,n=t.popper;function i(e){return!0===t.props.sticky||t.props.sticky===e}var r=null,o=null;function a(){var s=i("reference")?(t.popperInstance?t.popperInstance.state.elements.reference:e).getBoundingClientRect():null,p=i("popper")?n.getBoundingClientRect():null;(s&&at(r,s)||p&&at(o,p))&&t.popperInstance&&t.popperInstance.update(),r=s,o=p,t.state.isMounted&&requestAnimationFrame(a)}return{onMount:function(){t.props.sticky&&a()}}}};function at(t,e){return!t||!e||(t.top!==e.top||t.right!==e.right||t.bottom!==e.bottom||t.left!==e.left)}return e&&function(t){var e=document.createElement("style");e.textContent=t,e.setAttribute("data-tippy-stylesheet","");var n=document.head,i=document.querySelector("head>style,head>link");i?n.insertBefore(e,i):n.appendChild(e)}('.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:8px 16px;z-index:1}'),G.setDefaultProps({plugins:[Z,it,rt,ot],render:_}),G.createSingleton=function(t,e){var n;void 0===e&&(e={}),R(!Array.isArray(t),["The first argument passed to createSingleton() must be an array of","tippy instances. The passed value was",String(t)].join(" "));var i,r=t,o=[],a=e.overrides,s=[],p=!1;function u(){o=r.map((function(t){return t.reference}))}function l(t){r.forEach((function(e){t?e.enable():e.disable()}))}function f(t){return r.map((function(e){var n=e.setProps;return e.setProps=function(r){n(r),e.reference===i&&t.setProps(r)},function(){e.setProps=n}}))}function d(t,e){var n=o.indexOf(e);if(e!==i){i=e;var s=(a||[]).concat("content").reduce((function(t,e){return t[e]=r[n].props[e],t}),{});t.setProps(Object.assign({},s,{getReferenceClientRect:"function"==typeof s.getReferenceClientRect?s.getReferenceClientRect:function(){return e.getBoundingClientRect()}}))}}l(!1),u();var m={fn:function(){return{onDestroy:function(){l(!0)},onHidden:function(){i=null},onClickOutside:function(t){t.props.showOnCreate&&!p&&(p=!0,i=null)},onShow:function(t){t.props.showOnCreate&&!p&&(p=!0,d(t,o[0]))},onTrigger:function(t,e){d(t,e.currentTarget)}}}},h=G(v(),Object.assign({},c(e,["overrides"]),{plugins:[m].concat(e.plugins||[]),triggerTarget:o,popperOptions:Object.assign({},e.popperOptions,{modifiers:[].concat((null==(n=e.popperOptions)?void 0:n.modifiers)||[],[K])})})),g=h.show;h.show=function(t){if(g(),!i&&null==t)return d(h,o[0]);if(!i||null!=t){if("number"==typeof t)return o[t]&&d(h,o[t]);if(r.includes(t)){var e=t.reference;return d(h,e)}return o.includes(t)?d(h,t):void 0}},h.showNext=function(){var t=o[0];if(!i)return h.show(0);var e=o.indexOf(i);h.show(o[e+1]||t)},h.showPrevious=function(){var t=o[o.length-1];if(!i)return h.show(t);var e=o.indexOf(i),n=o[e-1]||t;h.show(n)};var y=h.setProps;return h.setProps=function(t){a=t.overrides||a,y(t)},h.setInstances=function(t){l(!0),s.forEach((function(t){return t()})),r=t,l(!1),u(),f(h),h.setProps({triggerTarget:o})},s=f(h),h},G.delegate=function(t,e){R(!(e&&e.target),["You must specity a `target` prop indicating a CSS selector string matching","the target elements that should receive a tippy."].join(" "));var n=[],i=[],o=!1,a=e.target,s=c(e,["target"]),p=Object.assign({},s,{trigger:"manual",touch:!1}),l=Object.assign({},s,{showOnCreate:!0}),f=G(t,p);function d(t){if(t.target&&!o){var n=t.target.closest(a);if(n){var r=n.getAttribute("data-tippy-trigger")||e.trigger||V.trigger;if(!n._tippy&&!("touchstart"===t.type&&"boolean"==typeof l.touch||"touchstart"!==t.type&&r.indexOf(Q[t.type])<0)){var s=G(n,l);s&&(i=i.concat(s))}}}}function v(t,e,i,r){void 0===r&&(r=!1),t.addEventListener(e,i,r),n.push({node:t,eventType:e,handler:i,options:r})}return u(f).forEach((function(t){var e=t.destroy,a=t.enable,s=t.disable;t.destroy=function(t){void 0===t&&(t=!0),t&&i.forEach((function(t){t.destroy()})),i=[],n.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,r=t.options;e.removeEventListener(n,i,r)})),n=[],e()},t.enable=function(){a(),i.forEach((function(t){return t.enable()})),o=!1},t.disable=function(){s(),i.forEach((function(t){return t.disable()})),o=!0},function(t){var e=t.reference;v(e,"touchstart",d,r),v(e,"mouseover",d),v(e,"focusin",d),v(e,"click",d)}(t)})),f},G.hideAll=function(t){var e=void 0===t?{}:t,n=e.exclude,i=e.duration;$.forEach((function(t){var e=!1;if(n&&(e=g(n)?t.reference===n:t.popper===n.popper),!e){var r=t.props.duration;t.setProps({duration:i}),t.hide(),t.state.isDestroyed||t.setProps({duration:r})}}))},G.roundArrow='<svg width="16" height="6" xmlns="http://www.w3.org/2000/svg"><path d="M0 6s1.796-.013 4.67-3.615C5.851.9 6.93.006 8 0c1.07-.006 2.148.887 3.343 2.385C14.233 6.005 16 6 16 6H0z"></svg>',G}));