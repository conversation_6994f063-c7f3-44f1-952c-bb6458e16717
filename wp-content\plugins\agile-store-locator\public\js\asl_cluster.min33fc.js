/*Marker Cluster:: New One*/
var markerClusterer=function(t){"use strict";function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function n(t,e,n){return e&&r(t.prototype,e),n&&r(t,n),t}function o(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&s(t,e)}function i(t){return(i=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function a(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function u(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=i(t);if(e){var o=i(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return a(this,r)}}function c(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var r=[],n=!0,o=!1,i=void 0;try{for(var s,a=t[Symbol.iterator]();!(n=(s=a.next()).done)&&(r.push(s.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==a.return||a.return()}finally{if(o)throw i}}return r}(t,e)||l(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||l(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},d=function(t){return t&&t.Math==Math&&t},m=d("object"==typeof globalThis&&globalThis)||d("object"==typeof window&&window)||d("object"==typeof self&&self)||d("object"==typeof p&&p)||function(){return this}()||Function("return this")(),g={},v=function(t){try{return!!t()}catch(t){return!0}},y=!v((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),b=!v((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),_=b,w=Function.prototype.call,k=_?w.bind(w):function(){return w.apply(w,arguments)},x={},M={}.propertyIsEnumerable,O=Object.getOwnPropertyDescriptor,P=O&&!M.call({1:2},1);x.f=P?function(t){var e=O(this,t);return!!e&&e.enumerable}:M;var S,E,C=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}},j=b,L=Function.prototype,A=L.bind,T=L.call,I=j&&A.bind(T,T),N=j?function(t){return t&&I(t)}:function(t){return t&&function(){return T.apply(t,arguments)}},D=N,F=D({}.toString),z=D("".slice),R=function(t){return z(F(t),8,-1)},q=N,Z=v,G=R,B=m.Object,V=q("".split),U=Z((function(){return!B("z").propertyIsEnumerable(0)}))?function(t){return"String"==G(t)?V(t,""):B(t)}:B,W=m.TypeError,Q=function(t){if(null==t)throw W("Can't call method on "+t);return t},X=U,H=Q,J=function(t){return X(H(t))},K=function(t){return"function"==typeof t},$=K,Y=function(t){return"object"==typeof t?null!==t:$(t)},tt=m,et=K,rt=function(t){return et(t)?t:void 0},nt=function(t,e){return arguments.length<2?rt(tt[t]):tt[t]&&tt[t][e]},ot=N({}.isPrototypeOf),it=m,st=nt("navigator","userAgent")||"",at=it.process,ut=it.Deno,ct=at&&at.versions||ut&&ut.version,ft=ct&&ct.v8;ft&&(E=(S=ft.split("."))[0]>0&&S[0]<4?1:+(S[0]+S[1])),!E&&st&&(!(S=st.match(/Edge\/(\d+)/))||S[1]>=74)&&(S=st.match(/Chrome\/(\d+)/))&&(E=+S[1]);var lt=E,ht=lt,pt=v,dt=!!Object.getOwnPropertySymbols&&!pt((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&ht&&ht<41})),mt=dt&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,gt=nt,vt=K,yt=ot,bt=mt,_t=m.Object,wt=bt?function(t){return"symbol"==typeof t}:function(t){var e=gt("Symbol");return vt(e)&&yt(e.prototype,_t(t))},kt=m.String,xt=K,Mt=function(t){try{return kt(t)}catch(t){return"Object"}},Ot=m.TypeError,Pt=function(t){if(xt(t))return t;throw Ot(Mt(t)+" is not a function")},St=Pt,Et=k,Ct=K,jt=Y,Lt=m.TypeError,At={exports:{}},Tt=m,It=Object.defineProperty,Nt=function(t,e){try{It(Tt,t,{value:e,configurable:!0,writable:!0})}catch(r){Tt[t]=e}return e},Dt=Nt,Ft="__core-js_shared__",zt=m[Ft]||Dt(Ft,{}),Rt=zt;(At.exports=function(t,e){return Rt[t]||(Rt[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.20.3",mode:"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.20.3/LICENSE",source:"https://github.com/zloirock/core-js"});var qt=Q,Zt=m.Object,Gt=function(t){return Zt(qt(t))},Bt=Gt,Vt=N({}.hasOwnProperty),Ut=Object.hasOwn||function(t,e){return Vt(Bt(t),e)},Wt=N,Qt=0,Xt=Math.random(),Ht=Wt(1..toString),Jt=function(t){return"Symbol("+(void 0===t?"":t)+")_"+Ht(++Qt+Xt,36)},Kt=m,$t=At.exports,Yt=Ut,te=Jt,ee=dt,re=mt,ne=$t("wks"),oe=Kt.Symbol,ie=oe&&oe.for,se=re?oe:oe&&oe.withoutSetter||te,ae=function(t){if(!Yt(ne,t)||!ee&&"string"!=typeof ne[t]){var e="Symbol."+t;ee&&Yt(oe,t)?ne[t]=oe[t]:ne[t]=re&&ie?ie(e):se(e)}return ne[t]},ue=k,ce=Y,fe=wt,le=function(t,e){var r=t[e];return null==r?void 0:St(r)},he=function(t,e){var r,n;if("string"===e&&Ct(r=t.toString)&&!jt(n=Et(r,t)))return n;if(Ct(r=t.valueOf)&&!jt(n=Et(r,t)))return n;if("string"!==e&&Ct(r=t.toString)&&!jt(n=Et(r,t)))return n;throw Lt("Can't convert object to primitive value")},pe=ae,de=m.TypeError,me=pe("toPrimitive"),ge=function(t,e){if(!ce(t)||fe(t))return t;var r,n=le(t,me);if(n){if(void 0===e&&(e="default"),r=ue(n,t,e),!ce(r)||fe(r))return r;throw de("Can't convert object to primitive value")}return void 0===e&&(e="number"),he(t,e)},ve=ge,ye=wt,be=function(t){var e=ve(t,"string");return ye(e)?e:e+""},_e=Y,we=m.document,ke=_e(we)&&_e(we.createElement),xe=function(t){return ke?we.createElement(t):{}},Me=xe,Oe=!y&&!v((function(){return 7!=Object.defineProperty(Me("div"),"a",{get:function(){return 7}}).a})),Pe=y,Se=k,Ee=x,Ce=C,je=J,Le=be,Ae=Ut,Te=Oe,Ie=Object.getOwnPropertyDescriptor;g.f=Pe?Ie:function(t,e){if(t=je(t),e=Le(e),Te)try{return Ie(t,e)}catch(t){}if(Ae(t,e))return Ce(!Se(Ee.f,t,e),t[e])};var Ne={},De=y&&v((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Fe=m,ze=Y,Re=Fe.String,qe=Fe.TypeError,Ze=function(t){if(ze(t))return t;throw qe(Re(t)+" is not an object")},Ge=y,Be=Oe,Ve=De,Ue=Ze,We=be,Qe=m.TypeError,Xe=Object.defineProperty,He=Object.getOwnPropertyDescriptor,Je="enumerable",Ke="configurable",$e="writable";Ne.f=Ge?Ve?function(t,e,r){if(Ue(t),e=We(e),Ue(r),"function"==typeof t&&"prototype"===e&&"value"in r&&$e in r&&!r.writable){var n=He(t,e);n&&n.writable&&(t[e]=r.value,r={configurable:Ke in r?r.configurable:n.configurable,enumerable:Je in r?r.enumerable:n.enumerable,writable:!1})}return Xe(t,e,r)}:Xe:function(t,e,r){if(Ue(t),e=We(e),Ue(r),Be)try{return Xe(t,e,r)}catch(t){}if("get"in r||"set"in r)throw Qe("Accessors not supported");return"value"in r&&(t[e]=r.value),t};var Ye=Ne,tr=C,er=y?function(t,e,r){return Ye.f(t,e,tr(1,r))}:function(t,e,r){return t[e]=r,t},rr={exports:{}},nr=K,or=zt,ir=N(Function.toString);nr(or.inspectSource)||(or.inspectSource=function(t){return ir(t)});var sr,ar,ur,cr=or.inspectSource,fr=K,lr=cr,hr=m.WeakMap,pr=fr(hr)&&/native code/.test(lr(hr)),dr=At.exports,mr=Jt,gr=dr("keys"),vr=function(t){return gr[t]||(gr[t]=mr(t))},yr={},br=pr,_r=m,wr=N,kr=Y,xr=er,Mr=Ut,Or=zt,Pr=vr,Sr=yr,Er="Object already initialized",Cr=_r.TypeError,jr=_r.WeakMap;if(br||Or.state){var Lr=Or.state||(Or.state=new jr),Ar=wr(Lr.get),Tr=wr(Lr.has),Ir=wr(Lr.set);sr=function(t,e){if(Tr(Lr,t))throw new Cr(Er);return e.facade=t,Ir(Lr,t,e),e},ar=function(t){return Ar(Lr,t)||{}},ur=function(t){return Tr(Lr,t)}}else{var Nr=Pr("state");Sr[Nr]=!0,sr=function(t,e){if(Mr(t,Nr))throw new Cr(Er);return e.facade=t,xr(t,Nr,e),e},ar=function(t){return Mr(t,Nr)?t[Nr]:{}},ur=function(t){return Mr(t,Nr)}}var Dr={set:sr,get:ar,has:ur,enforce:function(t){return ur(t)?ar(t):sr(t,{})},getterFor:function(t){return function(e){var r;if(!kr(e)||(r=ar(e)).type!==t)throw Cr("Incompatible receiver, "+t+" required");return r}}},Fr=y,zr=Ut,Rr=Function.prototype,qr=Fr&&Object.getOwnPropertyDescriptor,Zr=zr(Rr,"name"),Gr=Zr&&"something"===function(){}.name,Br=Zr&&(!Fr||Fr&&qr(Rr,"name").configurable),Vr=m,Ur=K,Wr=Ut,Qr=er,Xr=Nt,Hr=cr,Jr={EXISTS:Zr,PROPER:Gr,CONFIGURABLE:Br}.CONFIGURABLE,Kr=Dr.get,$r=Dr.enforce,Yr=String(String).split("String");(rr.exports=function(t,e,r,n){var o,i=!!n&&!!n.unsafe,s=!!n&&!!n.enumerable,a=!!n&&!!n.noTargetGet,u=n&&void 0!==n.name?n.name:e;Ur(r)&&("Symbol("===String(u).slice(0,7)&&(u="["+String(u).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),(!Wr(r,"name")||Jr&&r.name!==u)&&Qr(r,"name",u),(o=$r(r)).source||(o.source=Yr.join("string"==typeof u?u:""))),t!==Vr?(i?!a&&t[e]&&(s=!0):delete t[e],s?t[e]=r:Qr(t,e,r)):s?t[e]=r:Xr(e,r)})(Function.prototype,"toString",(function(){return Ur(this)&&Kr(this).source||Hr(this)}));var tn={},en=Math.ceil,rn=Math.floor,nn=function(t){var e=+t;return e!=e||0===e?0:(e>0?rn:en)(e)},on=nn,sn=Math.max,an=Math.min,un=function(t,e){var r=on(t);return r<0?sn(r+e,0):an(r,e)},cn=nn,fn=Math.min,ln=function(t){return t>0?fn(cn(t),9007199254740991):0},hn=function(t){return ln(t.length)},pn=J,dn=un,mn=hn,gn=function(t){return function(e,r,n){var o,i=pn(e),s=mn(i),a=dn(n,s);if(t&&r!=r){for(;s>a;)if((o=i[a++])!=o)return!0}else for(;s>a;a++)if((t||a in i)&&i[a]===r)return t||a||0;return!t&&-1}},vn={includes:gn(!0),indexOf:gn(!1)},yn=Ut,bn=J,_n=vn.indexOf,wn=yr,kn=N([].push),xn=function(t,e){var r,n=bn(t),o=0,i=[];for(r in n)!yn(wn,r)&&yn(n,r)&&kn(i,r);for(;e.length>o;)yn(n,r=e[o++])&&(~_n(i,r)||kn(i,r));return i},Mn=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],On=xn,Pn=Mn.concat("length","prototype");tn.f=Object.getOwnPropertyNames||function(t){return On(t,Pn)};var Sn={};Sn.f=Object.getOwnPropertySymbols;var En=nt,Cn=tn,jn=Sn,Ln=Ze,An=N([].concat),Tn=En("Reflect","ownKeys")||function(t){var e=Cn.f(Ln(t)),r=jn.f;return r?An(e,r(t)):e},In=Ut,Nn=Tn,Dn=g,Fn=Ne,zn=v,Rn=K,qn=/#|\.prototype\./,Zn=function(t,e){var r=Bn[Gn(t)];return r==Un||r!=Vn&&(Rn(e)?zn(e):!!e)},Gn=Zn.normalize=function(t){return String(t).replace(qn,".").toLowerCase()},Bn=Zn.data={},Vn=Zn.NATIVE="N",Un=Zn.POLYFILL="P",Wn=Zn,Qn=m,Xn=g.f,Hn=er,Jn=rr.exports,Kn=Nt,$n=function(t,e,r){for(var n=Nn(e),o=Fn.f,i=Dn.f,s=0;s<n.length;s++){var a=n[s];In(t,a)||r&&In(r,a)||o(t,a,i(e,a))}},Yn=Wn,to=function(t,e){var r,n,o,i,s,a=t.target,u=t.global,c=t.stat;if(r=u?Qn:c?Qn[a]||Kn(a,{}):(Qn[a]||{}).prototype)for(n in e){if(i=e[n],o=t.noTargetGet?(s=Xn(r,n))&&s.value:r[n],!Yn(u?n:a+(c?".":"#")+n,t.forced)&&void 0!==o){if(typeof i==typeof o)continue;$n(i,o)}(t.sham||o&&o.sham)&&Hn(i,"sham",!0),Jn(r,n,i,t)}},eo=Pt,ro=b,no=N(N.bind),oo=R,io=Array.isArray||function(t){return"Array"==oo(t)},so={};so[ae("toStringTag")]="z";var ao="[object z]"===String(so),uo=m,co=ao,fo=K,lo=R,ho=ae("toStringTag"),po=uo.Object,mo="Arguments"==lo(function(){return arguments}()),go=co?lo:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=po(t),ho))?r:mo?lo(e):"Object"==(n=lo(e))&&fo(e.callee)?"Arguments":n},vo=N,yo=v,bo=K,_o=go,wo=cr,ko=function(){},xo=[],Mo=nt("Reflect","construct"),Oo=/^\s*(?:class|function)\b/,Po=vo(Oo.exec),So=!Oo.exec(ko),Eo=function(t){if(!bo(t))return!1;try{return Mo(ko,xo,t),!0}catch(t){return!1}},Co=function(t){if(!bo(t))return!1;switch(_o(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return So||!!Po(Oo,wo(t))}catch(t){return!0}};Co.sham=!0;var jo=!Mo||yo((function(){var t;return Eo(Eo.call)||!Eo(Object)||!Eo((function(){t=!0}))||t}))?Co:Eo,Lo=m,Ao=io,To=jo,Io=Y,No=ae("species"),Do=Lo.Array,Fo=function(t){var e;return Ao(t)&&(e=t.constructor,(To(e)&&(e===Do||Ao(e.prototype))||Io(e)&&null===(e=e[No]))&&(e=void 0)),void 0===e?Do:e},zo=function(t,e){return new(Fo(t))(0===e?0:e)},Ro=function(t,e){return eo(t),void 0===e?t:ro?no(t,e):function(){return t.apply(e,arguments)}},qo=U,Zo=Gt,Go=hn,Bo=zo,Vo=N([].push),Uo=function(t){var e=1==t,r=2==t,n=3==t,o=4==t,i=6==t,s=7==t,a=5==t||i;return function(u,c,f,l){for(var h,p,d=Zo(u),m=qo(d),g=Ro(c,f),v=Go(m),y=0,b=l||Bo,_=e?b(u,v):r||s?b(u,0):void 0;v>y;y++)if((a||y in m)&&(p=g(h=m[y],y,d),t))if(e)_[y]=p;else if(p)switch(t){case 3:return!0;case 5:return h;case 6:return y;case 2:Vo(_,h)}else switch(t){case 4:return!1;case 7:Vo(_,h)}return i?-1:n||o?o:_}},Wo={forEach:Uo(0),map:Uo(1),filter:Uo(2),some:Uo(3),every:Uo(4),find:Uo(5),findIndex:Uo(6),filterReject:Uo(7)},Qo=v,Xo=lt,Ho=ae("species"),Jo=function(t){return Xo>=51||!Qo((function(){var e=[];return(e.constructor={})[Ho]=function(){return{foo:1}},1!==e[t](Boolean).foo}))},Ko=Wo.map;function $o(t,e){var r={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(r[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(r[n[o]]=t[n[o]])}return r}to({target:"Array",proto:!0,forced:!Jo("map")},{map:function(t){return Ko(this,t,arguments.length>1?arguments[1]:void 0)}});var Yo=Pt,ti=Gt,ei=U,ri=hn,ni=m.TypeError,oi=function(t){return function(e,r,n,o){Yo(r);var i=ti(e),s=ei(i),a=ri(i),u=t?a-1:0,c=t?-1:1;if(n<2)for(;;){if(u in s){o=s[u],u+=c;break}if(u+=c,t?u<0:a<=u)throw ni("Reduce of empty array with no initial value")}for(;t?u>=0:a>u;u+=c)u in s&&(o=r(o,s[u],u,i));return o}},ii={left:oi(!1),right:oi(!0)},si=v,ai=function(t,e){var r=[][t];return!!r&&si((function(){r.call(null,e||function(){throw 1},1)}))},ui="process"==R(m.process),ci=ii.left,fi=lt,li=ui;to({target:"Array",proto:!0,forced:!ai("reduce")||!li&&fi>79&&fi<83},{reduce:function(t){var e=arguments.length;return ci(this,t,e,e>1?arguments[1]:void 0)}});var hi=go,pi=ao?{}.toString:function(){return"[object "+hi(this)+"]"},di=ao,mi=rr.exports,gi=pi;di||mi(Object.prototype,"toString",gi,{unsafe:!0});var vi=Wo.filter;to({target:"Array",proto:!0,forced:!Jo("filter")},{filter:function(t){return vi(this,t,arguments.length>1?arguments[1]:void 0)}});var yi=function(){function t(r){var n=r.markers,o=r.position;e(this,t),this.markers=n,o&&(o instanceof google.maps.LatLng?this._position=o:this._position=new google.maps.LatLng(o))}return n(t,[{key:"bounds",get:function(){if(0!==this.markers.length||this._position)return this.markers.reduce((function(t,e){return t.extend(e.getPosition())}),new google.maps.LatLngBounds(this._position,this._position))}},{key:"position",get:function(){return this._position||this.bounds.getCenter()}},{key:"count",get:function(){return this.markers.filter((function(t){return t.getVisible()})).length}},{key:"push",value:function(t){this.markers.push(t)}},{key:"delete",value:function(){this.marker&&(this.marker.setMap(null),delete this.marker),this.markers.length=0}}]),t}(),bi=function(t,e,r,n){var o=_i(t.getBounds(),e,n);return r.filter((function(t){return o.contains(t.getPosition())}))},_i=function(t,e,r){var n=ki(t,e),o=n.northEast,i=n.southWest,s=xi({northEast:o,southWest:i},r);return Mi(s,e)},wi=function(t,e){var r=(e.lat-t.lat)*Math.PI/180,n=(e.lng-t.lng)*Math.PI/180,o=Math.sin(r/2)*Math.sin(r/2)+Math.cos(t.lat*Math.PI/180)*Math.cos(e.lat*Math.PI/180)*Math.sin(n/2)*Math.sin(n/2);return 6371*(2*Math.atan2(Math.sqrt(o),Math.sqrt(1-o)))},ki=function(t,e){return{northEast:e.fromLatLngToDivPixel(t.getNorthEast()),southWest:e.fromLatLngToDivPixel(t.getSouthWest())}},xi=function(t,e){var r=t.northEast,n=t.southWest;return r.x+=e,r.y-=e,n.x-=e,n.y+=e,{northEast:r,southWest:n}},Mi=function(t,e){var r=t.northEast,n=t.southWest,o=new google.maps.LatLngBounds;return o.extend(e.fromDivPixelToLatLng(r)),o.extend(e.fromDivPixelToLatLng(n)),o},Oi=function(){function t(r){var n=r.maxZoom,o=void 0===n?16:n;e(this,t),this.maxZoom=o}return n(t,[{key:"noop",value:function(t){var e=t.markers;return Si(e)}}]),t}(),Pi=function(t){o(i,t);var r=u(i);function i(t){var n;e(this,i);var o=t.viewportPadding,s=void 0===o?60:o,a=$o(t,["viewportPadding"]);return(n=r.call(this,a)).viewportPadding=60,n.viewportPadding=s,n}return n(i,[{key:"calculate",value:function(t){var e=t.markers,r=t.map,n=t.mapCanvasProjection;return r.getZoom()>=this.maxZoom?{clusters:this.noop({markers:e,map:r,mapCanvasProjection:n}),changed:!1}:{clusters:this.cluster({markers:bi(r,n,e,this.viewportPadding),map:r,mapCanvasProjection:n})}}}]),i}(Oi),Si=function(t){return t.map((function(t){return new yi({position:t.getPosition(),markers:[t]})}))},Ei=xe("span").classList,Ci=Ei&&Ei.constructor&&Ei.constructor.prototype,ji=Ci===Object.prototype?void 0:Ci,Li=Wo.forEach,Ai=m,Ti={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0},Ii=ji,Ni=ai("forEach")?[].forEach:function(t){return Li(this,t,arguments.length>1?arguments[1]:void 0)},Di=er,Fi=function(t){if(t&&t.forEach!==Ni)try{Di(t,"forEach",Ni)}catch(e){t.forEach=Ni}};for(var zi in Ti)Ti[zi]&&Fi(Ai[zi]&&Ai[zi].prototype);Fi(Ii);var Ri=k;to({target:"URL",proto:!0,enumerable:!0},{toJSON:function(){return Ri(URL.prototype.toString,this)}});var qi=function(t){o(i,t);var r=u(i);function i(t){var n;e(this,i);var o=t.maxDistance,s=void 0===o?4e4:o,a=t.gridSize,u=void 0===a?40:a,c=$o(t,["maxDistance","gridSize"]);return(n=r.call(this,c)).clusters=[],n.maxDistance=s,n.gridSize=u,n}return n(i,[{key:"cluster",value:function(t){var e=this,r=t.markers,n=t.map,o=t.mapCanvasProjection;return this.clusters=[],r.forEach((function(t){e.addToClosestCluster(t,n,o)})),this.clusters}},{key:"addToClosestCluster",value:function(t,e,r){for(var n=this.maxDistance,o=null,i=0;i<this.clusters.length;i++){var s=this.clusters[i],a=wi(s.bounds.getCenter().toJSON(),t.getPosition().toJSON());a<n&&(n=a,o=s)}if(o&&_i(o.bounds,r,this.gridSize).contains(t.getPosition()))o.push(t);else{var u=new yi({markers:[t]});this.clusters.push(u)}}}]),i}(Pi),Zi=function(t){o(i,t);var r=u(i);function i(t){e(this,i);var n=$o(t,[]);return r.call(this,n)}return n(i,[{key:"calculate",value:function(t){var e=t.markers,r=t.map,n=t.mapCanvasProjection;return{clusters:this.cluster({markers:e,map:r,mapCanvasProjection:n}),changed:!1}}},{key:"cluster",value:function(t){return this.noop(t)}}]),i}(Oi),Gi=6371008.8,Bi={centimeters:637100880,centimetres:637100880,degrees:57.22891354143274,feet:20902260.511392,inches:39.37*Gi,kilometers:6371.0088,kilometres:6371.0088,meters:Gi,metres:Gi,miles:3958.761333810546,millimeters:6371008800,millimetres:6371008800,nauticalmiles:Gi/1852,radians:1,yards:6967335.223679999};function Vi(t,e,r){if(void 0===r&&(r={}),!t)throw new Error("coordinates is required");if(!Array.isArray(t))throw new Error("coordinates must be an Array");if(t.length<2)throw new Error("coordinates must be at least 2 numbers long");if(!Hi(t[0])||!Hi(t[1]))throw new Error("coordinates must contain numbers");return function(t,e,r){void 0===r&&(r={});var n={type:"Feature"};return(0===r.id||r.id)&&(n.id=r.id),r.bbox&&(n.bbox=r.bbox),n.properties=e||{},n.geometry=t,n}({type:"Point",coordinates:t},e,r)}function Ui(t,e){void 0===e&&(e={});var r={type:"FeatureCollection"};return e.id&&(r.id=e.id),e.bbox&&(r.bbox=e.bbox),r.features=t,r}function Wi(t,e){void 0===e&&(e="kilometers");var r=Bi[e];if(!r)throw new Error(e+" units is invalid");return t*r}function Qi(t){return t%360*Math.PI/180}function Xi(t,e,r){if(void 0===e&&(e="kilometers"),void 0===r&&(r="kilometers"),!(t>=0))throw new Error("length must be a positive number");return Wi(function(t,e){void 0===e&&(e="kilometers");var r=Bi[e];if(!r)throw new Error(e+" units is invalid");return t/r}(t,e),r)}function Hi(t){return!isNaN(t)&&null!==t&&!Array.isArray(t)}function Ji(t){if(!t)throw new Error("geojson is required");switch(t.type){case"Feature":return Ki(t);case"FeatureCollection":return function(t){var e={type:"FeatureCollection"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"features":return;default:e[r]=t[r]}})),e.features=t.features.map((function(t){return Ki(t)})),e}(t);case"Point":case"LineString":case"Polygon":case"MultiPoint":case"MultiLineString":case"MultiPolygon":case"GeometryCollection":return Yi(t);default:throw new Error("unknown GeoJSON type")}}function Ki(t){var e={type:"Feature"};return Object.keys(t).forEach((function(r){switch(r){case"type":case"properties":case"geometry":return;default:e[r]=t[r]}})),e.properties=$i(t.properties),e.geometry=Yi(t.geometry),e}function $i(t){var e={};return t?(Object.keys(t).forEach((function(r){var n=t[r];"object"==typeof n?null===n?e[r]=null:Array.isArray(n)?e[r]=n.map((function(t){return t})):e[r]=$i(n):e[r]=n})),e):e}function Yi(t){var e={type:t.type};return t.bbox&&(e.bbox=t.bbox),"GeometryCollection"===t.type?(e.geometries=t.geometries.map((function(t){return Yi(t)})),e):(e.coordinates=ts(t.coordinates),e)}function ts(t){var e=t;return"object"!=typeof e[0]?e.slice():e.map((function(t){return ts(t)}))}function es(t,e,r){if(null!==t)for(var n,o,i,s,a,u,c,f,l=0,h=0,p=t.type,d="FeatureCollection"===p,m="Feature"===p,g=d?t.features.length:1,v=0;v<g;v++){a=(f=!!(c=d?t.features[v].geometry:m?t.geometry:t)&&"GeometryCollection"===c.type)?c.geometries.length:1;for(var y=0;y<a;y++){var b=0,_=0;if(null!==(s=f?c.geometries[y]:c)){u=s.coordinates;var w=s.type;switch(l=!r||"Polygon"!==w&&"MultiPolygon"!==w?0:1,w){case null:break;case"Point":if(!1===e(u,h,v,b,_))return!1;h++,b++;break;case"LineString":case"MultiPoint":for(n=0;n<u.length;n++){if(!1===e(u[n],h,v,b,_))return!1;h++,"MultiPoint"===w&&b++}"LineString"===w&&b++;break;case"Polygon":case"MultiLineString":for(n=0;n<u.length;n++){for(o=0;o<u[n].length-l;o++){if(!1===e(u[n][o],h,v,b,_))return!1;h++}"MultiLineString"===w&&b++,"Polygon"===w&&_++}"Polygon"===w&&b++;break;case"MultiPolygon":for(n=0;n<u.length;n++){for(_=0,o=0;o<u[n].length;o++){for(i=0;i<u[n][o].length-l;i++){if(!1===e(u[n][o][i],h,v,b,_))return!1;h++}_++}b++}break;case"GeometryCollection":for(n=0;n<s.geometries.length;n++)if(!1===es(s.geometries[n],e,r))return!1;break;default:throw new Error("Unknown Geometry Type")}}}}}function rs(t){var e=[];return es(t,(function(t){e.push(t)})),e}var ns={eudist:function(t,e,r){for(var n=t.length,o=0,i=0;i<n;i++){var s=(t[i]||0)-(e[i]||0);o+=s*s}return r?Math.sqrt(o):o},mandist:function(t,e,r){for(var n=t.length,o=0,i=0;i<n;i++)o+=Math.abs((t[i]||0)-(e[i]||0));return r?Math.sqrt(o):o},dist:function(t,e,r){var n=Math.abs(t-e);return r?n:n*n}},os=ns.eudist,is=ns.dist,ss={kmrand:function(t,e){for(var r={},n=[],o=e<<2,i=t.length,s=t[0].length>0;n.length<e&&o-- >0;){var a=t[Math.floor(Math.random()*i)],u=s?a.join("_"):""+a;r[u]||(r[u]=!0,n.push(a))}if(n.length<e)throw new Error("Error initializating clusters");return n},kmpp:function(t,e){var r=t[0].length?os:is,n=[],o=t.length,i=t[0].length>0,s=t[Math.floor(Math.random()*o)];i&&s.join("_");for(n.push(s);n.length<e;){for(var a=[],u=n.length,c=0,f=[],l=0;l<o;l++){for(var h=1/0,p=0;p<u;p++){var d=r(t[l],n[p]);d<=h&&(h=d)}a[l]=h}for(var m=0;m<o;m++)c+=a[m];for(var g=0;g<o;g++)f[g]={i:g,v:t[g],pr:a[g]/c,cs:0};f.sort((function(t,e){return t.pr-e.pr})),f[0].cs=f[0].pr;for(var v=1;v<o;v++)f[v].cs=f[v-1].cs+f[v].pr;for(var y=Math.random(),b=0;b<o-1&&f[b++].cs<y;);n.push(f[b-1].v)}return n}},as=ns.eudist,us=ss.kmrand,cs=ss.kmpp;function fs(t,e,r){r=r||[];for(var n=0;n<t;n++)r[n]=e;return r}var ls=function(t,e,r,n){var o=[],i=[],s=[],a=[],u=!1,c=n||1e4,f=t.length,l=t[0].length,h=l>0,p=[];if(r)o="kmrand"==r?us(t,e):"kmpp"==r?cs(t,e):r;else for(var d={};o.length<e;){var m=Math.floor(Math.random()*f);d[m]||(d[m]=!0,o.push(t[m]))}do{fs(e,0,p);for(var g=0;g<f;g++){for(var v=1/0,y=0,b=0;b<e;b++){(a=h?as(t[g],o[b]):Math.abs(t[g]-o[b]))<=v&&(v=a,y=b)}s[g]=y,p[y]++}for(var _=[],w=(i=[],0);w<e;w++)_[w]=h?fs(l,0,_[w]):0,i[w]=o[w];if(h){for(var k=0;k<e;k++)o[k]=[];for(var x=0;x<f;x++)for(var M=_[s[x]],O=t[x],P=0;P<l;P++)M[P]+=O[P];u=!0;for(var S=0;S<e;S++){for(var E=o[S],C=_[S],j=i[S],L=p[S],A=0;A<l;A++)E[A]=C[A]/L||0;if(u)for(var T=0;T<l;T++)if(j[T]!=E[T]){u=!1;break}}}else{for(var I=0;I<f;I++){_[s[I]]+=t[I]}for(var N=0;N<e;N++)o[N]=_[N]/p[N]||0;u=!0;for(var D=0;D<e;D++)if(i[D]!=o[D]){u=!1;break}}u=u||--c<=0}while(!u);return{it:1e4-c,k:e,idxs:s,centroids:o}};var hs=function(t){o(i,t);var r=u(i);function i(t){var n;e(this,i);var o=t.numberOfClusters,s=$o(t,["numberOfClusters"]);return(n=r.call(this,s)).numberOfClusters=o,n}return n(i,[{key:"cluster",value:function(t){var e=t.markers,r=t.map,n=[];return 0===e.length||function(t,e){void 0===e&&(e={});var r=t.features.length;e.numberOfClusters=e.numberOfClusters||Math.round(Math.sqrt(r/2)),e.numberOfClusters>r&&(e.numberOfClusters=r),!0!==e.mutate&&(t=Ji(t));var n=rs(t),o=n.slice(0,e.numberOfClusters),i=ls(n,e.numberOfClusters,o),s={};return i.centroids.forEach((function(t,e){s[e]=t})),function(t,e){if("Feature"===t.type)e(t,0);else if("FeatureCollection"===t.type)for(var r=0;r<t.features.length&&!1!==e(t.features[r],r);r++);}(t,(function(t,e){var r=i.idxs[e];t.properties.cluster=r,t.properties.centroid=s[r]})),t}(Ui(e.map((function(t){return Vi([t.getPosition().lng(),t.getPosition().lat()])}))),{numberOfClusters:this.numberOfClusters instanceof Function?this.numberOfClusters(e.length,r.getZoom()):this.numberOfClusters}).features.forEach((function(t,r){n[t.properties.cluster]||(n[t.properties.cluster]=new yi({position:{lng:t.properties.centroid[0],lat:t.properties.centroid[1]},markers:[]})),n[t.properties.cluster].push(e[r])})),n}}]),i}(Pi),ps=xn,ds=Mn,ms=Object.keys||function(t){return ps(t,ds)},gs=y,vs=N,ys=k,bs=v,_s=ms,ws=Sn,ks=x,xs=Gt,Ms=U,Os=Object.assign,Ps=Object.defineProperty,Ss=vs([].concat),Es=!Os||bs((function(){if(gs&&1!==Os({b:1},Os(Ps({},"a",{enumerable:!0,get:function(){Ps(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},r=Symbol(),n="abcdefghijklmnopqrst";return t[r]=7,n.split("").forEach((function(t){e[t]=t})),7!=Os({},t)[r]||_s(Os({},e)).join("")!=n}))?function(t,e){for(var r=xs(t),n=arguments.length,o=1,i=ws.f,s=ks.f;n>o;)for(var a,u=Ms(arguments[o++]),c=i?Ss(_s(u),i(u)):_s(u),f=c.length,l=0;f>l;)a=c[l++],gs&&!ys(s,u,a)||(r[a]=u[a]);return r}:Os;function Cs(t){if(!t)throw new Error("coord is required");if(!Array.isArray(t)){if("Feature"===t.type&&null!==t.geometry&&"Point"===t.geometry.type)return t.geometry.coordinates;if("Point"===t.type)return t.coordinates}if(Array.isArray(t)&&t.length>=2&&!Array.isArray(t[0])&&!Array.isArray(t[1]))return t;throw new Error("coord must be GeoJSON Point or an Array of numbers")}function js(t,e,r){void 0===r&&(r={});var n=Cs(t),o=Cs(e),i=Qi(o[1]-n[1]),s=Qi(o[0]-n[0]),a=Qi(n[1]),u=Qi(o[1]),c=Math.pow(Math.sin(i/2),2)+Math.pow(Math.sin(s/2),2)*Math.cos(a)*Math.cos(u);return Wi(2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c)),r.units)}to({target:"Object",stat:!0,forced:Object.assign!==Es},{assign:Es});var Ls={exports:{}},As={exports:{}};!function(t){function e(t,e,r,n){this.dataset=[],this.epsilon=1,this.minPts=2,this.distance=this._euclideanDistance,this.clusters=[],this.noise=[],this._visited=[],this._assigned=[],this._datasetLength=0,this._init(t,e,r,n)}e.prototype.run=function(t,e,r,n){this._init(t,e,r,n);for(var o=0;o<this._datasetLength;o++)if(1!==this._visited[o]){this._visited[o]=1;var i=this._regionQuery(o);if(i.length<this.minPts)this.noise.push(o);else{var s=this.clusters.length;this.clusters.push([]),this._addToCluster(o,s),this._expandCluster(s,i)}}return this.clusters},e.prototype._init=function(t,e,r,n){if(t){if(!(t instanceof Array))throw Error("Dataset must be of type array, "+typeof t+" given");this.dataset=t,this.clusters=[],this.noise=[],this._datasetLength=t.length,this._visited=new Array(this._datasetLength),this._assigned=new Array(this._datasetLength)}e&&(this.epsilon=e),r&&(this.minPts=r),n&&(this.distance=n)},e.prototype._expandCluster=function(t,e){for(var r=0;r<e.length;r++){var n=e[r];if(1!==this._visited[n]){this._visited[n]=1;var o=this._regionQuery(n);o.length>=this.minPts&&(e=this._mergeArrays(e,o))}1!==this._assigned[n]&&this._addToCluster(n,t)}},e.prototype._addToCluster=function(t,e){this.clusters[e].push(t),this._assigned[t]=1},e.prototype._regionQuery=function(t){for(var e=[],r=0;r<this._datasetLength;r++){this.distance(this.dataset[t],this.dataset[r])<this.epsilon&&e.push(r)}return e},e.prototype._mergeArrays=function(t,e){for(var r=e.length,n=0;n<r;n++){var o=e[n];t.indexOf(o)<0&&t.push(o)}return t},e.prototype._euclideanDistance=function(t,e){for(var r=0,n=Math.min(t.length,e.length);n--;)r+=(t[n]-e[n])*(t[n]-e[n]);return Math.sqrt(r)},t.exports&&(t.exports=e)}(As);var Ts={exports:{}};!function(t){function e(t,e,r){this.k=3,this.dataset=[],this.assignments=[],this.centroids=[],this.init(t,e,r)}e.prototype.init=function(t,e,r){this.assignments=[],this.centroids=[],void 0!==t&&(this.dataset=t),void 0!==e&&(this.k=e),void 0!==r&&(this.distance=r)},e.prototype.run=function(t,e){this.init(t,e);for(var r=this.dataset.length,n=0;n<this.k;n++)this.centroids[n]=this.randomCentroid();for(var o=!0;o;){o=this.assign();for(var i=0;i<this.k;i++){for(var s=new Array(f),a=0,u=0;u<f;u++)s[u]=0;for(var c=0;c<r;c++){var f=this.dataset[c].length;if(i===this.assignments[c]){for(u=0;u<f;u++)s[u]+=this.dataset[c][u];a++}}if(a>0){for(u=0;u<f;u++)s[u]/=a;this.centroids[i]=s}else this.centroids[i]=this.randomCentroid(),o=!0}}return this.getClusters()},e.prototype.randomCentroid=function(){var t,e,r=this.dataset.length-1;do{e=Math.round(Math.random()*r),t=this.dataset[e]}while(this.centroids.indexOf(t)>=0);return t},e.prototype.assign=function(){for(var t,e=!1,r=this.dataset.length,n=0;n<r;n++)(t=this.argmin(this.dataset[n],this.centroids,this.distance))!=this.assignments[n]&&(this.assignments[n]=t,e=!0);return e},e.prototype.getClusters=function(){for(var t,e=new Array(this.k),r=0;r<this.assignments.length;r++)void 0===e[t=this.assignments[r]]&&(e[t]=[]),e[t].push(r);return e},e.prototype.argmin=function(t,e,r){for(var n,o=Number.MAX_VALUE,i=0,s=e.length,a=0;a<s;a++)(n=r(t,e[a]))<o&&(o=n,i=a);return i},e.prototype.distance=function(t,e){for(var r=0,n=Math.min(t.length,e.length);n--;){var o=t[n]-e[n];r+=o*o}return Math.sqrt(r)},t.exports&&(t.exports=e)}(Ts);var Is,Ns={exports:{}},Ds={exports:{}};!function(t){function e(t,e,r){this._queue=[],this._priorities=[],this._sorting="desc",this._init(t,e,r)}e.prototype.insert=function(t,e){for(var r=this._queue.length,n=r;n--;){var o=this._priorities[n];"desc"===this._sorting?e>o&&(r=n):e<o&&(r=n)}this._insertAt(t,e,r)},e.prototype.remove=function(t){for(var e=this._queue.length;e--;){if(t===this._queue[e]){this._queue.splice(e,1),this._priorities.splice(e,1);break}}},e.prototype.forEach=function(t){this._queue.forEach(t)},e.prototype.getElements=function(){return this._queue},e.prototype.getElementPriority=function(t){return this._priorities[t]},e.prototype.getPriorities=function(){return this._priorities},e.prototype.getElementsWithPriorities=function(){for(var t=[],e=0,r=this._queue.length;e<r;e++)t.push([this._queue[e],this._priorities[e]]);return t},e.prototype._init=function(t,e,r){if(t&&e){if(this._queue=[],this._priorities=[],t.length!==e.length)throw new Error("Arrays must have the same length");for(var n=0;n<t.length;n++)this.insert(t[n],e[n])}r&&(this._sorting=r)},e.prototype._insertAt=function(t,e,r){this._queue.length===r?(this._queue.push(t),this._priorities.push(e)):(this._queue.splice(r,0,t),this._priorities.splice(r,0,e))},t.exports&&(t.exports=e)}(Ds),function(t){if(t.exports)var e=Ds.exports;function r(t,e,r,n){this.epsilon=1,this.minPts=1,this.distance=this._euclideanDistance,this._reachability=[],this._processed=[],this._coreDistance=0,this._orderedList=[],this._init(t,e,r,n)}r.prototype.run=function(t,r,n,o){this._init(t,r,n,o);for(var i=0,s=this.dataset.length;i<s;i++)if(1!==this._processed[i]){this._processed[i]=1,this.clusters.push([i]);var a=this.clusters.length-1;this._orderedList.push(i);var u=new e(null,null,"asc"),c=this._regionQuery(i);void 0!==this._distanceToCore(i)&&(this._updateQueue(i,c,u),this._expandCluster(a,u))}return this.clusters},r.prototype.getReachabilityPlot=function(){for(var t=[],e=0,r=this._orderedList.length;e<r;e++){var n=this._orderedList[e],o=this._reachability[n];t.push([n,o])}return t},r.prototype._init=function(t,e,r,n){if(t){if(!(t instanceof Array))throw Error("Dataset must be of type array, "+typeof t+" given");this.dataset=t,this.clusters=[],this._reachability=new Array(this.dataset.length),this._processed=new Array(this.dataset.length),this._coreDistance=0,this._orderedList=[]}e&&(this.epsilon=e),r&&(this.minPts=r),n&&(this.distance=n)},r.prototype._updateQueue=function(t,e,r){var n=this;this._coreDistance=this._distanceToCore(t),e.forEach((function(e){if(void 0===n._processed[e]){var o=n.distance(n.dataset[t],n.dataset[e]),i=Math.max(n._coreDistance,o);void 0===n._reachability[e]?(n._reachability[e]=i,r.insert(e,i)):i<n._reachability[e]&&(n._reachability[e]=i,r.remove(e),r.insert(e,i))}}))},r.prototype._expandCluster=function(t,e){for(var r=e.getElements(),n=0,o=r.length;n<o;n++){var i=r[n];if(void 0===this._processed[i]){var s=this._regionQuery(i);this._processed[i]=1,this.clusters[t].push(i),this._orderedList.push(i),void 0!==this._distanceToCore(i)&&(this._updateQueue(i,s,e),this._expandCluster(t,e))}}},r.prototype._distanceToCore=function(t){for(var e=this.epsilon,r=0;r<e;r++){if(this._regionQuery(t,r).length>=this.minPts)return r}},r.prototype._regionQuery=function(t,e){e=e||this.epsilon;for(var r=[],n=0,o=this.dataset.length;n<o;n++)this.distance(this.dataset[t],this.dataset[n])<e&&r.push(n);return r},r.prototype._euclideanDistance=function(t,e){for(var r=0,n=Math.min(t.length,e.length);n--;)r+=(t[n]-e[n])*(t[n]-e[n]);return Math.sqrt(r)},t.exports&&(t.exports=r)}(Ns),(Is=Ls).exports&&(Is.exports={DBSCAN:As.exports,KMEANS:Ts.exports,OPTICS:Ns.exports,PriorityQueue:Ds.exports});var Fs=Ls.exports;var zs={units:"kilometers",mutate:!1,minPoints:1},Rs=function(t){o(i,t);var r=u(i);function i(t){var n;e(this,i);var o=t.maxDistance,s=void 0===o?200:o,a=t.minPoints,u=void 0===a?zs.minPoints:a,c=$o(t,["maxDistance","minPoints"]);return(n=r.call(this,c)).maxDistance=s,n.options=Object.assign(Object.assign({},zs),{minPoints:u}),n}return n(i,[{key:"cluster",value:function(t){var e=t.markers,r=t.mapCanvasProjection,n=Ui(e.map((function(t){var e=r.fromLatLngToContainerPixel(t.getPosition());return Vi([e.x,e.y])}))),o=[];return function(t,e,r){void 0===r&&(r={}),!0!==r.mutate&&(t=Ji(t)),r.minPoints=r.minPoints||3;var n=new Fs.DBSCAN,o=n.run(rs(t),Xi(e,r.units),r.minPoints,js),i=-1;return o.forEach((function(e){i++,e.forEach((function(e){var r=t.features[e];r.properties||(r.properties={}),r.properties.cluster=i,r.properties.dbscan="core"}))})),n.noise.forEach((function(e){var r=t.features[e];r.properties||(r.properties={}),r.properties.cluster?r.properties.dbscan="edge":r.properties.dbscan="noise"})),t}(n,this.maxDistance,this.options).features.forEach((function(t,r){o[t.properties.cluster]||(o[t.properties.cluster]=[]),o[t.properties.cluster].push(e[r])})),o.map((function(t){return new yi({markers:t})}))}}]),i}(Pi),qs={exports:{}};!function(t,e){t.exports=function(){function t(r,n,o,i,s,a){if(!(s-i<=o)){var u=i+s>>1;e(r,n,u,i,s,a%2),t(r,n,o,i,u-1,a+1),t(r,n,o,u+1,s,a+1)}}function e(t,n,o,i,s,a){for(;s>i;){if(s-i>600){var u=s-i+1,c=o-i+1,f=Math.log(u),l=.5*Math.exp(2*f/3),h=.5*Math.sqrt(f*l*(u-l)/u)*(c-u/2<0?-1:1);e(t,n,o,Math.max(i,Math.floor(o-c*l/u+h)),Math.min(s,Math.floor(o+(u-c)*l/u+h)),a)}var p=n[2*o+a],d=i,m=s;for(r(t,n,i,o),n[2*s+a]>p&&r(t,n,i,s);d<m;){for(r(t,n,d,m),d++,m--;n[2*d+a]<p;)d++;for(;n[2*m+a]>p;)m--}n[2*i+a]===p?r(t,n,i,m):r(t,n,++m,s),m<=o&&(i=m+1),o<=m&&(s=m-1)}}function r(t,e,r,o){n(t,r,o),n(e,2*r,2*o),n(e,2*r+1,2*o+1)}function n(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function o(t,e,r,n,o,i,s){for(var a,u,c=[0,t.length-1,0],f=[];c.length;){var l=c.pop(),h=c.pop(),p=c.pop();if(h-p<=s)for(var d=p;d<=h;d++)a=e[2*d],u=e[2*d+1],a>=r&&a<=o&&u>=n&&u<=i&&f.push(t[d]);else{var m=Math.floor((p+h)/2);a=e[2*m],u=e[2*m+1],a>=r&&a<=o&&u>=n&&u<=i&&f.push(t[m]);var g=(l+1)%2;(0===l?r<=a:n<=u)&&(c.push(p),c.push(m-1),c.push(g)),(0===l?o>=a:i>=u)&&(c.push(m+1),c.push(h),c.push(g))}}return f}function i(t,e,r,n,o,i){for(var a=[0,t.length-1,0],u=[],c=o*o;a.length;){var f=a.pop(),l=a.pop(),h=a.pop();if(l-h<=i)for(var p=h;p<=l;p++)s(e[2*p],e[2*p+1],r,n)<=c&&u.push(t[p]);else{var d=Math.floor((h+l)/2),m=e[2*d],g=e[2*d+1];s(m,g,r,n)<=c&&u.push(t[d]);var v=(f+1)%2;(0===f?r-o<=m:n-o<=g)&&(a.push(h),a.push(d-1),a.push(v)),(0===f?r+o>=m:n+o>=g)&&(a.push(d+1),a.push(l),a.push(v))}}return u}function s(t,e,r,n){var o=t-r,i=e-n;return o*o+i*i}var a=function(t){return t[0]},u=function(t){return t[1]},c=function(e,r,n,o,i){void 0===r&&(r=a),void 0===n&&(n=u),void 0===o&&(o=64),void 0===i&&(i=Float64Array),this.nodeSize=o,this.points=e;for(var s=e.length<65536?Uint16Array:Uint32Array,c=this.ids=new s(e.length),f=this.coords=new i(2*e.length),l=0;l<e.length;l++)c[l]=l,f[2*l]=r(e[l]),f[2*l+1]=n(e[l]);t(c,f,o,0,c.length-1,0)};return c.prototype.range=function(t,e,r,n){return o(this.ids,this.coords,t,e,r,n,this.nodeSize)},c.prototype.within=function(t,e,r){return i(this.ids,this.coords,t,e,r,this.nodeSize)},c}()}(qs);var Zs=qs.exports;const Gs={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:t=>t},Bs=Math.fround||(Vs=new Float32Array(1),t=>(Vs[0]=+t,Vs[0]));var Vs;class Us{constructor(t){this.options=Ys(Object.create(Gs),t),this.trees=new Array(this.options.maxZoom+1)}load(t){const{log:e,minZoom:r,maxZoom:n,nodeSize:o}=this.options;e&&console.time("total time");const i=`prepare ${t.length} points`;e&&console.time(i),this.points=t;let s=[];for(let e=0;e<t.length;e++)t[e].geometry&&s.push(Qs(t[e],e));this.trees[n+1]=new Zs(s,ta,ea,o,Float32Array),e&&console.timeEnd(i);for(let t=n;t>=r;t--){const r=+Date.now();s=this._cluster(s,t),this.trees[t]=new Zs(s,ta,ea,o,Float32Array),e&&console.log("z%d: %d clusters in %dms",t,s.length,+Date.now()-r)}return e&&console.timeEnd("total time"),this}getClusters(t,e){let r=((t[0]+180)%360+360)%360-180;const n=Math.max(-90,Math.min(90,t[1]));let o=180===t[2]?180:((t[2]+180)%360+360)%360-180;const i=Math.max(-90,Math.min(90,t[3]));if(t[2]-t[0]>=360)r=-180,o=180;else if(r>o){const t=this.getClusters([r,n,180,i],e),s=this.getClusters([-180,n,o,i],e);return t.concat(s)}const s=this.trees[this._limitZoom(e)],a=s.range(Js(r),Ks(i),Js(o),Ks(n)),u=[];for(const t of a){const e=s.points[t];u.push(e.numPoints?Xs(e):this.points[e.index])}return u}getChildren(t){const e=this._getOriginId(t),r=this._getOriginZoom(t),n="No cluster with the specified id.",o=this.trees[r];if(!o)throw new Error(n);const i=o.points[e];if(!i)throw new Error(n);const s=this.options.radius/(this.options.extent*Math.pow(2,r-1)),a=o.within(i.x,i.y,s),u=[];for(const e of a){const r=o.points[e];r.parentId===t&&u.push(r.numPoints?Xs(r):this.points[r.index])}if(0===u.length)throw new Error(n);return u}getLeaves(t,e,r){e=e||10,r=r||0;const n=[];return this._appendLeaves(n,t,e,r,0),n}getTile(t,e,r){const n=this.trees[this._limitZoom(t)],o=Math.pow(2,t),{extent:i,radius:s}=this.options,a=s/i,u=(r-a)/o,c=(r+1+a)/o,f={features:[]};return this._addTileFeatures(n.range((e-a)/o,u,(e+1+a)/o,c),n.points,e,r,o,f),0===e&&this._addTileFeatures(n.range(1-a/o,u,1,c),n.points,o,r,o,f),e===o-1&&this._addTileFeatures(n.range(0,u,a/o,c),n.points,-1,r,o,f),f.features.length?f:null}getClusterExpansionZoom(t){let e=this._getOriginZoom(t)-1;for(;e<=this.options.maxZoom;){const r=this.getChildren(t);if(e++,1!==r.length)break;t=r[0].properties.cluster_id}return e}_appendLeaves(t,e,r,n,o){const i=this.getChildren(e);for(const e of i){const i=e.properties;if(i&&i.cluster?o+i.point_count<=n?o+=i.point_count:o=this._appendLeaves(t,i.cluster_id,r,n,o):o<n?o++:t.push(e),t.length===r)break}return o}_addTileFeatures(t,e,r,n,o,i){for(const s of t){const t=e[s],a=t.numPoints;let u,c,f;if(a)u=Hs(t),c=t.x,f=t.y;else{const e=this.points[t.index];u=e.properties,c=Js(e.geometry.coordinates[0]),f=Ks(e.geometry.coordinates[1])}const l={type:1,geometry:[[Math.round(this.options.extent*(c*o-r)),Math.round(this.options.extent*(f*o-n))]],tags:u};let h;a?h=t.id:this.options.generateId?h=t.index:this.points[t.index].id&&(h=this.points[t.index].id),void 0!==h&&(l.id=h),i.features.push(l)}}_limitZoom(t){return Math.max(this.options.minZoom,Math.min(+t,this.options.maxZoom+1))}_cluster(t,e){const r=[],{radius:n,extent:o,reduce:i,minPoints:s}=this.options,a=n/(o*Math.pow(2,e));for(let n=0;n<t.length;n++){const o=t[n];if(o.zoom<=e)continue;o.zoom=e;const u=this.trees[e+1],c=u.within(o.x,o.y,a),f=o.numPoints||1;let l=f;for(const t of c){const r=u.points[t];r.zoom>e&&(l+=r.numPoints||1)}if(l>f&&l>=s){let t=o.x*f,s=o.y*f,a=i&&f>1?this._map(o,!0):null;const h=(n<<5)+(e+1)+this.points.length;for(const r of c){const n=u.points[r];if(n.zoom<=e)continue;n.zoom=e;const c=n.numPoints||1;t+=n.x*c,s+=n.y*c,n.parentId=h,i&&(a||(a=this._map(o,!0)),i(a,this._map(n)))}o.parentId=h,r.push(Ws(t/l,s/l,h,l,a))}else if(r.push(o),l>1)for(const t of c){const n=u.points[t];n.zoom<=e||(n.zoom=e,r.push(n))}}return r}_getOriginId(t){return t-this.points.length>>5}_getOriginZoom(t){return(t-this.points.length)%32}_map(t,e){if(t.numPoints)return e?Ys({},t.properties):t.properties;const r=this.points[t.index].properties,n=this.options.map(r);return e&&n===r?Ys({},n):n}}function Ws(t,e,r,n,o){return{x:Bs(t),y:Bs(e),zoom:1/0,id:r,parentId:-1,numPoints:n,properties:o}}function Qs(t,e){const[r,n]=t.geometry.coordinates;return{x:Bs(Js(r)),y:Bs(Ks(n)),zoom:1/0,index:e,parentId:-1}}function Xs(t){return{type:"Feature",id:t.id,properties:Hs(t),geometry:{type:"Point",coordinates:[(e=t.x,360*(e-.5)),$s(t.y)]}};var e}function Hs(t){const e=t.numPoints,r=e>=1e4?Math.round(e/1e3)+"k":e>=1e3?Math.round(e/100)/10+"k":e;return Ys(Ys({},t.properties),{cluster:!0,cluster_id:t.id,point_count:e,point_count_abbreviated:r})}function Js(t){return t/360+.5}function Ks(t){const e=Math.sin(t*Math.PI/180),r=.5-.25*Math.log((1+e)/(1-e))/Math.PI;return r<0?0:r>1?1:r}function $s(t){const e=(180-360*t)*Math.PI/180;return 360*Math.atan(Math.exp(e))/Math.PI-90}function Ys(t,e){for(const r in e)t[r]=e[r];return t}function ta(t){return t.x}function ea(t){return t.y}var ra=function t(e,r){if(e===r)return!0;if(e&&r&&"object"==typeof e&&"object"==typeof r){if(e.constructor!==r.constructor)return!1;var n,o,i;if(Array.isArray(e)){if((n=e.length)!=r.length)return!1;for(o=n;0!=o--;)if(!t(e[o],r[o]))return!1;return!0}if(e instanceof Map&&r instanceof Map){if(e.size!==r.size)return!1;for(o of e.entries())if(!r.has(o[0]))return!1;for(o of e.entries())if(!t(o[1],r.get(o[0])))return!1;return!0}if(e instanceof Set&&r instanceof Set){if(e.size!==r.size)return!1;for(o of e.entries())if(!r.has(o[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(r)){if((n=e.length)!=r.length)return!1;for(o=n;0!=o--;)if(e[o]!==r[o])return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if((n=(i=Object.keys(e)).length)!==Object.keys(r).length)return!1;for(o=n;0!=o--;)if(!Object.prototype.hasOwnProperty.call(r,i[o]))return!1;for(o=n;0!=o--;){var s=i[o];if(!t(e[s],r[s]))return!1}return!0}return e!=e&&r!=r},na=function(t){o(i,t);var r=u(i);function i(t){var n;e(this,i);var o=t.maxZoom,s=t.radius,a=void 0===s?60:s,u=$o(t,["maxZoom","radius"]);return(n=r.call(this,{maxZoom:o})).superCluster=new Us(Object.assign({maxZoom:n.maxZoom,radius:a},u)),n.state={zoom:null},n}return n(i,[{key:"calculate",value:function(t){var e=!1;if(!ra(t.markers,this.markers)){e=!0,this.markers=f(t.markers);var r=this.markers.map((function(t){return{type:"Feature",geometry:{type:"Point",coordinates:[t.getPosition().lng(),t.getPosition().lat()]},properties:{marker:t}}}));this.superCluster.load(r)}var n={zoom:t.map.getZoom()};return e||this.state.zoom>this.maxZoom&&n.zoom>this.maxZoom||(e=e||!ra(this.state,n)),this.state=n,e&&(this.clusters=this.cluster(t)),{clusters:this.clusters,changed:e}}},{key:"cluster",value:function(t){var e=t.map;return this.superCluster.getClusters([-180,-90,180,90],Math.round(e.getZoom())).map(this.transformCluster.bind(this))}},{key:"transformCluster",value:function(t){var e=c(t.geometry.coordinates,2),r=e[0],n=e[1],o=t.properties;if(o.cluster)return new yi({markers:this.superCluster.getLeaves(o.cluster_id,1/0).map((function(t){return t.properties.marker})),position:new google.maps.LatLng({lat:n,lng:r})});var i=o.marker;return new yi({markers:[i],position:i.getPosition()})}}]),i}(Oi),oa={},ia=y,sa=De,aa=Ne,ua=Ze,ca=J,fa=ms;oa.f=ia&&!sa?Object.defineProperties:function(t,e){ua(t);for(var r,n=ca(e),o=fa(e),i=o.length,s=0;i>s;)aa.f(t,r=o[s++],n[r]);return t};var la,ha=nt("document","documentElement"),pa=Ze,da=oa,ma=Mn,ga=yr,va=ha,ya=xe,ba=vr("IE_PROTO"),_a=function(){},wa=function(t){return"<script>"+t+"</"+"script>"},ka=function(t){t.write(wa("")),t.close();var e=t.parentWindow.Object;return t=null,e},xa=function(){try{la=new ActiveXObject("htmlfile")}catch(t){}var t,e;xa="undefined"!=typeof document?document.domain&&la?ka(la):((e=ya("iframe")).style.display="none",va.appendChild(e),e.src=String("javascript:"),(t=e.contentWindow.document).open(),t.write(wa("document.F=Object")),t.close(),t.F):ka(la);for(var r=ma.length;r--;)delete xa.prototype[ma[r]];return xa()};ga[ba]=!0;var Ma=Object.create||function(t,e){var r;return null!==t?(_a.prototype=pa(t),r=new _a,_a.prototype=null,r[ba]=t):r=xa(),void 0===e?r:da.f(r,e)},Oa=Ne,Pa=ae("unscopables"),Sa=Array.prototype;null==Sa[Pa]&&Oa.f(Sa,Pa,{configurable:!0,value:Ma(null)});var Ea=vn.includes,Ca=function(t){Sa[Pa][t]=!0};to({target:"Array",proto:!0},{includes:function(t){return Ea(this,t,arguments.length>1?arguments[1]:void 0)}}),Ca("includes");var ja=Y,La=R,Aa=ae("match"),Ta=function(t){var e;return ja(t)&&(void 0!==(e=t[Aa])?!!e:"RegExp"==La(t))},Ia=m.TypeError,Na=go,Da=m.String,Fa=function(t){if("Symbol"===Na(t))throw TypeError("Cannot convert a Symbol value to a string");return Da(t)},za=ae("match"),Ra=to,qa=function(t){if(Ta(t))throw Ia("The method doesn't accept regular expressions");return t},Za=Q,Ga=Fa,Ba=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[za]=!1,"/./"[t](e)}catch(t){}}return!1},Va=N("".indexOf);Ra({target:"String",proto:!0,forced:!Ba("includes")},{includes:function(t){return!!~Va(Ga(Za(this)),Ga(qa(t)),arguments.length>1?arguments[1]:void 0)}});var Ua=to,Wa=vn.indexOf,Qa=ai,Xa=N([].indexOf),Ha=!!Xa&&1/Xa([1],1,-0)<0,Ja=Qa("indexOf");Ua({target:"Array",proto:!0,forced:Ha||!Ja},{indexOf:function(t){var e=arguments.length>1?arguments[1]:void 0;return Ha?Xa(this,t,e)||0:Wa(this,t,e)}});var Ka=be,$a=Ne,Ya=C,tu=to,eu=m,ru=un,nu=nn,ou=hn,iu=Gt,su=zo,au=function(t,e,r){var n=Ka(e);n in t?$a.f(t,n,Ya(0,r)):t[n]=r},uu=Jo("splice"),cu=eu.TypeError,fu=Math.max,lu=Math.min,hu=9007199254740991,pu="Maximum allowed length exceeded";tu({target:"Array",proto:!0,forced:!uu},{splice:function(t,e){var r,n,o,i,s,a,u=iu(this),c=ou(u),f=ru(t,c),l=arguments.length;if(0===l?r=n=0:1===l?(r=0,n=c-f):(r=l-2,n=lu(fu(nu(e),0),c-f)),c+r-n>hu)throw cu(pu);for(o=su(u,n),i=0;i<n;i++)(s=f+i)in u&&au(o,i,u[s]);if(o.length=n,r<n){for(i=f;i<c-n;i++)a=i+r,(s=i+n)in u?u[a]=u[s]:delete u[a];for(i=c;i>c-n+r;i--)delete u[i-1]}else if(r>n)for(i=c-n;i>f;i--)a=i+r-1,(s=i+n-1)in u?u[a]=u[s]:delete u[a];for(i=0;i<r;i++)u[i+f]=arguments[i+2];return u.length=c-n+r,o}});var du=m,mu=K,gu=du.String,vu=du.TypeError,yu=N,bu=Ze,_u=function(t){if("object"==typeof t||mu(t))return t;throw vu("Can't set "+gu(t)+" as a prototype")},wu=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=yu(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return bu(r),_u(n),e?t(r,n):r.__proto__=n,r}}():void 0),ku=K,xu=Y,Mu=wu,Ou=N(1..valueOf),Pu=Q,Su=Fa,Eu=N("".replace),Cu="[\t\n\v\f\r                　\u2028\u2029\ufeff]",ju=RegExp("^"+Cu+Cu+"*"),Lu=RegExp(Cu+Cu+"*$"),Au=function(t){return function(e){var r=Su(Pu(e));return 1&t&&(r=Eu(r,ju,"")),2&t&&(r=Eu(r,Lu,"")),r}},Tu={start:Au(1),end:Au(2),trim:Au(3)},Iu=y,Nu=m,Du=N,Fu=Wn,zu=rr.exports,Ru=Ut,qu=function(t,e,r){var n,o;return Mu&&ku(n=e.constructor)&&n!==r&&xu(o=n.prototype)&&o!==r.prototype&&Mu(t,o),t},Zu=ot,Gu=wt,Bu=ge,Vu=v,Uu=tn.f,Wu=g.f,Qu=Ne.f,Xu=Ou,Hu=Tu.trim,Ju="Number",Ku=Nu.Number,$u=Ku.prototype,Yu=Nu.TypeError,tc=Du("".slice),ec=Du("".charCodeAt),rc=function(t){var e=Bu(t,"number");return"bigint"==typeof e?e:nc(e)},nc=function(t){var e,r,n,o,i,s,a,u,c=Bu(t,"number");if(Gu(c))throw Yu("Cannot convert a Symbol value to a number");if("string"==typeof c&&c.length>2)if(c=Hu(c),43===(e=ec(c,0))||45===e){if(88===(r=ec(c,2))||120===r)return NaN}else if(48===e){switch(ec(c,1)){case 66:case 98:n=2,o=49;break;case 79:case 111:n=8,o=55;break;default:return+c}for(s=(i=tc(c,2)).length,a=0;a<s;a++)if((u=ec(i,a))<48||u>o)return NaN;return parseInt(i,n)}return+c};if(Fu(Ju,!Ku(" 0o1")||!Ku("0b1")||Ku("+0x1"))){for(var oc,ic=function(t){var e=arguments.length<1?0:Ku(rc(t)),r=this;return Zu($u,r)&&Vu((function(){Xu(r)}))?qu(Object(e),r,ic):e},sc=Iu?Uu(Ku):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,isFinite,isInteger,isNaN,isSafeInteger,parseFloat,parseInt,fromString,range".split(","),ac=0;sc.length>ac;ac++)Ru(Ku,oc=sc[ac])&&!Ru(ic,oc)&&Qu(ic,oc,Wu(Ku,oc));ic.prototype=$u,$u.constructor=ic,zu(Nu,Ju,ic)}var uc=n((function t(r,n){e(this,t),this.markers={sum:r.length};var o=n.map((function(t){return t.count})),i=o.reduce((function(t,e){return t+e}),0);this.clusters={count:n.length,markers:{mean:i/n.length,sum:i,min:Math.min.apply(Math,f(o)),max:Math.max.apply(Math,f(o))}}})),cc=function(){function t(){e(this,t)}return n(t,[{key:"render",value:function(t,e){var r=t.count,n=t.position,o=r>Math.max(10,e.clusters.markers.mean)?"#ff0000":"#0000ff",i=window.btoa('\n  <svg fill="'.concat(o,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240">\n    <circle cx="120" cy="120" opacity=".6" r="70" />\n    <circle cx="120" cy="120" opacity=".3" r="90" />\n    <circle cx="120" cy="120" opacity=".2" r="110" />\n  </svg>'));return new google.maps.Marker({position:n,icon:{url:"data:image/svg+xml;base64,".concat(i),scaledSize:new google.maps.Size(45,45)},label:{text:String(r),color:"rgba(255,255,255,0.9)",fontSize:"12px"},title:"Cluster of ".concat(r," markers"),zIndex:Number(google.maps.Marker.MAX_ZINDEX)+r})}}]),t}();var fc,lc=n((function t(){e(this,t),function(t,e){for(var r in e.prototype)t.prototype[r]=e.prototype[r]}(t,google.maps.OverlayView)}));t.MarkerClustererEvents=void 0,(fc=t.MarkerClustererEvents||(t.MarkerClustererEvents={})).CLUSTERING_BEGIN="clusteringbegin",fc.CLUSTERING_END="clusteringend",fc.CLUSTER_CLICK="click";var hc=function(t,e,r){r.fitBounds(e.bounds)},pc=function(r){o(s,r);var i=u(s);function s(t){var r,n=t.map,o=t.markers,a=void 0===o?[]:o,u=t.algorithm,c=void 0===u?new na({}):u,l=t.renderer,h=void 0===l?new cc:l,p=t.onClusterClick,d=void 0===p?hc:p;return e(this,s),(r=i.call(this)).markers=f(a),r.clusters=[],r.algorithm=c,r.renderer=h,r.onClusterClick=d,n&&r.setMap(n),r}return n(s,[{key:"addMarker",value:function(t,e){this.markers.includes(t)||(this.markers.push(t),e||this.render())}},{key:"addMarkers",value:function(t,e){var r=this;t.forEach((function(t){r.addMarker(t,!0)})),e||this.render()}},{key:"removeMarker",value:function(t,e){var r=this.markers.indexOf(t);return-1!==r&&(t.setMap(null),this.markers.splice(r,1),e||this.render(),!0)}},{key:"removeMarkers",value:function(t,e){var r=this,n=!1;return t.forEach((function(t){n=r.removeMarker(t,!0)||n})),n&&!e&&this.render(),n}},{key:"clearMarkers",value:function(t){this.markers.length=0,t||this.render()}},{key:"render",value:function(){var e=this.getMap();if(e instanceof google.maps.Map&&this.getProjection()){google.maps.event.trigger(this,t.MarkerClustererEvents.CLUSTERING_BEGIN,this);var r=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()}),n=r.clusters,o=r.changed;(o||null==o)&&(this.reset(),this.clusters=n,this.renderClusters()),google.maps.event.trigger(this,t.MarkerClustererEvents.CLUSTERING_END,this)}}},{key:"onAdd",value:function(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}},{key:"onRemove",value:function(){google.maps.event.removeListener(this.idleListener),this.reset()}},{key:"reset",value:function(){this.markers.forEach((function(t){return t.setMap(null)})),this.clusters.forEach((function(t){return t.delete()})),this.clusters=[]}},{key:"renderClusters",value:function(){var e=this,r=new uc(this.markers,this.clusters),n=this.getMap();this.clusters.forEach((function(o){1===o.markers.length?o.marker=o.markers[0]:(o.marker=e.renderer.render(o,r),e.onClusterClick&&o.marker.addListener("click",(function(r){google.maps.event.trigger(e,t.MarkerClustererEvents.CLUSTER_CLICK,o),e.onClusterClick(r,o,n)}))),o.marker.setMap(n)}))}}]),s}(lc);return t.AbstractAlgorithm=Oi,t.AbstractViewportAlgorithm=Pi,t.Cluster=yi,t.ClusterStats=uc,t.DBScanAlgorithm=Rs,t.DefaultRenderer=cc,t.GridAlgorithm=qi,t.KmeansAlgorithm=hs,t.MarkerClusterer=pc,t.NoopAlgorithm=Zi,t.SuperClusterAlgorithm=na,t.defaultOnClusterClickHandler=hc,t.distanceBetweenPoints=wi,t.extendBoundsToPaddedViewport=_i,t.extendPixelBounds=xi,t.filterMarkersToPaddedViewport=bi,t.noop=Si,t.pixelBoundsToLatLngBounds=Mi,Object.defineProperty(t,"__esModule",{value:!0}),t}({});


/**
 * [MarkerClusterer Override the marker cluster]
 * @param {[type]} _map     [description]
 * @param {[type]} _markers [description]
 * @param {[type]} _config  [description]
 */
var MarkerClusterer = function (_map, _markers, _config) {

  var interpolatedRenderer = {
    render: function render(_ref, stats) {
      var count = _ref.count,
          position = _ref.position;
      // change color if this cluster has more markers than the mean cluster
      var color = count > Math.max(10, stats.clusters.markers.mean) ? (asl_configuration.cluster_m1 || "#00a2d3") : (asl_configuration.cluster_m2 || "#ff9b00"); //const color = this.palette(count / stats.clusters.markers.max);
      
      var svg = window.btoa("<svg fill=\"".concat(color, "\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\n                          <circle cx=\"120\" cy=\"120\" opacity=\".6\" r=\"70\" />\n                          <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\n                          <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\n                        </svg>\n                      "));

      return new google.maps.Marker({
        position: position,
        icon: {
          url: "data:image/svg+xml;base64,".concat(svg),
          scaledSize: new google.maps.Size(60, 60)
        },
        label: {
          text: String(count),
          color: asl_configuration.cluster_label || "#FFFFFF",
          fontSize: "12px"
        },
        // adjust zIndex to be above other markers
        zIndex: Number(google.maps.Marker.MAX_ZINDEX) + count
      });
    }
  };

  //, algorithm: new markerClusterer.SuperClusterAlgorithm(_config)
  return new markerClusterer.MarkerClusterer({map: _map, markers: _markers, renderer: interpolatedRenderer});
};
MarkerClusterer.prototype = markerClusterer.MarkerClusterer.prototype;
MarkerClusterer.prototype.constructor = MarkerClusterer;
