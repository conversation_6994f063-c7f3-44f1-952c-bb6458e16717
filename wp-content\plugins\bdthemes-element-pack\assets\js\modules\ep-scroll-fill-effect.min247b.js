!function(e,n){"use strict";e(window).on("elementor/frontend/init",(function(e){var n,t=elementorModules.frontend.handlers.Base;n=t.extend({bindEvents:function(){this.run()},onElementChange:debounce((function(e){-1!==e.indexOf("ep_widget_sf_fx_")&&this.run()}),400),settings:function(e){return this.getElementSettings("ep_widget_sf_fx_"+e)},run:function(){var e=this.$element;"yes"===this.settings("enable")&&epObserveTarget(e[0],(function(){var n=jQuery(e).find(".elementor-heading-title, .bdt-heading-tag span, .bdt-ep-advanced-heading-main-title-inner");gsap.to(n,{scrollTrigger:{trigger:n,start:"bottom center+=50%",end:"bottom center",scrub:!0},backgroundSize:"100% 200%"})}),{root:null,rootMargin:"0px",threshold:.8})}}),elementorFrontend.hooks.addAction("frontend/element_ready/heading.default",(function(e){elementorFrontend.elementsHandler.addHandler(n,{$element:e})})),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-animated-heading.default",(function(e){elementorFrontend.elementsHandler.addHandler(n,{$element:e})})),elementorFrontend.hooks.addAction("frontend/element_ready/bdt-advanced-heading.default",(function(e){elementorFrontend.elementsHandler.addHandler(n,{$element:e})}))}))}(jQuery,window.elementorFrontend);