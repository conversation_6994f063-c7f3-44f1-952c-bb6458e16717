!function(e,d){"use strict";var t=function(e,d){var t=e.find(".bdt-remote-thumbs"),i=t.data("settings"),n=Boolean(elementorFrontend.isEditMode());if(t.length){if(!i.remoteId){var o=e.closest(".elementor-section");i.remoteId=o}d(i.remoteId).find(".swiper").length<=0?1==n&&d(i.id+"-notice").removeClass("bdt-hidden"):(d(i.id+"-notice").addClass("bdt-hidden"),d(document).ready((function(){setTimeout((()=>{const e=d(i.remoteId).find(".swiper")[0].swiper;var t=d(i.remoteId).find(".swiper-slide-active"),n=t.data("swiper-slide-index");void 0===n&&(n=t.index()),d(i.id).find(".bdt-item:eq("+n+")").addClass("bdt-active"),d(i.id).find(".bdt-item").on("click",(function(){var t=d(this).data("index");i.loopStatus?e.slideToLoop(t):e.slideTo(t),d(i.id).find(".bdt-item").removeClass("bdt-active"),d(i.id).find(".bdt-item:eq("+t+")").addClass("bdt-active"),d(i.id).addClass("wait--")})),e.on("slideChangeTransitionEnd",(function(t){d(i.id).hasClass("wait--")?d(i.id).removeClass("wait--"):(d(i.id).find(".bdt-item").removeClass("bdt-active"),d(i.id).find(".bdt-item:eq("+e.realIndex+")").addClass("bdt-active"))}))}),2500)})))}};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/bdt-remote-thumbs.default",t)}))}(jQuery,window.elementorFrontend);