.ui-split-animate{opacity:0}.ui-split-animate .ui-e-animated{animation-fill-mode:both;animation-duration:var(---ui-speed);animation-delay:calc(var(---ui-delay) + var(---ui-stagger) * var(---ui-char-index))}.ui-splitby-words .ui-e-animated{animation-delay:calc(var(---ui-delay) + var(---ui-stagger) * var(---ui-word-index))}.ui-splitby-lines .ui-e-animated{animation-delay:calc(var(---ui-delay) + var(---ui-stagger) * var(---ui-line-index))}.ui-split-animate span:not(.whitespace):not(.ui-e-headline-text){display:inline-block}.fadeInUpAlt{animation-name:rotateInUpLeft,uicoreFadeIn!important;animation-timing-function:cubic-bezier(.22,1,.36,1),cubic-bezier(0,0,.2,1)}@keyframes scaleIn{0%{transform:translate(0,80%) scale(.7,1.8)}100%{transform:translate(0,0) scale(1,1)}}.scaleIn{transform-origin:50% 0;animation-name:scaleIn,uicoreFadeIn!important;animation-timing-function:cubic-bezier(.25,.46,.45,.94),cubic-bezier(0,0,.2,1)}.splitting.ui-e-fadeInUpAlt{transform:rotate(4deg);transition:0s!important;transition-delay:0s}.splitting{display:inline-block;transition:transform calc(var(---ui-speed) * var(---ui-word-total)/ 10) ease-out;transition-delay:calc(var(---ui-delay) * .7);transform-origin:top left}.splitting.lines{transition:transform calc(var(---ui-speed) * var(---ui-line-total)/ 3) cubic-bezier(.22,1,.36,1)}.ui-e-highlight-image>*{vertical-align:middle;height:calc(1em * var(--ui-e-asset-size,1));font-size:calc(1em * var(--ui-e-asset-size,1));width:auto}.ui-e-cut{overflow:hidden}.blur.fadeInUp{animation-name:uicoreFadeInUp,uicoreFadeIn,blurIn!important;animation-timing-function:cubic-bezier(.24,.74,.39,.96),ease-out,cubic-bezier(0,0,.15,1.01)}