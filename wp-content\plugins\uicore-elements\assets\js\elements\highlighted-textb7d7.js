"use strict";jQuery(document).ready(function(){document.querySelectorAll(".elementor-widget-highlighted-text.ui-e-a-animate .ui-e--highlighted-text").forEach(function(e,t){var n=e.querySelectorAll(".uicore-svg-wrapper path"),i=e.getAttribute("data-delay")||0;n.forEach(function(t,n){var r=new IntersectionObserver(function(e){e.forEach(function(e){e.isIntersecting&&(e=300*n+400+parseInt(i),setTimeout(function(){t.style.animationPlayState="running"},e),r.unobserve(t))})},{threshold:.9});r.observe(t)})})});