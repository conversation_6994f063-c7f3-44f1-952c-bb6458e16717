!function(t,i){"use strict";var e=function(t,i){var e=(y=t.find(".bdt-switchers")).data("settings"),n=y.data("activator"),d=y.data("bdt-link-widget"),c=Boolean(elementorFrontend.isEditMode());if(n){const g=`#bdt-switcher-activator-${n.id}`,f=`#bdt-switcher-${n.id}`;function k(t){if(bdtUIkit.switcher(g).show(t),bdtUIkit.switcher(f).show(t),d){const e=0===t;i(d.linkWidgetTargetA).css({opacity:e?1:0,display:e?"block":"none"}),i(d.linkWidgetTargetB).css({opacity:e?0:1,display:e?"none":"block"})}}bdtUIkit.util.on(n.switchA,"click",(()=>k(0))),bdtUIkit.util.on(n.switchB,"click",(()=>k(1)))}if(void 0!==e&&!1===c){var s=y.find(".bdt-switcher > div > div > .bdt-switcher-item-a"),o=y.find(".bdt-switcher > div > div > .bdt-switcher-item-b"),a=i(".elementor").find(".elementor-element#"+e["switch-a-content"]),r=i(".elementor").find(".elementor-element#"+e["switch-b-content"]);if(!0!==e.positionUnchanged&&(s.length&&a.length&&i(a).appendTo(s),o.length&&r.length&&i(r).appendTo(o)),1==e.positionUnchanged){i("#bdt-tabs-"+e.id).find(".bdt-switcher").remove();var l=i("#"+e["switch-a-content"]),w=i("#"+e["switch-b-content"]);i("#"+e["switch-a-content"]).parent().append(`<div id="bdt-switcher-${e.id}" class="bdt-switcher bdt-switcher-item-content" style="width:100%;"></div>`),i(l).appendTo(i("#bdt-switcher-"+e.id)),i(w).appendTo(i("#bdt-switcher-"+e.id));var h,b="";"a"==e.defaultActive?h="bdt-active":b="bdt-active",i("#"+e["switch-a-content"]).wrapAll('<div class="bdt-switcher-item-content-inner '+h+'"></div>'),i("#"+e["switch-b-content"]).wrapAll('<div class="bdt-switcher-item-content-inner '+b+'"></div>')}}if(void 0!==d&&!1===c){var p=i(d.linkWidgetTargetA),v=i(d.linkWidgetTargetB),y="#bdt-switcher-"+d.id;"a"==d.defaultActive?(p.css({opacity:1,display:"block"}),v.css({opacity:0,display:"none"})):(p.css({opacity:0,display:"none"}),v.css({opacity:1,display:"block"})),p.css({"grid-row-start":1,"grid-column-start":1}),v.css({"grid-row-start":1,"grid-column-start":1}),p.parent().css({display:"grid"}),bdtUIkit.util.on(y,"shown",(function(t){0==bdtUIkit.util.index(t.target)?(p.css({opacity:1,display:"block"}),v.css({opacity:0,display:"none"})):(v.css({opacity:1,display:"block"}),p.css({opacity:0,display:"none"}))}))}};jQuery(window).on("elementor/frontend/init",(function(){elementorFrontend.hooks.addAction("frontend/element_ready/bdt-switcher.default",e)}))}(jQuery,window.elementorFrontend);